/**
 * Pre-Auth Onboarding Page
 * Pagina di scelta del tipo di account prima dell'autenticazione
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import { 
  TruckIcon, 
  BuildingStorefrontIcon, 
  ArrowRightIcon,
  CheckIcon,
  ArrowLeftIcon
} from '@heroicons/react/24/outline'

const Onboarding = () => {
  const navigate = useNavigate()
  const [selectedType, setSelectedType] = useState(null)

  const handleContinue = (e) => {
    e.preventDefault() // Previeni il comportamento di default
    console.log('🔄 Onboarding - handleContinue called with selectedType:', selectedType)

    if (!selectedType) {
      console.log('❌ Onboarding - No selectedType, returning')
      return
    }

    // Salva il tipo scelto nel localStorage per usarlo dopo la registrazione
    localStorage.setItem('pendingUserType', selectedType)
    console.log('✅ Onboarding - Saved to localStorage:', selectedType)

    // Reindirizza alla registrazione Clerk con il tipo come parametro
    const targetUrl = `/sign-up?type=${selectedType}`
    console.log('🔄 Onboarding - Navigating to:', targetUrl)
    navigate(targetUrl)
  }

  const userTypes = [
    {
      type: 'ESERCENTE',
      title: 'Sono un Esercente',
      description: 'Ho un\'attività commerciale e ho bisogno di rider per consegne e servizi',
      icon: BuildingStorefrontIcon,
      color: 'bemyrider',
      features: [
        'Trova rider qualificati nella tua zona',
        'Gestisci turni e orari di lavoro',
        'Conformità normativa automatica',
        'Analytics e reportistica dettagliate',
        'Pagamenti sicuri tramite Stripe'
      ]
    },
    {
      type: 'RIDER',
      title: 'Sono un Rider',
      description: 'Voglio offrire i miei servizi di consegna e trasporto per guadagnare',
      icon: TruckIcon,
      color: 'bemyrider',
      features: [
        'Turni flessibili di 1-2 ore',
        'Guadagna fino a €12.50/ora',
        'Gestisci la tua disponibilità',
        'Pagamenti settimanali sicuri',
        'Ricevute automatiche per prestazioni occasionali'
      ]
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <button
                onClick={() => navigate('/')}
                className="flex items-center text-gray-600 hover:text-bemyrider-600 transition-colors duration-200 mr-6"
              >
                <ArrowLeftIcon className="h-5 w-5 mr-2" />
                Torna alla home
              </button>
              <img
                src="/bemyrider-icona-trasp.svg"
                alt="bemyrider"
                className="h-8 w-8 mr-3"
              />
              <h1 className="bemyrider-logo text-2xl">bemyrider</h1>
            </div>
            <button
              onClick={() => navigate('/sign-in')}
              className="text-bemyrider-600 hover:text-bemyrider-700 font-medium transition-colors duration-200"
            >
              Hai già un account? Accedi
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Scegli il tuo tipo di account
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Seleziona il tipo di account che meglio descrive la tua attività. 
            Ricorda che gli account non sono interscambiabili: dovrai utilizzare 
            email diverse per tipi di account diversi.
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 gap-8 mb-12">
          {userTypes.map((userType, index) => {
            const Icon = userType.icon
            const isSelected = selectedType === userType.type

            return (
              <motion.div
                key={userType.type}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                className={`bg-white rounded-2xl shadow-xl p-8 border-2 transition-all duration-300 cursor-pointer ${
                  isSelected 
                    ? 'border-bemyrider-500 ring-4 ring-bemyrider-200 scale-105' 
                    : 'border-transparent hover:border-bemyrider-200 hover:shadow-2xl'
                }`}
                onClick={() => setSelectedType(userType.type)}
              >
                <div className="text-center">
                  <div className={`bg-bemyrider-100 rounded-full p-4 w-16 h-16 mx-auto mb-6 ${
                    isSelected ? 'bg-bemyrider-500' : ''
                  }`}>
                    <Icon className={`w-8 h-8 ${
                      isSelected ? 'text-white' : 'text-bemyrider-600'
                    }`} />
                  </div>
                  
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">
                    {userType.title}
                  </h3>
                  
                  <p className="text-gray-600 mb-8">
                    {userType.description}
                  </p>

                  <div className="text-left space-y-3 mb-8">
                    {userType.features.map((feature, i) => (
                      <div key={i} className="flex items-start space-x-3">
                        <CheckIcon className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                        <span className="text-sm text-gray-700">{feature}</span>
                      </div>
                    ))}
                  </div>

                  {isSelected && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.3 }}
                      className="flex items-center justify-center"
                    >
                      <div className="w-8 h-8 bg-bemyrider-500 rounded-full flex items-center justify-center">
                        <CheckIcon className="w-5 h-5 text-white" />
                      </div>
                    </motion.div>
                  )}
                </div>
              </motion.div>
            )
          })}
        </div>

        {/* Continue Button */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="text-center"
        >
          <button
            type="button"
            onClick={handleContinue}
            disabled={!selectedType}
            className={`px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-200 flex items-center justify-center mx-auto ${
              selectedType
                ? 'bg-bemyrider-600 text-white hover:bg-bemyrider-700 shadow-lg hover:shadow-xl'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }`}
          >
            Continua con la registrazione
            <ArrowRightIcon className="w-5 h-5 ml-2" />
          </button>
          
          {selectedType && (
            <motion.p
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-sm text-gray-600 mt-4"
            >
              Procederai alla registrazione come <strong>{selectedType}</strong>
            </motion.p>
          )}
        </motion.div>

        {/* Info Box */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="mt-16 bg-blue-50 border border-blue-200 rounded-lg p-6 max-w-4xl mx-auto"
        >
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-bold">!</span>
              </div>
            </div>
            <div>
              <h4 className="font-semibold text-blue-900 mb-2">Importante da sapere</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Gli account ESERCENTE e RIDER sono completamente separati</li>
                <li>• Non puoi cambiare tipo di account dopo la registrazione</li>
                <li>• Per registrarti con un tipo diverso dovrai usare un'email diversa</li>
                <li>• Tutti i dati e le funzionalità sono specifici per il tipo di account scelto</li>
              </ul>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  )
}

export default Onboarding
