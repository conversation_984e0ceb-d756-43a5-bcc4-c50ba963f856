/**
 * Load Testing Suite
 * Performance tests for BeMyRider API
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import { createTestApp } from '../helpers/test-app.js'
import { PerformanceTester } from '../helpers/performance-tester.js'

describe('Load Testing', () => {
  let app
  let performanceTester
  let authHeaders

  beforeAll(async () => {
    app = await createTestApp()
    performanceTester = new PerformanceTester(app)
    authHeaders = global.createAuthHeaders()

    // Setup test data
    const testUsers = Array(100).fill().map((_, i) => 
      global.createTestUser({
        clerk_id: `test_user_${i}`,
        email: `user${i}@example.com`,
        type: i % 3 === 0 ? 'RIDER' : 'ESERCENTE',
        city: ['Milano', 'Roma', 'Napoli'][i % 3],
        is_available: i % 2 === 0
      })
    )

    testUsers.forEach(user => global.testDb.addUser(user))
  })

  afterAll(async () => {
    if (global.testDb) {
      global.testDb.clear()
    }
  })

  describe('API Response Time Tests', () => {
    it('should handle health check requests under 50ms', async () => {
      const results = await performanceTester.loadTest({
        endpoint: '/api/health',
        method: 'GET',
        concurrency: 10,
        requests: 100,
        maxResponseTime: 50
      })

      expect(results.averageResponseTime).toBeLessThan(50)
      expect(results.successRate).toBeGreaterThan(0.95) // 95% success rate
      expect(results.errors.length).toBe(0)
    })

    it('should handle user profile requests under 200ms', async () => {
      const results = await performanceTester.loadTest({
        endpoint: '/api/users/profile/test_user_0',
        method: 'GET',
        headers: authHeaders,
        concurrency: 5,
        requests: 50,
        maxResponseTime: 200
      })

      expect(results.averageResponseTime).toBeLessThan(200)
      expect(results.successRate).toBeGreaterThan(0.90)
      expect(results.p95ResponseTime).toBeLessThan(300)
    })

    it('should handle rider search requests under 300ms', async () => {
      const results = await performanceTester.loadTest({
        endpoint: '/api/users/riders?city=Milano&available=true',
        method: 'GET',
        concurrency: 8,
        requests: 80,
        maxResponseTime: 300
      })

      expect(results.averageResponseTime).toBeLessThan(300)
      expect(results.successRate).toBeGreaterThan(0.90)
      expect(results.maxResponseTime).toBeLessThan(1000)
    })

    it('should handle user sync requests under 500ms', async () => {
      const testData = {
        clerkId: 'test_user_0',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        name: 'Test User',
        userType: 'RIDER',
        profileComplete: true
      }

      const results = await performanceTester.loadTest({
        endpoint: '/api/users/sync',
        method: 'POST',
        headers: authHeaders,
        body: testData,
        concurrency: 3,
        requests: 30,
        maxResponseTime: 500
      })

      expect(results.averageResponseTime).toBeLessThan(500)
      expect(results.successRate).toBeGreaterThan(0.85)
    })
  })

  describe('Concurrency Tests', () => {
    it('should handle high concurrency on read operations', async () => {
      const results = await performanceTester.concurrencyTest({
        endpoint: '/api/users/riders',
        method: 'GET',
        maxConcurrency: 50,
        duration: 10000, // 10 seconds
        rampUpTime: 2000 // 2 seconds ramp up
      })

      expect(results.averageResponseTime).toBeLessThan(500)
      expect(results.successRate).toBeGreaterThan(0.90)
      expect(results.throughput).toBeGreaterThan(10) // requests per second
    })

    it('should handle moderate concurrency on write operations', async () => {
      const testData = {
        firstName: 'Updated',
        lastName: 'Name',
        city: 'Milano'
      }

      const results = await performanceTester.concurrencyTest({
        endpoint: '/api/users/profile/test_user_0',
        method: 'PUT',
        headers: authHeaders,
        body: testData,
        maxConcurrency: 10,
        duration: 5000,
        rampUpTime: 1000
      })

      expect(results.averageResponseTime).toBeLessThan(1000)
      expect(results.successRate).toBeGreaterThan(0.80)
    })
  })

  describe('Stress Tests', () => {
    it('should maintain performance under stress', async () => {
      const results = await performanceTester.stressTest({
        endpoint: '/api/health',
        method: 'GET',
        initialLoad: 10,
        maxLoad: 100,
        stepSize: 10,
        stepDuration: 5000,
        acceptableErrorRate: 0.05 // 5% error rate acceptable
      })

      expect(results.breakingPoint).toBeGreaterThan(50)
      expect(results.maxThroughput).toBeGreaterThan(20)
    })

    it('should handle memory pressure gracefully', async () => {
      const initialMemory = process.memoryUsage()

      const results = await performanceTester.loadTest({
        endpoint: '/api/users/riders',
        method: 'GET',
        concurrency: 20,
        requests: 200,
        maxResponseTime: 1000
      })

      const finalMemory = process.memoryUsage()
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed

      // Memory increase should be reasonable (less than 50MB)
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024)
      expect(results.successRate).toBeGreaterThan(0.85)
    })
  })

  describe('Rate Limiting Tests', () => {
    it('should enforce rate limits correctly', async () => {
      const results = await performanceTester.rateLimitTest({
        endpoint: '/api/users/sync',
        method: 'POST',
        headers: authHeaders,
        body: {
          clerkId: 'test_user_0',
          email: '<EMAIL>',
          userType: 'RIDER'
        },
        requestsPerSecond: 20, // Above rate limit
        duration: 5000
      })

      expect(results.rateLimitedRequests).toBeGreaterThan(0)
      expect(results.rateLimitedRequests / results.totalRequests).toBeGreaterThan(0.1)
    })

    it('should allow requests within rate limits', async () => {
      const results = await performanceTester.rateLimitTest({
        endpoint: '/api/users/riders',
        method: 'GET',
        requestsPerSecond: 5, // Within rate limit
        duration: 3000
      })

      expect(results.rateLimitedRequests).toBe(0)
      expect(results.successRate).toBe(1.0)
    })
  })

  describe('Database Performance Tests', () => {
    it('should handle large dataset queries efficiently', async () => {
      // Add more test data
      const largeDataset = Array(1000).fill().map((_, i) => 
        global.createTestUser({
          clerk_id: `large_user_${i}`,
          email: `large${i}@example.com`,
          type: 'RIDER',
          city: ['Milano', 'Roma', 'Napoli', 'Torino', 'Palermo'][i % 5]
        })
      )

      largeDataset.forEach(user => global.testDb.addUser(user))

      const results = await performanceTester.loadTest({
        endpoint: '/api/users/riders?limit=100',
        method: 'GET',
        concurrency: 5,
        requests: 25,
        maxResponseTime: 500
      })

      expect(results.averageResponseTime).toBeLessThan(500)
      expect(results.successRate).toBeGreaterThan(0.90)

      // Cleanup
      largeDataset.forEach(user => global.testDb.users.delete(user.id))
    })

    it('should handle complex filter queries efficiently', async () => {
      const complexQuery = '/api/users/riders?city=Milano&vehicleType=MOTO&available=true&minRate=20&maxRate=40'

      const results = await performanceTester.loadTest({
        endpoint: complexQuery,
        method: 'GET',
        concurrency: 10,
        requests: 50,
        maxResponseTime: 400
      })

      expect(results.averageResponseTime).toBeLessThan(400)
      expect(results.successRate).toBeGreaterThan(0.90)
    })
  })

  describe('Error Recovery Tests', () => {
    it('should recover from temporary failures', async () => {
      let failureCount = 0
      const originalGetRiders = global.testDb.getRiders

      // Inject temporary failures
      global.testDb.getRiders = async (filters) => {
        failureCount++
        if (failureCount <= 5) {
          throw new Error('Temporary database error')
        }
        return originalGetRiders.call(global.testDb, filters)
      }

      const results = await performanceTester.loadTest({
        endpoint: '/api/users/riders',
        method: 'GET',
        concurrency: 2,
        requests: 20,
        maxResponseTime: 1000,
        retryOnFailure: true
      })

      // Should eventually succeed after failures
      expect(results.successRate).toBeGreaterThan(0.70)

      // Restore original function
      global.testDb.getRiders = originalGetRiders
    })

    it('should handle cascading failures gracefully', async () => {
      // Simulate database unavailability
      const originalDb = global.testDb
      global.testDb = null

      const results = await performanceTester.loadTest({
        endpoint: '/api/users/riders',
        method: 'GET',
        concurrency: 5,
        requests: 25,
        maxResponseTime: 2000
      })

      // Should fail gracefully with proper error responses
      expect(results.successRate).toBe(0)
      expect(results.averageResponseTime).toBeLessThan(1000) // Fast failures

      // Restore database
      global.testDb = originalDb
    })
  })

  describe('Performance Regression Tests', () => {
    it('should maintain baseline performance metrics', async () => {
      const baseline = {
        healthCheck: { maxTime: 50, minThroughput: 100 },
        userProfile: { maxTime: 200, minThroughput: 20 },
        riderSearch: { maxTime: 300, minThroughput: 15 }
      }

      // Test health check performance
      const healthResults = await performanceTester.loadTest({
        endpoint: '/api/health',
        method: 'GET',
        concurrency: 10,
        requests: 100
      })

      expect(healthResults.averageResponseTime).toBeLessThan(baseline.healthCheck.maxTime)
      expect(healthResults.throughput).toBeGreaterThan(baseline.healthCheck.minThroughput)

      // Test user profile performance
      const profileResults = await performanceTester.loadTest({
        endpoint: '/api/users/profile/test_user_0',
        method: 'GET',
        headers: authHeaders,
        concurrency: 5,
        requests: 50
      })

      expect(profileResults.averageResponseTime).toBeLessThan(baseline.userProfile.maxTime)
      expect(profileResults.throughput).toBeGreaterThan(baseline.userProfile.minThroughput)

      // Test rider search performance
      const searchResults = await performanceTester.loadTest({
        endpoint: '/api/users/riders',
        method: 'GET',
        concurrency: 8,
        requests: 80
      })

      expect(searchResults.averageResponseTime).toBeLessThan(baseline.riderSearch.maxTime)
      expect(searchResults.throughput).toBeGreaterThan(baseline.riderSearch.minThroughput)
    })
  })
})
