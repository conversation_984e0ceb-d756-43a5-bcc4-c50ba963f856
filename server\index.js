import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import { validateEnvironment, getEnv, getSafeConfig } from './utils/env-validator.js'

// Security and monitoring imports
import {
  securityHeaders,
  generalRateLimit,
  sanitizeInput,
  securityMonitoring,
  requestSizeLimit
} from './middleware/security.js'
import { requestLoggingMiddleware, logger } from './utils/logger.js'
import { metricsMiddleware } from './utils/metrics.js'
import { complianceMiddleware } from './utils/compliance.js'

// Validate environment variables first
try {
  validateEnvironment()
  logger.info('Environment validation passed')
} catch (error) {
  logger.error('Environment validation failed', { error: error.message })
  process.exit(1)
}

const app = express()
const PORT = parseInt(getEnv('PORT', '5000'), 10)

logger.info('Server starting with validated configuration', getSafeConfig())

// Security middleware - Applied in order of importance
app.use(requestLoggingMiddleware)
app.use(metricsMiddleware)
app.use(complianceMiddleware)
app.use(requestSizeLimit)
app.use(generalRateLimit)
app.use(securityMonitoring)
app.use(sanitizeInput)

// Enhanced security headers
app.use(securityHeaders)

// Legacy helmet configuration (keeping for compatibility)
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'", "https://api.stripe.com", "https://*.supabase.co", "https://*.clerk.accounts.dev"]
    }
  },
  crossOriginEmbedderPolicy: false // Allow embedding for Stripe
}))
const corsOptions = {
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true)

    const allowedOrigins = [
      'http://localhost:3000',  // Development frontend
      'http://localhost:5173',  // Vite dev server
      getEnv('FRONTEND_URL'),   // Production frontend
    ].filter(Boolean) // Remove undefined values

    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true)
    } else {
      console.warn(`🚫 CORS blocked request from origin: ${origin}`)
      callback(new Error('Not allowed by CORS'))
    }
  },
  credentials: true,
  optionsSuccessStatus: 200 // Some legacy browsers choke on 204
}

app.use(cors(corsOptions))

// Body parsing middleware with size limits
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))

// Routes
import usersRoutes from './routes/users.js'
import ridersRoutes from './routes/riders.js'
import riderOnboardingRoutes from './routes/rider-onboarding.js'
import availabilityRoutes from './routes/availability.js'
import matchingRoutes from './routes/matching.js'
import fleetManagementRoutes from './routes/fleet-management.js'
import bookingsRoutes from './routes/bookings.js'
import paymentsRoutes from './routes/payments.js'
import dashboardRoutes from './routes/dashboard.js'
import migrateRoutes from './routes/migrate.js'
import monitoringRoutes from './routes/monitoring.js'

app.use('/api/users', usersRoutes)
app.use('/api/riders', ridersRoutes)
app.use('/api/rider-onboarding', riderOnboardingRoutes)
app.use('/api/availability', availabilityRoutes)
app.use('/api/matching', matchingRoutes)
app.use('/api/fleet-management', fleetManagementRoutes)
app.use('/api/bookings', bookingsRoutes)
app.use('/api/payments', paymentsRoutes)
app.use('/api/dashboard', dashboardRoutes)
app.use('/api/migrate', migrateRoutes)
app.use('/api/monitoring', monitoringRoutes)

// Health check
app.get('/api/health', (_, res) => {
  res.json({ status: 'OK', message: 'BeMyRider API is running' })
})

// Error handling middleware
app.use((err, req, res, _) => {
  logger.error('Application error', {
    error: {
      name: err.name,
      message: err.message,
      stack: err.stack
    },
    request: {
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      userAgent: req.headers['user-agent']
    }
  })

  res.status(500).json({
    success: false,
    message: 'Qualcosa è andato storto!',
    error: process.env.NODE_ENV === 'development' ? err.message : undefined
  })
})

// 404 handler
app.use('*', (_, res) => {
  res.status(404).json({
    success: false,
    message: 'Endpoint non trovato'
  })
})

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('Shutting down gracefully...')
  console.log('✅ Server stopped')
  process.exit(0)
})

app.listen(PORT, () => {
  logger.info('BeMyRider API server started', {
    port: PORT,
    environment: process.env.NODE_ENV || 'development',
    healthCheck: `http://localhost:${PORT}/api/health`,
    apiBaseUrl: `http://localhost:${PORT}/api`,
    monitoringUrl: `http://localhost:${PORT}/api/monitoring`,
    metricsUrl: `http://localhost:${PORT}/api/monitoring/metrics`
  })

  console.log(`🚀 BeMyRider API server running on port ${PORT}`)
  console.log(`� Environment: ${process.env.NODE_ENV || 'development'}`)
  console.log(`🔗 Health check: http://localhost:${PORT}/api/health`)
  console.log(`📈 Metrics: http://localhost:${PORT}/api/monitoring/metrics`)
  console.log(`🔒 Security dashboard: http://localhost:${PORT}/api/monitoring/security/dashboard`)
})
