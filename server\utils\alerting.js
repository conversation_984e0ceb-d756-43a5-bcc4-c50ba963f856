/**
 * Alerting and Incident Response System
 * Enterprise-grade alerting for BeMyRider
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { securityLogger, auditLogger, logError } from './logger.js'
import { recordSecurityEvent, recordError } from './metrics.js'
import { getEnv } from './env-validator.js'

// ============================================================================
// ALERT CONFIGURATION
// ============================================================================

const ALERT_LEVELS = {
  INFO: 'info',
  WARNING: 'warning',
  ERROR: 'error',
  CRITICAL: 'critical'
}

const ALERT_CHANNELS = {
  EMAIL: 'email',
  SLACK: 'slack',
  SMS: 'sms',
  WEBHOOK: 'webhook',
  LOG: 'log'
}

const ALERT_RULES = {
  // Security alerts
  FAILED_LOGIN_ATTEMPTS: {
    threshold: 5,
    window: 300000, // 5 minutes
    level: ALERT_LEVELS.WARNING,
    channels: [ALERT_CHANNELS.EMAIL, ALERT_CHANNELS.SLACK]
  },
  
  SUSPICIOUS_ACTIVITY: {
    threshold: 1,
    window: 0,
    level: ALERT_LEVELS.ERROR,
    channels: [ALERT_CHANNELS.EMAIL, ALERT_CHANNELS.SLACK, ALERT_CHANNELS.SMS]
  },
  
  // Performance alerts
  HIGH_RESPONSE_TIME: {
    threshold: 5000, // 5 seconds
    window: 60000, // 1 minute
    level: ALERT_LEVELS.WARNING,
    channels: [ALERT_CHANNELS.SLACK]
  },
  
  HIGH_ERROR_RATE: {
    threshold: 10, // 10 errors
    window: 300000, // 5 minutes
    level: ALERT_LEVELS.ERROR,
    channels: [ALERT_CHANNELS.EMAIL, ALERT_CHANNELS.SLACK]
  },
  
  // System alerts
  HIGH_MEMORY_USAGE: {
    threshold: 90, // 90%
    window: 0,
    level: ALERT_LEVELS.WARNING,
    channels: [ALERT_CHANNELS.SLACK]
  },
  
  DATABASE_CONNECTION_FAILURE: {
    threshold: 1,
    window: 0,
    level: ALERT_LEVELS.CRITICAL,
    channels: [ALERT_CHANNELS.EMAIL, ALERT_CHANNELS.SLACK, ALERT_CHANNELS.SMS]
  },
  
  // Business alerts
  PAYMENT_FAILURE_SPIKE: {
    threshold: 5,
    window: 600000, // 10 minutes
    level: ALERT_LEVELS.ERROR,
    channels: [ALERT_CHANNELS.EMAIL, ALERT_CHANNELS.SLACK]
  }
}

// ============================================================================
// ALERT TRACKING
// ============================================================================

class AlertTracker {
  constructor() {
    this.events = new Map()
    this.sentAlerts = new Map()
    this.cooldownPeriod = 300000 // 5 minutes cooldown
  }

  /**
   * Track an event for alerting
   */
  trackEvent(eventType, data = {}) {
    const now = Date.now()
    const rule = ALERT_RULES[eventType]
    
    if (!rule) {
      return false
    }

    // Initialize event tracking if not exists
    if (!this.events.has(eventType)) {
      this.events.set(eventType, [])
    }

    const events = this.events.get(eventType)
    
    // Add current event
    events.push({ timestamp: now, data })
    
    // Clean old events outside the window
    if (rule.window > 0) {
      const cutoff = now - rule.window
      this.events.set(eventType, events.filter(event => event.timestamp > cutoff))
    }

    // Check if threshold is exceeded
    const currentEvents = this.events.get(eventType)
    if (currentEvents.length >= rule.threshold) {
      return this.shouldSendAlert(eventType, rule, currentEvents)
    }

    return false
  }

  /**
   * Check if alert should be sent (considering cooldown)
   */
  shouldSendAlert(eventType, rule, events) {
    const now = Date.now()
    const lastAlert = this.sentAlerts.get(eventType)
    
    // Check cooldown period
    if (lastAlert && (now - lastAlert) < this.cooldownPeriod) {
      return false
    }

    // Mark alert as sent
    this.sentAlerts.set(eventType, now)
    
    return true
  }

  /**
   * Reset event tracking for a specific type
   */
  resetEvents(eventType) {
    this.events.delete(eventType)
    this.sentAlerts.delete(eventType)
  }

  /**
   * Get current event counts
   */
  getEventCounts() {
    const counts = {}
    for (const [eventType, events] of this.events.entries()) {
      counts[eventType] = events.length
    }
    return counts
  }
}

const alertTracker = new AlertTracker()

// ============================================================================
// ALERT SENDERS
// ============================================================================

/**
 * Email alert sender
 */
class EmailAlertSender {
  async send(alert) {
    try {
      // In production, integrate with email service (SendGrid, SES, etc.)
      console.log('📧 EMAIL ALERT:', JSON.stringify(alert, null, 2))
      
      // Mock email sending
      if (getEnv('NODE_ENV') === 'production') {
        // await emailService.send({
        //   to: getEnv('ALERT_EMAIL'),
        //   subject: `[${alert.level.toUpperCase()}] ${alert.title}`,
        //   html: this.formatEmailAlert(alert)
        // })
      }
      
      return { success: true, channel: 'email' }
    } catch (error) {
      logError(error, { context: 'email_alert_sender' })
      return { success: false, channel: 'email', error: error.message }
    }
  }

  formatEmailAlert(alert) {
    return `
      <h2>🚨 BeMyRider Alert</h2>
      <p><strong>Level:</strong> ${alert.level.toUpperCase()}</p>
      <p><strong>Title:</strong> ${alert.title}</p>
      <p><strong>Description:</strong> ${alert.description}</p>
      <p><strong>Time:</strong> ${alert.timestamp}</p>
      <p><strong>Environment:</strong> ${alert.environment}</p>
      ${alert.data ? `<p><strong>Data:</strong> <pre>${JSON.stringify(alert.data, null, 2)}</pre></p>` : ''}
    `
  }
}

/**
 * Slack alert sender
 */
class SlackAlertSender {
  async send(alert) {
    try {
      // In production, integrate with Slack webhook
      console.log('💬 SLACK ALERT:', JSON.stringify(alert, null, 2))
      
      if (getEnv('SLACK_WEBHOOK_URL')) {
        // await fetch(getEnv('SLACK_WEBHOOK_URL'), {
        //   method: 'POST',
        //   headers: { 'Content-Type': 'application/json' },
        //   body: JSON.stringify(this.formatSlackAlert(alert))
        // })
      }
      
      return { success: true, channel: 'slack' }
    } catch (error) {
      logError(error, { context: 'slack_alert_sender' })
      return { success: false, channel: 'slack', error: error.message }
    }
  }

  formatSlackAlert(alert) {
    const emoji = this.getLevelEmoji(alert.level)
    
    return {
      text: `${emoji} BeMyRider Alert`,
      attachments: [{
        color: this.getLevelColor(alert.level),
        fields: [
          { title: 'Level', value: alert.level.toUpperCase(), short: true },
          { title: 'Environment', value: alert.environment, short: true },
          { title: 'Title', value: alert.title, short: false },
          { title: 'Description', value: alert.description, short: false },
          { title: 'Time', value: alert.timestamp, short: true }
        ]
      }]
    }
  }

  getLevelEmoji(level) {
    const emojis = {
      info: 'ℹ️',
      warning: '⚠️',
      error: '❌',
      critical: '🚨'
    }
    return emojis[level] || '📢'
  }

  getLevelColor(level) {
    const colors = {
      info: '#36a64f',
      warning: '#ff9500',
      error: '#ff0000',
      critical: '#8b0000'
    }
    return colors[level] || '#cccccc'
  }
}

/**
 * SMS alert sender
 */
class SMSAlertSender {
  async send(alert) {
    try {
      // In production, integrate with SMS service (Twilio, AWS SNS, etc.)
      console.log('📱 SMS ALERT:', JSON.stringify(alert, null, 2))
      
      if (getEnv('SMS_ENABLED') === 'true') {
        // await smsService.send({
        //   to: getEnv('ALERT_PHONE'),
        //   message: this.formatSMSAlert(alert)
        // })
      }
      
      return { success: true, channel: 'sms' }
    } catch (error) {
      logError(error, { context: 'sms_alert_sender' })
      return { success: false, channel: 'sms', error: error.message }
    }
  }

  formatSMSAlert(alert) {
    return `🚨 BeMyRider ${alert.level.toUpperCase()}: ${alert.title} - ${alert.description} (${alert.timestamp})`
  }
}

/**
 * Webhook alert sender
 */
class WebhookAlertSender {
  async send(alert) {
    try {
      console.log('🔗 WEBHOOK ALERT:', JSON.stringify(alert, null, 2))
      
      if (getEnv('ALERT_WEBHOOK_URL')) {
        // await fetch(getEnv('ALERT_WEBHOOK_URL'), {
        //   method: 'POST',
        //   headers: { 'Content-Type': 'application/json' },
        //   body: JSON.stringify(alert)
        // })
      }
      
      return { success: true, channel: 'webhook' }
    } catch (error) {
      logError(error, { context: 'webhook_alert_sender' })
      return { success: false, channel: 'webhook', error: error.message }
    }
  }
}

/**
 * Log alert sender
 */
class LogAlertSender {
  async send(alert) {
    try {
      securityLogger.warn('ALERT TRIGGERED', alert)
      return { success: true, channel: 'log' }
    } catch (error) {
      return { success: false, channel: 'log', error: error.message }
    }
  }
}

// ============================================================================
// ALERT MANAGER
// ============================================================================

class AlertManager {
  constructor() {
    this.senders = {
      [ALERT_CHANNELS.EMAIL]: new EmailAlertSender(),
      [ALERT_CHANNELS.SLACK]: new SlackAlertSender(),
      [ALERT_CHANNELS.SMS]: new SMSAlertSender(),
      [ALERT_CHANNELS.WEBHOOK]: new WebhookAlertSender(),
      [ALERT_CHANNELS.LOG]: new LogAlertSender()
    }
  }

  /**
   * Send alert through specified channels
   */
  async sendAlert(eventType, title, description, data = {}, level = ALERT_LEVELS.WARNING) {
    const alert = {
      eventType,
      title,
      description,
      level,
      timestamp: new Date().toISOString(),
      environment: getEnv('NODE_ENV', 'development'),
      data
    }

    const rule = ALERT_RULES[eventType]
    const channels = rule?.channels || [ALERT_CHANNELS.LOG]

    const results = []

    for (const channel of channels) {
      const sender = this.senders[channel]
      if (sender) {
        try {
          const result = await sender.send(alert)
          results.push(result)
        } catch (error) {
          results.push({ success: false, channel, error: error.message })
        }
      }
    }

    // Log alert sending results
    auditLogger.info('Alert Sent', {
      eventType,
      title,
      level,
      channels,
      results
    })

    return results
  }

  /**
   * Check and send alert if threshold is met
   */
  async checkAndAlert(eventType, title, description, data = {}) {
    const shouldAlert = alertTracker.trackEvent(eventType, data)
    
    if (shouldAlert) {
      const rule = ALERT_RULES[eventType]
      await this.sendAlert(eventType, title, description, data, rule.level)
      
      // Record security event if it's a security alert
      if (eventType.includes('SECURITY') || eventType.includes('FAILED_LOGIN') || eventType.includes('SUSPICIOUS')) {
        recordSecurityEvent(eventType, rule.level)
      }
      
      return true
    }
    
    return false
  }

  /**
   * Get alert statistics
   */
  getAlertStats() {
    return {
      eventCounts: alertTracker.getEventCounts(),
      rules: ALERT_RULES,
      uptime: process.uptime()
    }
  }
}

const alertManager = new AlertManager()

// ============================================================================
// CONVENIENCE FUNCTIONS
// ============================================================================

/**
 * Alert for failed login attempts
 */
const alertFailedLogin = async (ip, userAgent, attempts) => {
  await alertManager.checkAndAlert(
    'FAILED_LOGIN_ATTEMPTS',
    'Multiple Failed Login Attempts',
    `${attempts} failed login attempts detected from IP ${ip}`,
    { ip, userAgent, attempts }
  )
}

/**
 * Alert for suspicious activity
 */
const alertSuspiciousActivity = async (type, details, req) => {
  await alertManager.sendAlert(
    'SUSPICIOUS_ACTIVITY',
    'Suspicious Activity Detected',
    `Suspicious ${type} activity detected`,
    {
      type,
      details,
      ip: req?.ip,
      userAgent: req?.headers?.['user-agent'],
      url: req?.originalUrl
    },
    ALERT_LEVELS.ERROR
  )
}

/**
 * Alert for high response time
 */
const alertHighResponseTime = async (endpoint, responseTime) => {
  await alertManager.checkAndAlert(
    'HIGH_RESPONSE_TIME',
    'High Response Time Detected',
    `Endpoint ${endpoint} responded in ${responseTime}ms`,
    { endpoint, responseTime }
  )
}

/**
 * Alert for high error rate
 */
const alertHighErrorRate = async (errorCount, timeWindow) => {
  await alertManager.checkAndAlert(
    'HIGH_ERROR_RATE',
    'High Error Rate Detected',
    `${errorCount} errors in the last ${timeWindow}ms`,
    { errorCount, timeWindow }
  )
}

/**
 * Alert for database issues
 */
const alertDatabaseIssue = async (error, operation) => {
  await alertManager.sendAlert(
    'DATABASE_CONNECTION_FAILURE',
    'Database Connection Issue',
    `Database ${operation} failed: ${error.message}`,
    { error: error.message, operation },
    ALERT_LEVELS.CRITICAL
  )
}

/**
 * Alert for payment failures
 */
const alertPaymentFailure = async (paymentId, error, amount) => {
  await alertManager.checkAndAlert(
    'PAYMENT_FAILURE_SPIKE',
    'Payment Failure Detected',
    `Payment ${paymentId} failed: ${error}`,
    { paymentId, error, amount }
  )
}

// ============================================================================
// EXPORTS
// ============================================================================

export default alertManager

export {
  alertManager,
  alertTracker,
  ALERT_LEVELS,
  ALERT_CHANNELS,
  alertFailedLogin,
  alertSuspiciousActivity,
  alertHighResponseTime,
  alertHighErrorRate,
  alertDatabaseIssue,
  alertPaymentFailure
}
