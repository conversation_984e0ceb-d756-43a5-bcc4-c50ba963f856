<!DOCTYPE html>
<html lang="it">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>BeMyRider - Marketplace per Rider e Merchant</title>
    
    <!-- Meta tags for SEO -->
    <meta name="description" content="BeMyRider - La piattaforma che connette merchant e rider per servizi di delivery e trasporto. Trova il rider perfetto per la tua attività." />
    <meta name="keywords" content="rider, delivery, trasporto, marketplace, merchant, BeMyRider" />
    <meta name="author" content="BeMyRider Team" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://bemyrider.com/" />
    <meta property="og:title" content="BeMyRider - Marketplace per Rider e Merchant" />
    <meta property="og:description" content="La piattaforma che connette merchant e rider per servizi di delivery e trasporto." />
    <meta property="og:image" content="/og-image.jpg" />
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://bemyrider.com/" />
    <meta property="twitter:title" content="BeMyRider - Marketplace per Rider e Merchant" />
    <meta property="twitter:description" content="La piattaforma che connette merchant e rider per servizi di delivery e trasporto." />
    <meta property="twitter:image" content="/og-image.jpg" />
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    
    <!-- Google Fonts - Manrope -->
    <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@200;300;400;500;600;700;800&display=swap" rel="stylesheet" />
    
    <!-- Favicon -->
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link rel="manifest" href="/site.webmanifest" />
    
    <!-- Theme color -->
    <meta name="theme-color" content="#333366" />
    
    <!-- Prevent zoom on mobile -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    
    <!-- PWA support -->
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="BeMyRider" />
    
    <!-- Loading styles -->
    <style>
      /* Loading screen */
      #loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #333366 0%, #4f46e5 100%);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        color: white;
        font-family: 'Manrope', sans-serif;
      }
      
      .loading-logo {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 2rem;
        animation: pulse 2s infinite;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-top: 3px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.7; }
      }
      
      /* Hide loading when app loads */
      .app-loaded #loading {
        display: none;
      }
    </style>
  </head>
  <body>
    <!-- Loading screen -->
    <div id="loading">
      <div class="loading-logo">BeMyRider</div>
      <div class="loading-spinner"></div>
    </div>
    
    <!-- React app root -->
    <div id="root"></div>
    
    <!-- Vite script -->
    <script type="module" src="/src/main.jsx"></script>
    
    <!-- Hide loading screen when app loads -->
    <script>
      window.addEventListener('load', function() {
        setTimeout(function() {
          document.body.classList.add('app-loaded');
        }, 500);
      });
    </script>
  </body>
</html>
