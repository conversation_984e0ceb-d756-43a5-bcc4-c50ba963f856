import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { Calendar, Clock, MapPin, CreditCard } from 'lucide-react'
import { format, addDays, startOfMonth, endOfMonth, eachDayOfInterval, isSameDay, isToday } from 'date-fns'
import { it } from 'date-fns/locale'
import axios from 'axios'
import toast from 'react-hot-toast'

interface Rider {
  id: string
  name: string
  rating: number
  hourlyRate: number
  location: string
  vehicleType: string
  vehicleModel: string
  profileImage?: string
}

const BookingPage: React.FC = () => {
  const { riderId } = useParams<{ riderId: string }>()
  const navigate = useNavigate()
  const [rider, setRider] = useState<Rider | null>(null)
  const [loading, setLoading] = useState(true)
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)
  const [duration, setDuration] = useState<number>(1)
  const [address, setAddress] = useState('')
  const [instructions, setInstructions] = useState('')
  const [showCalendar, setShowCalendar] = useState(false)
  const [currentMonth, setCurrentMonth] = useState(new Date())
  const [processing, setProcessing] = useState(false)

  useEffect(() => {
    fetchRiderDetails()
  }, [riderId])

  const fetchRiderDetails = async () => {
    try {
      setLoading(true)
      const response = await axios.get(`/api/riders/${riderId}`)
      setRider(response.data.rider)
    } catch (error) {
      console.error('Error fetching rider details:', error)
      // Mock data for development
      setRider({
        id: riderId || '1',
        name: 'Paolo cimino',
        rating: 5.0,
        hourlyRate: 12,
        location: 'Catania CT, Italia',
        vehicleType: 'MOTO',
        vehicleModel: 'TRK 502X (NON POSSIEDO LA BORSA)'
      })
    } finally {
      setLoading(false)
    }
  }

  const getDaysInMonth = () => {
    const start = startOfMonth(currentMonth)
    const end = endOfMonth(currentMonth)
    return eachDayOfInterval({ start, end })
  }

  const handleDateSelect = (date: Date) => {
    setSelectedDate(date)
    setShowCalendar(false)
  }

  const calculateTotal = () => {
    if (!rider) return 0
    const subtotal = rider.hourlyRate * duration
    const commission = subtotal * 0.15 // 15% commission
    return subtotal + commission
  }

  const handleBooking = async () => {
    if (!rider || !selectedDate || !address) {
      toast.error('Compila tutti i campi obbligatori')
      return
    }

    setProcessing(true)
    try {
      const bookingData = {
        riderId: rider.id,
        date: selectedDate.toISOString(),
        duration,
        address,
        instructions,
        totalAmount: calculateTotal()
      }

      const response = await axios.post('/api/bookings', bookingData)
      
      if (response.data.success) {
        toast.success('Prenotazione creata con successo!')
        navigate('/app/bookings')
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Errore durante la prenotazione')
    } finally {
      setProcessing(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
      </div>
    )
  }

  if (!rider) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <p className="text-gray-500">Rider non trovato</p>
      </div>
    )
  }

  return (
    <div className="bg-gray-50 min-h-screen pb-24">
      {/* Rider Info */}
      <div className="bg-white px-4 py-6 border-b">
        <div className="flex items-center space-x-4">
          <div className="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center">
            {rider.profileImage ? (
              <img
                src={rider.profileImage}
                alt={rider.name}
                className="w-12 h-12 rounded-full object-cover"
              />
            ) : (
              <span className="text-gray-600 font-medium">
                {rider.name.charAt(0).toUpperCase()}
              </span>
            )}
          </div>
          
          <div className="flex-1">
            <h1 className="text-lg font-bold text-gray-900">{rider.name}</h1>
            <div className="flex items-center space-x-1 mt-1">
              <span className="text-sm text-gray-600">⭐ {rider.rating.toFixed(1)}</span>
            </div>
          </div>
          
          <div className="text-right">
            <div className="text-sm text-gray-600">Tariffa</div>
            <div className="text-lg font-bold text-purple-600">€{rider.hourlyRate}/ora</div>
          </div>
        </div>

        <div className="mt-4 space-y-2">
          <div className="text-sm">
            <span className="font-medium">Disponibilità giornaliera:</span>
            <span className="text-gray-600 ml-2">Domenica, Lunedì, Martedì, Mercoledì, Giovedì, Venerdì, Sabato</span>
          </div>
          <div className="text-sm">
            <span className="font-medium">Disponibilità oraria:</span>
            <span className="text-gray-600 ml-2">10:00 - 23:00</span>
          </div>
          <div className="text-sm">
            <span className="font-medium">Modello del mezzo e attrezzatura:</span>
            <span className="text-gray-600 ml-2">{rider.vehicleModel}</span>
          </div>
          <div className="text-sm">
            <span className="font-medium">Tipo di Veicolo:</span>
            <span className="text-gray-600 ml-2">{rider.vehicleType}</span>
          </div>
        </div>
      </div>

      {/* Booking Form */}
      <div className="px-4 py-6 space-y-6">
        {/* Calendar */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Calendario*
          </label>
          <button
            onClick={() => setShowCalendar(!showCalendar)}
            className="w-full flex items-center justify-between px-4 py-3 border border-gray-300 rounded-lg bg-white"
          >
            <div className="flex items-center space-x-2">
              <Calendar size={20} className="text-gray-400" />
              <span className={selectedDate ? 'text-gray-900' : 'text-gray-500'}>
                {selectedDate 
                  ? format(selectedDate, 'EEEE d MMMM yyyy', { locale: it })
                  : 'Seleziona una data'
                }
              </span>
            </div>
          </button>

          {showCalendar && (
            <div className="mt-2 bg-white border border-gray-300 rounded-lg p-4">
              <div className="flex items-center justify-between mb-4">
                <button
                  onClick={() => setCurrentMonth(addDays(currentMonth, -30))}
                  className="p-2 hover:bg-gray-100 rounded"
                >
                  ←
                </button>
                <h3 className="font-semibold">
                  {format(currentMonth, 'MMMM yyyy', { locale: it })}
                </h3>
                <button
                  onClick={() => setCurrentMonth(addDays(currentMonth, 30))}
                  className="p-2 hover:bg-gray-100 rounded"
                >
                  →
                </button>
              </div>

              <div className="grid grid-cols-7 gap-1 text-center text-sm">
                {['L', 'M', 'M', 'G', 'V', 'S', 'D'].map((day) => (
                  <div key={day} className="p-2 font-medium text-gray-500">
                    {day}
                  </div>
                ))}
                
                {getDaysInMonth().map((date) => (
                  <button
                    key={date.toISOString()}
                    onClick={() => handleDateSelect(date)}
                    className={`p-2 rounded-full text-sm ${
                      selectedDate && isSameDay(date, selectedDate)
                        ? 'bg-purple-600 text-white'
                        : isToday(date)
                        ? 'bg-purple-100 text-purple-600 font-medium'
                        : 'hover:bg-gray-100'
                    }`}
                  >
                    {format(date, 'd')}
                  </button>
                ))}
              </div>

              <div className="mt-4 flex justify-end space-x-2">
                <button
                  onClick={() => setShowCalendar(false)}
                  className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
                >
                  ANNULLA
                </button>
                <button
                  onClick={() => setShowCalendar(false)}
                  className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
                >
                  OK
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Duration */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Durata del servizio*
          </label>
          <select
            value={duration}
            onChange={(e) => setDuration(Number(e.target.value))}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-white"
          >
            <option value={1}>1 ora</option>
            <option value={2}>2 ore</option>
          </select>
        </div>

        {/* Address */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Indirizzo*
          </label>
          <input
            type="text"
            value={address}
            onChange={(e) => setAddress(e.target.value)}
            placeholder="Inserisci l'indirizzo di partenza"
            className="w-full px-4 py-3 border border-gray-300 rounded-lg"
          />
        </div>

        {/* Instructions */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Istruzioni e comunicazioni
          </label>
          <textarea
            value={instructions}
            onChange={(e) => setInstructions(e.target.value)}
            placeholder="Aggiungi note o istruzioni speciali..."
            rows={4}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg"
          />
        </div>

        {/* Price Summary */}
        <div className="bg-white rounded-lg p-4 border">
          <h3 className="font-semibold mb-3">Riepilogo Prezzo</h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>Tariffa oraria</span>
              <span>€{rider.hourlyRate}</span>
            </div>
            <div className="flex justify-between">
              <span>Durata</span>
              <span>{duration} ora{duration > 1 ? 'e' : ''}</span>
            </div>
            <div className="flex justify-between">
              <span>Subtotale</span>
              <span>€{(rider.hourlyRate * duration).toFixed(2)}</span>
            </div>
            <div className="flex justify-between">
              <span>Commissione piattaforma (15%)</span>
              <span>€{(rider.hourlyRate * duration * 0.15).toFixed(2)}</span>
            </div>
            <div className="border-t pt-2 flex justify-between font-semibold">
              <span>Totale</span>
              <span>€{calculateTotal().toFixed(2)}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Book Button */}
      <div className="fixed bottom-20 left-0 right-0 px-4 py-4 bg-white border-t">
        <button
          onClick={handleBooking}
          disabled={processing || !selectedDate || !address}
          className="w-full bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white font-semibold py-4 rounded-lg transition-colors"
        >
          {processing ? 'Elaborazione...' : 'Verifica disponibilità'}
        </button>
      </div>
    </div>
  )
}

export default BookingPage
