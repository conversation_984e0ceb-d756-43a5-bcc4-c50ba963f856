/**
 * Notification Center Component
 * Real-time notifications system
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { toast } from 'react-hot-toast'
import {
  BellIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  XMarkIcon
} from '@heroicons/react/24/outline'

import Card from '@components/ui/Card'
import Button from '@components/ui/Button'

const NotificationCenter = () => {
  const [notifications, setNotifications] = useState([
    {
      id: 1,
      type: 'success',
      title: 'Prenotazione Confermata',
      message: '<PERSON> ha accettato la tua prenotazione per oggi alle 14:00',
      timestamp: new Date(Date.now() - 5 * 60 * 1000),
      read: false,
      actions: [
        { label: 'Visualizza', action: 'view' },
        { label: 'Messaggio', action: 'message' }
      ]
    },
    {
      id: 2,
      type: 'warning',
      title: 'Documento in Scadenza',
      message: 'La patente di <PERSON> scade tra 15 giorni',
      timestamp: new Date(Date.now() - 30 * 60 * 1000),
      read: false,
      actions: [
        { label: 'Gestisci', action: 'manage' }
      ]
    },
    {
      id: 3,
      type: 'info',
      title: 'Nuovo Rider Disponibile',
      message: '3 nuovi rider si sono registrati nella tua zona',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
      read: true,
      actions: [
        { label: 'Visualizza', action: 'view' }
      ]
    }
  ])

  const [isOpen, setIsOpen] = useState(false)

  // Simulate real-time notifications
  useEffect(() => {
    const interval = setInterval(() => {
      // Simulate new notification
      if (Math.random() > 0.8) {
        const newNotification = {
          id: Date.now(),
          type: ['success', 'warning', 'info'][Math.floor(Math.random() * 3)],
          title: 'Nuova Notifica',
          message: 'Questa è una notifica di test in tempo reale',
          timestamp: new Date(),
          read: false,
          actions: [{ label: 'Visualizza', action: 'view' }]
        }

        setNotifications(prev => [newNotification, ...prev])
        
        // Show toast notification
        toast.success(newNotification.title, {
          duration: 4000,
          position: 'top-right'
        })
      }
    }, 10000) // Check every 10 seconds

    return () => clearInterval(interval)
  }, [])

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'success':
        return <CheckCircleIcon className="w-5 h-5 text-green-600" />
      case 'warning':
        return <ExclamationTriangleIcon className="w-5 h-5 text-orange-600" />
      case 'info':
        return <InformationCircleIcon className="w-5 h-5 text-blue-600" />
      default:
        return <BellIcon className="w-5 h-5 text-gray-600" />
    }
  }

  const getNotificationColor = (type) => {
    switch (type) {
      case 'success':
        return 'border-green-200 bg-green-50'
      case 'warning':
        return 'border-orange-200 bg-orange-50'
      case 'info':
        return 'border-blue-200 bg-blue-50'
      default:
        return 'border-gray-200 bg-gray-50'
    }
  }

  const markAsRead = (id) => {
    setNotifications(prev =>
      prev.map(notif =>
        notif.id === id ? { ...notif, read: true } : notif
      )
    )
  }

  const markAllAsRead = () => {
    setNotifications(prev =>
      prev.map(notif => ({ ...notif, read: true }))
    )
  }

  const removeNotification = (id) => {
    setNotifications(prev => prev.filter(notif => notif.id !== id))
  }

  const handleAction = (notification, action) => {
    console.log(`Action ${action} for notification:`, notification)
    markAsRead(notification.id)
  }

  const unreadCount = notifications.filter(n => !n.read).length

  const formatTimestamp = (timestamp) => {
    const now = new Date()
    const diff = now - timestamp
    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))

    if (minutes < 1) return 'Ora'
    if (minutes < 60) return `${minutes} min fa`
    if (hours < 24) return `${hours} ore fa`
    return `${days} giorni fa`
  }

  return (
    <div className="relative">
      {/* Notification Bell */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-full transition-colors"
      >
        <BellIcon className="h-6 w-6" />
        {unreadCount > 0 && (
          <motion.span
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            className="absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-medium"
          >
            {unreadCount > 9 ? '9+' : unreadCount}
          </motion.span>
        )}
      </button>

      {/* Notification Panel */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-50 max-h-96 overflow-hidden"
          >
            {/* Header */}
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">
                  Notifiche {unreadCount > 0 && `(${unreadCount})`}
                </h3>
                <div className="flex items-center space-x-2">
                  {unreadCount > 0 && (
                    <Button variant="ghost" size="sm" onClick={markAllAsRead}>
                      Segna tutte come lette
                    </Button>
                  )}
                  <button
                    onClick={() => setIsOpen(false)}
                    className="p-1 hover:bg-gray-100 rounded"
                  >
                    <XMarkIcon className="w-4 h-4 text-gray-500" />
                  </button>
                </div>
              </div>
            </div>

            {/* Notifications List */}
            <div className="max-h-80 overflow-y-auto">
              {notifications.length === 0 ? (
                <div className="p-8 text-center">
                  <BellIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Nessuna notifica
                  </h3>
                  <p className="text-gray-600">
                    Le tue notifiche appariranno qui
                  </p>
                </div>
              ) : (
                <div className="divide-y divide-gray-200">
                  {notifications.map((notification, index) => (
                    <motion.div
                      key={notification.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.05 }}
                      className={`p-4 hover:bg-gray-50 transition-colors ${
                        !notification.read ? 'bg-blue-50' : ''
                      }`}
                    >
                      <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0 mt-1">
                          {getNotificationIcon(notification.type)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <h4 className={`text-sm font-medium ${
                              !notification.read ? 'text-gray-900' : 'text-gray-700'
                            }`}>
                              {notification.title}
                            </h4>
                            <button
                              onClick={() => removeNotification(notification.id)}
                              className="ml-2 p-1 hover:bg-gray-200 rounded"
                            >
                              <XMarkIcon className="w-3 h-3 text-gray-400" />
                            </button>
                          </div>
                          <p className="text-sm text-gray-600 mt-1">
                            {notification.message}
                          </p>
                          <div className="flex items-center justify-between mt-2">
                            <span className="text-xs text-gray-500">
                              {formatTimestamp(notification.timestamp)}
                            </span>
                            {notification.actions && notification.actions.length > 0 && (
                              <div className="flex space-x-2">
                                {notification.actions.map((action, actionIndex) => (
                                  <button
                                    key={actionIndex}
                                    onClick={() => handleAction(notification, action.action)}
                                    className="text-xs text-blue-600 hover:text-blue-800 font-medium"
                                  >
                                    {action.label}
                                  </button>
                                ))}
                              </div>
                            )}
                          </div>
                          {!notification.read && (
                            <div className="absolute left-2 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-blue-500 rounded-full"></div>
                          )}
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              )}
            </div>

            {/* Footer */}
            {notifications.length > 0 && (
              <div className="p-3 border-t border-gray-200 bg-gray-50">
                <Button variant="ghost" size="sm" fullWidth>
                  Visualizza tutte le notifiche
                </Button>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Click outside to close */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  )
}

export default NotificationCenter
