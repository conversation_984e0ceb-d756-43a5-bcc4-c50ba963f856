/**
 * Rider Profile Page
 * Manage rider profile and settings
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { useState } from 'react'
import { motion } from 'framer-motion'
import {
  UserCircleIcon,
  TruckIcon,
  DocumentTextIcon,
  CogIcon,
  StarIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  PencilIcon
} from '@heroicons/react/24/outline'

import Card, { CardHeader, CardTitle, CardContent } from '@components/ui/Card'
import Button from '@components/ui/Button'
import Input from '@components/ui/Input'
import Header from '@components/layout/Header'

const mockProfile = {
  personal: {
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    phone: '+39 333 1234567',
    dateOfBirth: '1990-01-15',
    fiscalCode: '****************',
    address: 'Via Roma 123, Milano, 20100'
  },
  vehicle: {
    type: 'MOTO',
    make: 'Honda',
    model: 'SH 150',
    year: '2020',
    licensePlate: 'AB123CD',
    hourlyRate: 12.00
  },
  documents: [
    { type: 'identity_card', name: '<PERSON><PERSON> d\'Identità', status: 'approved', expiryDate: '2029-01-15' },
    { type: 'driving_license', name: 'Patente di Guida', status: 'approved', expiryDate: '2025-01-15' },
    { type: 'vehicle_registration', name: 'Libretto Circolazione', status: 'approved', expiryDate: '2025-12-31' },
    { type: 'insurance_certificate', name: 'Assicurazione', status: 'expiring_soon', expiryDate: '2024-12-31' }
  ],
  stats: {
    rating: 4.8,
    totalShifts: 156,
    completionRate: 98.5,
    responseTime: '2 min'
  }
}

const RiderProfile = () => {
  const [editMode, setEditMode] = useState(false)
  const [formData, setFormData] = useState(mockProfile.personal)

  const getDocumentStatusIcon = (status) => {
    switch (status) {
      case 'approved':
        return <CheckCircleIcon className="w-5 h-5 text-green-600" />
      case 'pending':
        return <ExclamationTriangleIcon className="w-5 h-5 text-yellow-600" />
      case 'expiring_soon':
        return <ExclamationTriangleIcon className="w-5 h-5 text-orange-600" />
      case 'expired':
        return <ExclamationTriangleIcon className="w-5 h-5 text-red-600" />
      default:
        return <ExclamationTriangleIcon className="w-5 h-5 text-gray-600" />
    }
  }

  const getDocumentStatusLabel = (status) => {
    switch (status) {
      case 'approved': return 'Approvato'
      case 'pending': return 'In revisione'
      case 'expiring_soon': return 'In scadenza'
      case 'expired': return 'Scaduto'
      default: return 'Sconosciuto'
    }
  }

  const getDocumentStatusColor = (status) => {
    switch (status) {
      case 'approved': return 'status-success'
      case 'pending': return 'status-warning'
      case 'expiring_soon': return 'bg-orange-100 text-orange-800'
      case 'expired': return 'status-error'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const handleSave = () => {
    // API call to save profile
    console.log('Saving profile:', formData)
    setEditMode(false)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header 
        title="Il Mio Profilo"
        subtitle="Gestisci le tue informazioni personali e documenti"
        actions={
          <Button 
            variant={editMode ? "rider" : "outline"} 
            onClick={editMode ? handleSave : () => setEditMode(true)}
            leftIcon={editMode ? <CheckCircleIcon className="w-4 h-4" /> : <PencilIcon className="w-4 h-4" />}
          >
            {editMode ? 'Salva Modifiche' : 'Modifica Profilo'}
          </Button>
        }
      />

      <div className="p-6 space-y-6">
        {/* Profile Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
          >
            <Card className="metric-card text-center">
              <div className="flex items-center justify-center mb-2">
                <StarIcon className="w-8 h-8 text-yellow-500 fill-current" />
              </div>
              <p className="metric-value">{mockProfile.stats.rating}</p>
              <p className="metric-label">Rating Medio</p>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Card className="metric-card text-center">
              <div className="flex items-center justify-center mb-2">
                <TruckIcon className="w-8 h-8 text-blue-600" />
              </div>
              <p className="metric-value">{mockProfile.stats.totalShifts}</p>
              <p className="metric-label">Turni Completati</p>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card className="metric-card text-center">
              <div className="flex items-center justify-center mb-2">
                <CheckCircleIcon className="w-8 h-8 text-green-600" />
              </div>
              <p className="metric-value">{mockProfile.stats.completionRate}%</p>
              <p className="metric-label">Tasso Completamento</p>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <Card className="metric-card text-center">
              <div className="flex items-center justify-center mb-2">
                <CogIcon className="w-8 h-8 text-purple-600" />
              </div>
              <p className="metric-value">{mockProfile.stats.responseTime}</p>
              <p className="metric-label">Tempo Risposta</p>
            </Card>
          </motion.div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Personal Information */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.5 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <UserCircleIcon className="w-5 h-5 text-blue-600" />
                  <span>Informazioni Personali</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Input
                      label="Nome"
                      value={formData.firstName}
                      onChange={(e) => setFormData({...formData, firstName: e.target.value})}
                      disabled={!editMode}
                    />
                    <Input
                      label="Cognome"
                      value={formData.lastName}
                      onChange={(e) => setFormData({...formData, lastName: e.target.value})}
                      disabled={!editMode}
                    />
                  </div>
                  <Input
                    label="Email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData({...formData, email: e.target.value})}
                    disabled={!editMode}
                  />
                  <Input
                    label="Telefono"
                    value={formData.phone}
                    onChange={(e) => setFormData({...formData, phone: e.target.value})}
                    disabled={!editMode}
                  />
                  <Input
                    label="Data di Nascita"
                    type="date"
                    value={formData.dateOfBirth}
                    onChange={(e) => setFormData({...formData, dateOfBirth: e.target.value})}
                    disabled={!editMode}
                  />
                  <Input
                    label="Codice Fiscale"
                    value={formData.fiscalCode}
                    onChange={(e) => setFormData({...formData, fiscalCode: e.target.value})}
                    disabled={!editMode}
                  />
                  <Input
                    label="Indirizzo"
                    value={formData.address}
                    onChange={(e) => setFormData({...formData, address: e.target.value})}
                    disabled={!editMode}
                  />
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Vehicle Information */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.6 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <TruckIcon className="w-5 h-5 text-green-600" />
                  <span>Informazioni Veicolo</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="form-label">Tipo Veicolo</label>
                      <select 
                        className="form-input"
                        value={mockProfile.vehicle.type}
                        disabled={!editMode}
                      >
                        <option value="MOTO">Moto</option>
                        <option value="BICI">Bicicletta</option>
                        <option value="AUTO">Auto</option>
                      </select>
                    </div>
                    <Input
                      label="Marca"
                      value={mockProfile.vehicle.make}
                      disabled={!editMode}
                    />
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Input
                      label="Modello"
                      value={mockProfile.vehicle.model}
                      disabled={!editMode}
                    />
                    <Input
                      label="Anno"
                      value={mockProfile.vehicle.year}
                      disabled={!editMode}
                    />
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Input
                      label="Targa"
                      value={mockProfile.vehicle.licensePlate}
                      disabled={!editMode}
                    />
                    <Input
                      label="Tariffa Oraria (€)"
                      type="number"
                      step="0.50"
                      max="12.50"
                      value={mockProfile.vehicle.hourlyRate}
                      disabled={!editMode}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Documents */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
        >
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center space-x-2">
                  <DocumentTextIcon className="w-5 h-5 text-purple-600" />
                  <span>Documenti</span>
                </CardTitle>
                <Button variant="outline" size="sm">
                  Carica Documento
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockProfile.documents.map((doc, index) => (
                  <motion.div
                    key={doc.type}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.8 + index * 0.1 }}
                    className="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
                  >
                    <div className="flex items-center space-x-4">
                      {getDocumentStatusIcon(doc.status)}
                      <div>
                        <h4 className="font-medium text-gray-900">{doc.name}</h4>
                        <p className="text-sm text-gray-600">
                          Scadenza: {new Date(doc.expiryDate).toLocaleDateString('it-IT')}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <span className={`status-badge ${getDocumentStatusColor(doc.status)}`}>
                        {getDocumentStatusLabel(doc.status)}
                      </span>
                      <Button variant="outline" size="sm">
                        Visualizza
                      </Button>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}

export default RiderProfile
