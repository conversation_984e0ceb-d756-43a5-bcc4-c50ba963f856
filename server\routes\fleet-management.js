/**
 * Fleet Management Routes
 * API endpoints for merchant fleet management
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import express from 'express'
import { FleetManagementService } from '../services/FleetManagementService.js'
import { authenticateClerk } from '../middleware/auth.js'
import { validationRules, handleValidationErrors, createRateLimit } from '../middleware/security.js'
import { body } from 'express-validator'

const router = express.Router()
const fleetService = new FleetManagementService()

// Apply rate limiting
router.use(createRateLimit)

// ============================================================================
// FLEET OVERVIEW AND ANALYTICS
// ============================================================================

/**
 * Get fleet overview
 */
router.get('/overview',
  authenticateClerk,
  async (req, res) => {
    try {
      const merchantId = req.auth.userId
      const { startDate, endDate } = req.query

      const overview = await fleetService.getFleetOverview(merchantId, {
        startDate,
        endDate
      })

      res.json({
        success: true,
        overview
      })
    } catch (error) {
      console.error('Error getting fleet overview:', error)
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to get fleet overview'
      })
    }
  }
)

/**
 * Get detailed fleet analytics
 */
router.get('/analytics',
  authenticateClerk,
  async (req, res) => {
    try {
      const merchantId = req.auth.userId
      const { startDate, endDate } = req.query

      if (!startDate || !endDate) {
        return res.status(400).json({
          success: false,
          message: 'Start date and end date are required'
        })
      }

      const analytics = await fleetService.getFleetAnalytics(merchantId, {
        startDate,
        endDate
      })

      res.json({
        success: true,
        analytics
      })
    } catch (error) {
      console.error('Error getting fleet analytics:', error)
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to get fleet analytics'
      })
    }
  }
)

// ============================================================================
// BULK OPERATIONS
// ============================================================================

/**
 * Bulk schedule shifts
 */
router.post('/bulk-schedule',
  authenticateClerk,
  [
    body('scheduleRequests').notEmpty().withMessage('Schedule requests are required')
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const merchantId = req.auth.userId
      const { scheduleRequests } = req.body

      if (!Array.isArray(scheduleRequests) || scheduleRequests.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Schedule requests must be a non-empty array'
        })
      }

      const results = await fleetService.bulkScheduleShifts(merchantId, scheduleRequests)

      res.json({
        success: true,
        message: 'Bulk scheduling completed',
        results
      })
    } catch (error) {
      console.error('Error bulk scheduling shifts:', error)
      res.status(400).json({
        success: false,
        message: error.message || 'Failed to bulk schedule shifts'
      })
    }
  }
)

/**
 * Manage rider assignments
 */
router.post('/assignments',
  authenticateClerk,
  [
    body('assignments').notEmpty().withMessage('Assignments are required')
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const merchantId = req.auth.userId
      const { assignments } = req.body

      if (!Array.isArray(assignments) || assignments.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Assignments must be a non-empty array'
        })
      }

      const results = await fleetService.manageRiderAssignments(merchantId, assignments)

      res.json({
        success: true,
        message: 'Rider assignments processed',
        results
      })
    } catch (error) {
      console.error('Error managing rider assignments:', error)
      res.status(400).json({
        success: false,
        message: error.message || 'Failed to manage rider assignments'
      })
    }
  }
)

// ============================================================================
// OPTIMIZATION
// ============================================================================

/**
 * Get fleet optimization recommendations
 */
router.post('/optimize',
  authenticateClerk,
  [
    body('timeRange').notEmpty().withMessage('Time range is required'),
    body('objectives').notEmpty().withMessage('Objectives are required')
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const merchantId = req.auth.userId
      const optimizationRequest = req.body

      const optimization = await fleetService.optimizeFleetAllocation(merchantId, optimizationRequest)

      res.json({
        success: true,
        optimization
      })
    } catch (error) {
      console.error('Error optimizing fleet:', error)
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to optimize fleet allocation'
      })
    }
  }
)

// ============================================================================
// RIDER MANAGEMENT
// ============================================================================

/**
 * Get fleet riders
 */
router.get('/riders',
  authenticateClerk,
  async (req, res) => {
    try {
      const merchantId = req.auth.userId
      const { status, vehicleType, sortBy } = req.query

      const riders = await fleetService.getFleetRiders(merchantId)

      // Apply filters
      let filteredRiders = riders
      if (status) {
        filteredRiders = filteredRiders.filter(rider => rider.status === status)
      }
      if (vehicleType) {
        filteredRiders = filteredRiders.filter(rider => rider.vehicle_type === vehicleType)
      }

      // Apply sorting
      if (sortBy) {
        switch (sortBy) {
          case 'rating':
            filteredRiders.sort((a, b) => (b.rating || 0) - (a.rating || 0))
            break
          case 'name':
            filteredRiders.sort((a, b) => a.name.localeCompare(b.name))
            break
          case 'shifts':
            filteredRiders.sort((a, b) => (b.total_shifts || 0) - (a.total_shifts || 0))
            break
        }
      }

      res.json({
        success: true,
        riders: filteredRiders,
        total: filteredRiders.length
      })
    } catch (error) {
      console.error('Error getting fleet riders:', error)
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to get fleet riders'
      })
    }
  }
)

/**
 * Get rider performance details
 */
router.get('/riders/:riderId/performance',
  authenticateClerk,
  async (req, res) => {
    try {
      const merchantId = req.auth.userId
      const { riderId } = req.params
      const { startDate, endDate } = req.query

      // Get rider shifts for the period
      const shifts = await fleetService.getFleetShifts(merchantId, { startDate, endDate })
      const riderShifts = shifts.filter(shift => shift.rider_id === riderId)

      const performance = {
        riderId,
        period: { startDate, endDate },
        metrics: {
          totalShifts: riderShifts.length,
          totalHours: riderShifts.reduce((sum, shift) => sum + (shift.duration || 1), 0),
          completionRate: riderShifts.filter(s => s.status === 'completed').length / riderShifts.length * 100,
          averageRating: 4.7, // Mock
          earnings: riderShifts.reduce((sum, shift) => sum + (shift.duration || 1) * 12, 0)
        },
        trends: {
          daily: {}, // Mock
          weekly: {} // Mock
        },
        compliance: {
          documentsValid: true,
          hoursCompliant: true,
          alerts: []
        }
      }

      res.json({
        success: true,
        performance
      })
    } catch (error) {
      console.error('Error getting rider performance:', error)
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to get rider performance'
      })
    }
  }
)

// ============================================================================
// COMPLIANCE AND MONITORING
// ============================================================================

/**
 * Get compliance dashboard
 */
router.get('/compliance',
  authenticateClerk,
  async (req, res) => {
    try {
      const merchantId = req.auth.userId

      const riders = await fleetService.getFleetRiders(merchantId)
      const shifts = await fleetService.getFleetShifts(merchantId, {})

      const compliance = {
        overview: {
          totalRiders: riders.length,
          compliantRiders: riders.filter(r => r.status === 'active').length,
          complianceScore: await fleetService.calculateComplianceScore(merchantId)
        },
        documents: await fleetService.getDocumentsComplianceStatus(riders),
        workingHours: fleetService.checkWorkingHoursCompliance(shifts),
        alerts: await fleetService.getComplianceAlerts(merchantId),
        upcomingExpirations: [
          {
            riderId: 'rider_1',
            riderName: 'Mario Rossi',
            documentType: 'driving_license',
            expiryDate: '2025-01-15',
            daysUntilExpiry: 15
          }
        ]
      }

      res.json({
        success: true,
        compliance
      })
    } catch (error) {
      console.error('Error getting compliance data:', error)
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to get compliance data'
      })
    }
  }
)

/**
 * Get fleet alerts
 */
router.get('/alerts',
  authenticateClerk,
  async (req, res) => {
    try {
      const merchantId = req.auth.userId
      const { priority, type } = req.query

      let alerts = await fleetService.getFleetAlerts(merchantId)

      // Apply filters
      if (priority) {
        alerts = alerts.filter(alert => alert.priority === priority)
      }
      if (type) {
        alerts = alerts.filter(alert => alert.type === type)
      }

      res.json({
        success: true,
        alerts,
        total: alerts.length
      })
    } catch (error) {
      console.error('Error getting fleet alerts:', error)
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to get fleet alerts'
      })
    }
  }
)

// ============================================================================
// REPORTING
// ============================================================================

/**
 * Generate fleet report
 */
router.post('/reports/generate',
  authenticateClerk,
  [
    body('reportType').notEmpty().withMessage('Report type is required'),
    body('startDate').notEmpty().withMessage('Start date is required'),
    body('endDate').notEmpty().withMessage('End date is required')
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const merchantId = req.auth.userId
      const { reportType, startDate, endDate, format = 'json' } = req.body

      let reportData

      switch (reportType) {
        case 'performance':
          reportData = await fleetService.getFleetAnalytics(merchantId, { startDate, endDate })
          break
        case 'compliance':
          reportData = {
            compliance: await fleetService.calculateComplianceScore(merchantId),
            alerts: await fleetService.getFleetAlerts(merchantId)
          }
          break
        case 'utilization':
          const overview = await fleetService.getFleetOverview(merchantId, { startDate, endDate })
          reportData = {
            utilizationRate: overview.operations.utilizationRate,
            totalHours: overview.operations.totalHours,
            efficiency: overview.performance.efficiency
          }
          break
        default:
          return res.status(400).json({
            success: false,
            message: 'Invalid report type'
          })
      }

      const report = {
        reportType,
        period: { startDate, endDate },
        generatedAt: new Date().toISOString(),
        merchantId,
        data: reportData
      }

      res.json({
        success: true,
        report
      })
    } catch (error) {
      console.error('Error generating report:', error)
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to generate report'
      })
    }
  }
)

export default router
