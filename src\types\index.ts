// Common types and interfaces for BeMyRider

export interface User {
  id: string
  clerk_id: string
  email: string
  first_name?: string
  last_name?: string
  name?: string
  type: 'ESERCENTE' | 'RIDER'
  profile_complete: boolean
  phone?: string
  city?: string
  hourly_rate?: number
  vehicle_type?: string
  vehicle_model?: string
  description?: string
  is_available?: boolean
  created_at: string
  updated_at: string
}

export interface Rider extends User {
  averageRating?: number
  totalReviews?: number
  totalBookings?: number
}

export interface DashboardStats {
  totalBookings: number
  activeBookings: number
  totalEarnings?: number
  averageRating?: number
  totalReviews?: number
  totalSpent?: number
  completedBookings?: number
}

export interface Booking {
  id: string
  rider_id: string
  client_id: string
  date: string
  duration: number
  address: string
  instructions?: string
  hourly_rate: number
  subtotal: number
  platform_fee: number
  total_amount: number
  status: 'PENDING' | 'CONFIRMED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED'
  created_at: string
  updated_at: string
  rider?: Rider
  client?: User
}

export interface ApiResponse<T> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

export interface DashboardData {
  stats: DashboardStats | null
  recentBookings: Booking[]
  earnings: any | null
  activity: any[]
  loading: boolean
  error: string | null
}
