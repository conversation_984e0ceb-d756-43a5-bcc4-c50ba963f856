/**
 * Users API Integration Tests
 * Tests the complete user API endpoints
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest'
import request from 'supertest'
import { createTestApp } from '../../helpers/test-app.js'

describe('Users API Integration Tests', () => {
  let app
  let testUser
  let authHeaders

  beforeAll(async () => {
    app = await createTestApp()
    
    // Create test user
    testUser = global.createTestUser()
    authHeaders = global.createAuthHeaders()
  })

  afterAll(async () => {
    // Cleanup test data
    if (global.testDb) {
      global.testDb.clear()
    }
  })

  beforeEach(() => {
    // Reset test database state
    if (global.testDb) {
      global.testDb.clear()
      global.testDb.addUser(testUser)
    }
  })

  describe('GET /api/users/profile/:clerkId', () => {
    it('should get user profile successfully with valid auth', async () => {
      const response = await request(app)
        .get(`/api/users/profile/${testUser.clerk_id}`)
        .set(authHeaders)
        .expect(200)

      expect(response.body).toMatchObject({
        success: true,
        user: expect.objectContaining({
          id: testUser.id,
          clerk_id: testUser.clerk_id,
          email: testUser.email,
          type: testUser.type
        })
      })

      // Should not expose sensitive fields
      expect(response.body.user.stripe_customer_id).toBeUndefined()
      expect(response.body.user.stripe_account_id).toBeUndefined()
    })

    it('should return 401 without authentication', async () => {
      const response = await request(app)
        .get(`/api/users/profile/${testUser.clerk_id}`)
        .expect(401)

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('autenticazione')
      })
    })

    it('should return 403 when accessing other user profile', async () => {
      const otherUser = global.createTestUser({ clerk_id: 'other_user_id' })
      global.testDb.addUser(otherUser)

      const response = await request(app)
        .get(`/api/users/profile/${otherUser.clerk_id}`)
        .set(authHeaders)
        .expect(403)

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('accedere solo al tuo profilo')
      })
    })

    it('should return 404 for non-existent user', async () => {
      const response = await request(app)
        .get('/api/users/profile/nonexistent_user')
        .set(authHeaders)
        .expect(404)

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('non trovato')
      })
    })

    it('should handle database errors gracefully', async () => {
      // Mock database error
      const originalDb = global.testDb
      global.testDb = {
        getUserByClerkId: () => { throw new Error('Database connection failed') }
      }

      const response = await request(app)
        .get(`/api/users/profile/${testUser.clerk_id}`)
        .set(authHeaders)
        .expect(500)

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('errore')
      })

      // Restore original db
      global.testDb = originalDb
    })
  })

  describe('POST /api/users/sync', () => {
    const validSyncData = {
      clerkId: 'new_user_clerk_id',
      email: '<EMAIL>',
      firstName: 'New',
      lastName: 'User',
      name: 'New User',
      userType: 'RIDER',
      profileComplete: false
    }

    it('should sync new user successfully', async () => {
      const response = await request(app)
        .post('/api/users/sync')
        .set(authHeaders)
        .send(validSyncData)
        .expect(200)

      expect(response.body).toMatchObject({
        success: true,
        message: expect.stringContaining('sincronizzato'),
        user: expect.objectContaining({
          clerk_id: validSyncData.clerkId,
          email: validSyncData.email,
          type: validSyncData.userType
        })
      })
    })

    it('should update existing user on sync', async () => {
      // First sync
      await request(app)
        .post('/api/users/sync')
        .set(authHeaders)
        .send(validSyncData)
        .expect(200)

      // Second sync with updated data
      const updatedData = {
        ...validSyncData,
        firstName: 'Updated',
        profileComplete: true
      }

      const response = await request(app)
        .post('/api/users/sync')
        .set(authHeaders)
        .send(updatedData)
        .expect(200)

      expect(response.body.user.first_name).toBe('Updated')
      expect(response.body.user.profile_complete).toBe(true)
    })

    it('should validate required fields', async () => {
      const invalidData = {
        clerkId: 'test_id'
        // Missing required fields
      }

      const response = await request(app)
        .post('/api/users/sync')
        .set(authHeaders)
        .send(invalidData)
        .expect(400)

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('validazione'),
        errors: expect.arrayContaining([
          expect.stringContaining('email'),
          expect.stringContaining('userType')
        ])
      })
    })

    it('should validate email format', async () => {
      const invalidData = {
        ...validSyncData,
        email: 'invalid-email-format'
      }

      const response = await request(app)
        .post('/api/users/sync')
        .set(authHeaders)
        .send(invalidData)
        .expect(400)

      expect(response.body.errors).toContain(
        expect.stringContaining('email valido')
      )
    })

    it('should prevent syncing other user data', async () => {
      const unauthorizedData = {
        ...validSyncData,
        clerkId: 'different_user_id'
      }

      const response = await request(app)
        .post('/api/users/sync')
        .set(authHeaders)
        .send(unauthorizedData)
        .expect(403)

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('sincronizzare solo i tuoi dati')
      })
    })

    it('should handle rate limiting', async () => {
      // Make multiple rapid requests
      const requests = Array(10).fill().map(() =>
        request(app)
          .post('/api/users/sync')
          .set(authHeaders)
          .send(validSyncData)
      )

      const responses = await Promise.all(requests)
      const rateLimitedResponses = responses.filter(r => r.status === 429)

      expect(rateLimitedResponses.length).toBeGreaterThan(0)
    })
  })

  describe('PUT /api/users/profile/:clerkId', () => {
    const updateData = {
      firstName: 'Updated',
      lastName: 'Name',
      phone: '+39 333 1234567',
      city: 'Milano',
      hourlyRate: 30.00
    }

    it('should update user profile successfully', async () => {
      const response = await request(app)
        .put(`/api/users/profile/${testUser.clerk_id}`)
        .set(authHeaders)
        .send(updateData)
        .expect(200)

      expect(response.body).toMatchObject({
        success: true,
        message: expect.stringContaining('aggiornato'),
        user: expect.objectContaining({
          first_name: 'Updated',
          last_name: 'Name',
          phone: '+39 333 1234567'
        })
      })
    })

    it('should validate phone number format', async () => {
      const invalidData = {
        ...updateData,
        phone: 'invalid-phone'
      }

      const response = await request(app)
        .put(`/api/users/profile/${testUser.clerk_id}`)
        .set(authHeaders)
        .send(invalidData)
        .expect(400)

      expect(response.body.errors).toContain(
        expect.stringContaining('telefono')
      )
    })

    it('should validate hourly rate for riders', async () => {
      const riderUser = global.createTestUser({ type: 'RIDER' })
      global.testDb.addUser(riderUser)

      const invalidData = {
        ...updateData,
        hourlyRate: -10 // Invalid negative rate
      }

      const response = await request(app)
        .put(`/api/users/profile/${riderUser.clerk_id}`)
        .set(authHeaders)
        .send(invalidData)
        .expect(400)

      expect(response.body.errors).toContain(
        expect.stringContaining('tariffa')
      )
    })

    it('should prevent updating other user profiles', async () => {
      const otherUser = global.createTestUser({ clerk_id: 'other_user' })
      global.testDb.addUser(otherUser)

      const response = await request(app)
        .put(`/api/users/profile/${otherUser.clerk_id}`)
        .set(authHeaders)
        .send(updateData)
        .expect(403)

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('modificare solo il tuo profilo')
      })
    })

    it('should sanitize input data', async () => {
      const maliciousData = {
        firstName: '<script>alert("xss")</script>',
        lastName: 'Normal Name',
        description: 'Normal description with <b>bold</b> text'
      }

      const response = await request(app)
        .put(`/api/users/profile/${testUser.clerk_id}`)
        .set(authHeaders)
        .send(maliciousData)
        .expect(200)

      // Should strip dangerous HTML but allow safe formatting
      expect(response.body.user.first_name).not.toContain('<script>')
      expect(response.body.user.description).toContain('<b>bold</b>')
    })
  })

  describe('GET /api/users/riders', () => {
    beforeEach(() => {
      // Add test riders
      const riders = [
        global.createTestUser({ 
          type: 'RIDER', 
          city: 'Milano', 
          vehicle_type: 'MOTO',
          hourly_rate: 25.00,
          average_rating: 4.5,
          is_available: true
        }),
        global.createTestUser({ 
          type: 'RIDER', 
          city: 'Roma', 
          vehicle_type: 'BICI',
          hourly_rate: 15.00,
          average_rating: 4.0,
          is_available: false
        }),
        global.createTestUser({ 
          type: 'RIDER', 
          city: 'Milano', 
          vehicle_type: 'AUTO',
          hourly_rate: 35.00,
          average_rating: 4.8,
          is_available: true
        })
      ]

      riders.forEach(rider => global.testDb.addUser(rider))
    })

    it('should get all available riders', async () => {
      const response = await request(app)
        .get('/api/users/riders')
        .expect(200)

      expect(response.body).toMatchObject({
        success: true,
        riders: expect.arrayContaining([
          expect.objectContaining({
            type: 'RIDER',
            is_available: true
          })
        ])
      })

      // Should be ordered by rating
      const ratings = response.body.riders.map(r => r.average_rating)
      expect(ratings).toEqual([...ratings].sort((a, b) => b - a))
    })

    it('should filter riders by city', async () => {
      const response = await request(app)
        .get('/api/users/riders?city=Milano')
        .expect(200)

      expect(response.body.riders).toHaveLength(2)
      response.body.riders.forEach(rider => {
        expect(rider.city).toBe('Milano')
      })
    })

    it('should filter riders by vehicle type', async () => {
      const response = await request(app)
        .get('/api/users/riders?vehicleType=MOTO')
        .expect(200)

      expect(response.body.riders).toHaveLength(1)
      expect(response.body.riders[0].vehicle_type).toBe('MOTO')
    })

    it('should filter riders by availability', async () => {
      const response = await request(app)
        .get('/api/users/riders?available=true')
        .expect(200)

      response.body.riders.forEach(rider => {
        expect(rider.is_available).toBe(true)
      })
    })

    it('should filter riders by rate range', async () => {
      const response = await request(app)
        .get('/api/users/riders?minRate=20&maxRate=30')
        .expect(200)

      response.body.riders.forEach(rider => {
        expect(rider.hourly_rate).toBeGreaterThanOrEqual(20)
        expect(rider.hourly_rate).toBeLessThanOrEqual(30)
      })
    })

    it('should support pagination', async () => {
      const response = await request(app)
        .get('/api/users/riders?page=1&limit=2')
        .expect(200)

      expect(response.body).toMatchObject({
        success: true,
        riders: expect.any(Array),
        pagination: expect.objectContaining({
          page: 1,
          limit: 2,
          total: expect.any(Number),
          pages: expect.any(Number)
        })
      })

      expect(response.body.riders.length).toBeLessThanOrEqual(2)
    })

    it('should include rider statistics', async () => {
      const response = await request(app)
        .get('/api/users/riders')
        .expect(200)

      response.body.riders.forEach(rider => {
        expect(rider).toMatchObject({
          averageRating: expect.any(Number),
          totalReviews: expect.any(Number),
          totalBookings: expect.any(Number)
        })
      })
    })
  })

  describe('Error Handling', () => {
    it('should handle malformed JSON', async () => {
      const response = await request(app)
        .post('/api/users/sync')
        .set(authHeaders)
        .set('Content-Type', 'application/json')
        .send('{"invalid": json}')
        .expect(400)

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('JSON')
      })
    })

    it('should handle missing Content-Type header', async () => {
      const response = await request(app)
        .post('/api/users/sync')
        .set('Authorization', authHeaders.Authorization)
        .send('some data')
        .expect(400)

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('Content-Type')
      })
    })

    it('should handle request timeout', async () => {
      // Mock a slow operation
      const originalDb = global.testDb
      global.testDb = {
        getUserByClerkId: () => new Promise(resolve => setTimeout(resolve, 35000))
      }

      const response = await request(app)
        .get(`/api/users/profile/${testUser.clerk_id}`)
        .set(authHeaders)
        .timeout(5000)
        .expect(408)

      // Restore original db
      global.testDb = originalDb
    })
  })
})
