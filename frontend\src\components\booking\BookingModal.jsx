/**
 * Booking Modal Component
 * Modal for creating new bookings
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { useState, Fragment } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { motion } from 'framer-motion'
import {
  XMarkIcon,
  CalendarIcon,
  ClockIcon,
  MapPinIcon,
  CurrencyEuroIcon,
  UserIcon,
  TruckIcon
} from '@heroicons/react/24/outline'

import Button from '@components/ui/Button'
import Input from '@components/ui/Input'
import Card, { CardContent } from '@components/ui/Card'

const BookingModal = ({ isOpen, onClose, rider, onConfirm }) => {
  const [bookingData, setBookingData] = useState({
    startTime: '',
    endTime: '',
    location: '',
    notes: '',
    urgentDelivery: false
  })
  const [step, setStep] = useState(1)
  const [loading, setLoading] = useState(false)

  const calculateCost = () => {
    if (!bookingData.startTime || !bookingData.endTime || !rider) return 0
    
    const start = new Date(bookingData.startTime)
    const end = new Date(bookingData.endTime)
    const hours = (end - start) / (1000 * 60 * 60)
    
    const baseCost = hours * rider.hourlyRate
    const platformFee = baseCost * 0.15
    const urgentFee = bookingData.urgentDelivery ? baseCost * 0.1 : 0
    
    return {
      baseCost,
      platformFee,
      urgentFee,
      total: baseCost + platformFee + urgentFee
    }
  }

  const handleConfirm = async () => {
    setLoading(true)
    try {
      const cost = calculateCost()
      await onConfirm({
        ...bookingData,
        riderId: rider.id,
        cost
      })
      onClose()
    } catch (error) {
      console.error('Booking error:', error)
    } finally {
      setLoading(false)
    }
  }

  const cost = calculateCost()

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <div className="flex items-center justify-between mb-6">
                  <Dialog.Title className="text-lg font-medium text-gray-900">
                    Prenota {rider?.name}
                  </Dialog.Title>
                  <button
                    onClick={onClose}
                    className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                  >
                    <XMarkIcon className="w-5 h-5 text-gray-500" />
                  </button>
                </div>

                {step === 1 && (
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    className="space-y-6"
                  >
                    {/* Rider Info */}
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center space-x-4">
                          <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                            <span className="text-white font-bold text-lg">
                              {rider?.name.split(' ').map(n => n[0]).join('')}
                            </span>
                          </div>
                          <div className="flex-1">
                            <h3 className="font-semibold text-gray-900">{rider?.name}</h3>
                            <div className="flex items-center space-x-4 text-sm text-gray-600">
                              <span className="flex items-center space-x-1">
                                <TruckIcon className="w-4 h-4" />
                                <span>{rider?.vehicleType}</span>
                              </span>
                              <span>⭐ {rider?.rating}</span>
                              <span>€{rider?.hourlyRate}/h</span>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Booking Form */}
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Input
                          label="Data e Ora Inizio"
                          type="datetime-local"
                          value={bookingData.startTime}
                          onChange={(e) => setBookingData({...bookingData, startTime: e.target.value})}
                          leftIcon={<CalendarIcon className="w-4 h-4" />}
                        />
                        <Input
                          label="Data e Ora Fine"
                          type="datetime-local"
                          value={bookingData.endTime}
                          onChange={(e) => setBookingData({...bookingData, endTime: e.target.value})}
                          leftIcon={<ClockIcon className="w-4 h-4" />}
                        />
                      </div>
                      
                      <Input
                        label="Luogo di Lavoro"
                        placeholder="es. Via Roma 123, Milano"
                        value={bookingData.location}
                        onChange={(e) => setBookingData({...bookingData, location: e.target.value})}
                        leftIcon={<MapPinIcon className="w-4 h-4" />}
                      />
                      
                      <div>
                        <label className="form-label">Note Aggiuntive</label>
                        <textarea
                          className="form-input"
                          rows={3}
                          placeholder="Istruzioni speciali, dettagli del lavoro..."
                          value={bookingData.notes}
                          onChange={(e) => setBookingData({...bookingData, notes: e.target.value})}
                        />
                      </div>

                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="urgent"
                          checked={bookingData.urgentDelivery}
                          onChange={(e) => setBookingData({...bookingData, urgentDelivery: e.target.checked})}
                          className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                        />
                        <label htmlFor="urgent" className="text-sm font-medium text-gray-700">
                          Consegna Urgente (+10%)
                        </label>
                      </div>
                    </div>

                    <div className="flex justify-end space-x-3">
                      <Button variant="outline" onClick={onClose}>
                        Annulla
                      </Button>
                      <Button 
                        variant="merchant" 
                        onClick={() => setStep(2)}
                        disabled={!bookingData.startTime || !bookingData.endTime || !bookingData.location}
                      >
                        Continua
                      </Button>
                    </div>
                  </motion.div>
                )}

                {step === 2 && (
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    className="space-y-6"
                  >
                    <h3 className="text-lg font-semibold text-gray-900">Riepilogo Prenotazione</h3>
                    
                    {/* Booking Summary */}
                    <Card>
                      <CardContent className="p-4 space-y-4">
                        <div className="flex items-center justify-between">
                          <span className="text-gray-600">Rider:</span>
                          <span className="font-medium">{rider?.name}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-gray-600">Periodo:</span>
                          <span className="font-medium">
                            {new Date(bookingData.startTime).toLocaleString('it-IT')} - 
                            {new Date(bookingData.endTime).toLocaleString('it-IT')}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-gray-600">Luogo:</span>
                          <span className="font-medium">{bookingData.location}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-gray-600">Durata:</span>
                          <span className="font-medium">
                            {((new Date(bookingData.endTime) - new Date(bookingData.startTime)) / (1000 * 60 * 60)).toFixed(1)} ore
                          </span>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Cost Breakdown */}
                    <Card>
                      <CardContent className="p-4 space-y-3">
                        <h4 className="font-medium text-gray-900">Dettaglio Costi</h4>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span>Costo base (€{rider?.hourlyRate}/h)</span>
                            <span>€{cost.baseCost?.toFixed(2)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Commissione piattaforma (15%)</span>
                            <span>€{cost.platformFee?.toFixed(2)}</span>
                          </div>
                          {bookingData.urgentDelivery && (
                            <div className="flex justify-between">
                              <span>Supplemento urgente (10%)</span>
                              <span>€{cost.urgentFee?.toFixed(2)}</span>
                            </div>
                          )}
                          <div className="border-t pt-2 flex justify-between font-semibold">
                            <span>Totale</span>
                            <span>€{cost.total?.toFixed(2)}</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <div className="flex justify-between">
                      <Button variant="outline" onClick={() => setStep(1)}>
                        Indietro
                      </Button>
                      <Button 
                        variant="merchant" 
                        onClick={handleConfirm}
                        loading={loading}
                        leftIcon={<CurrencyEuroIcon className="w-4 h-4" />}
                      >
                        Conferma Prenotazione
                      </Button>
                    </div>
                  </motion.div>
                )}
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  )
}

export default BookingModal
