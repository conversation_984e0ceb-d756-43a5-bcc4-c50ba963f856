/**
 * Merchant Analytics Page
 * Analytics and reporting for merchant operations
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { useState } from 'react'
import { motion } from 'framer-motion'
import { ChartBarIcon, ArrowTrendingUpIcon, CurrencyEuroIcon, ClockIcon } from '@heroicons/react/24/outline'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts'

import Card, { CardHeader, CardTitle, CardContent } from '@components/ui/Card'
import Button from '@components/ui/Button'
import Header from '@components/layout/Header'

const mockAnalyticsData = {
  summary: {
    totalCost: 5420.75,
    totalHours: 456,
    averageUtilization: 78,
    costPerHour: 11.89
  },
  chartData: [
    { month: 'Gen', cost: 834.50, hours: 70, riders: 6 },
    { month: 'Feb', cost: 945.75, hours: 79, riders: 7 },
    { month: 'Mar', cost: 1156.25, hours: 97, riders: 8 },
    { month: 'Apr', cost: 989.50, hours: 83, riders: 8 },
    { month: 'Mag', cost: 1267.80, hours: 106, riders: 8 },
    { month: 'Giu', cost: 1226.95, hours: 103, riders: 8 }
  ],
  vehicleDistribution: [
    { name: 'MOTO', value: 60, color: '#3B82F6' },
    { name: 'BICI', value: 30, color: '#10B981' },
    { name: 'AUTO', value: 10, color: '#F59E0B' }
  ]
}

const MerchantAnalytics = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('6months')

  return (
    <div className="min-h-screen bg-gray-50">
      <Header 
        title="Analytics Flotta"
        subtitle="Analisi dettagliate delle performance della tua flotta"
        actions={
          <Button variant="outline" size="sm">
            Esporta Report
          </Button>
        }
      />

      <div className="p-6 space-y-6">
        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="metric-card">
            <div className="flex items-center justify-between">
              <div>
                <p className="metric-label">Costo Totale</p>
                <p className="metric-value">€{mockAnalyticsData.summary.totalCost.toFixed(2)}</p>
                <p className="metric-change metric-change-positive">+12.5% dal periodo precedente</p>
              </div>
              <CurrencyEuroIcon className="w-8 h-8 text-green-600" />
            </div>
          </Card>

          <Card className="metric-card">
            <div className="flex items-center justify-between">
              <div>
                <p className="metric-label">Ore Totali</p>
                <p className="metric-value">{mockAnalyticsData.summary.totalHours}h</p>
                <p className="metric-change metric-change-positive">+8.3% dal periodo precedente</p>
              </div>
              <ClockIcon className="w-8 h-8 text-blue-600" />
            </div>
          </Card>

          <Card className="metric-card">
            <div className="flex items-center justify-between">
              <div>
                <p className="metric-label">Utilizzo Medio</p>
                <p className="metric-value">{mockAnalyticsData.summary.averageUtilization}%</p>
                <p className="metric-change metric-change-positive">Ottimo livello</p>
              </div>
              <ArrowTrendingUpIcon className="w-8 h-8 text-purple-600" />
            </div>
          </Card>

          <Card className="metric-card">
            <div className="flex items-center justify-between">
              <div>
                <p className="metric-label">Costo/Ora</p>
                <p className="metric-value">€{mockAnalyticsData.summary.costPerHour.toFixed(2)}</p>
                <p className="metric-change">Media di mercato</p>
              </div>
              <ChartBarIcon className="w-8 h-8 text-orange-600" />
            </div>
          </Card>
        </div>

        {/* Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Andamento Costi</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={mockAnalyticsData.chartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip formatter={(value) => [`€${value}`, 'Costi']} />
                  <Line type="monotone" dataKey="cost" stroke="#3B82F6" strokeWidth={3} />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Distribuzione Veicoli</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={mockAnalyticsData.vehicleDistribution}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    dataKey="value"
                    label={({ name, value }) => `${name}: ${value}%`}
                  >
                    {mockAnalyticsData.vehicleDistribution.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default MerchantAnalytics
