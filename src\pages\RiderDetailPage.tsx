import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { Star, Calendar, Clock, Bike, User, MessageSquare } from 'lucide-react'
import axios from 'axios'

interface Rider {
  id: string
  name: string
  rating: number
  hourlyRate: number
  location: string
  vehicleType: string
  vehicleModel: string
  profileImage?: string
  description: string
  availability: {
    days: string[]
    hours: string
  }
  isAvailable: boolean
}

const RiderDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const [rider, setRider] = useState<Rider | null>(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'info' | 'profile' | 'reviews' | 'contact'>('info')

  useEffect(() => {
    fetchRiderDetails()
  }, [id])

  const fetchRiderDetails = async () => {
    try {
      setLoading(true)
      const response = await axios.get(`/api/riders/${id}`)
      setRider(response.data.rider)
    } catch (error) {
      console.error('Error fetching rider details:', error)
      // Mock data for development
      setRider({
        id: id || '1',
        name: 'Paolo cimino',
        rating: 5.0,
        hourlyRate: 12,
        location: 'Catania CT, Italia',
        vehicleType: 'MOTO',
        vehicleModel: 'TRK 502X (NON POSSIEDO LA BORSA)',
        description: 'Ciao Sono Paolo, persona affidabile, seria, e puntuale, con esperienza nel settore',
        availability: {
          days: ['Domenica', 'Lunedì', 'Martedì', 'Mercoledì', 'Giovedì', 'Venerdì', 'Sabato'],
          hours: '10:00 - 23:00'
        },
        isAvailable: true
      })
    } finally {
      setLoading(false)
    }
  }

  const handleBooking = () => {
    if (rider) {
      navigate(`/app/booking/${rider.id}`)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
      </div>
    )
  }

  if (!rider) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <p className="text-gray-500">Rider non trovato</p>
      </div>
    )
  }

  const tabs = [
    { key: 'info', icon: Calendar, label: '' },
    { key: 'profile', icon: User, label: '' },
    { key: 'reviews', icon: Star, label: '' },
    { key: 'contact', icon: MessageSquare, label: '' }
  ]

  return (
    <div className="bg-gray-50 min-h-screen">
      {/* Rider Header */}
      <div className="bg-white px-4 py-6 border-b">
        <div className="flex items-center space-x-4">
          <div className="w-16 h-16 bg-gray-300 rounded-full flex items-center justify-center">
            {rider.profileImage ? (
              <img
                src={rider.profileImage}
                alt={rider.name}
                className="w-16 h-16 rounded-full object-cover"
              />
            ) : (
              <span className="text-gray-600 font-medium text-xl">
                {rider.name.charAt(0).toUpperCase()}
              </span>
            )}
          </div>
          
          <div className="flex-1">
            <h1 className="text-xl font-bold text-gray-900">{rider.name}</h1>
            <div className="flex items-center space-x-1 mt-1">
              <Star className="w-4 h-4 text-orange-400 fill-current" />
              <span className="text-sm text-gray-600">{rider.rating.toFixed(1)}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white border-b">
        <div className="flex">
          {tabs.map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key as any)}
                className={`flex-1 py-4 px-2 border-b-2 transition-colors ${
                  activeTab === tab.key
                    ? 'border-purple-600 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                <Icon size={20} className="mx-auto" />
              </button>
            )
          })}
        </div>
      </div>

      {/* Tab Content */}
      <div className="px-4 py-6">
        {activeTab === 'info' && (
          <div className="space-y-6">
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Tariffa</h3>
              <p className="text-2xl font-bold text-purple-600">€{rider.hourlyRate}/ora</p>
            </div>

            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Disponibilità giornaliera</h3>
              <p className="text-gray-600">{rider.availability.days.join(', ')}</p>
            </div>

            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Disponibilità oraria</h3>
              <p className="text-gray-600">{rider.availability.hours}</p>
            </div>

            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Modello del mezzo e attrezzatura</h3>
              <p className="text-gray-600">{rider.vehicleModel}</p>
            </div>

            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Tipo di Veicolo</h3>
              <p className="text-gray-600">{rider.vehicleType}</p>
            </div>
          </div>
        )}

        {activeTab === 'profile' && (
          <div className="space-y-6">
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Informazioni sul fornitore</h3>
              <p className="text-gray-600">{rider.description}</p>
            </div>
          </div>
        )}

        {activeTab === 'reviews' && (
          <div className="space-y-6">
            <div className="text-center py-8">
              <Star className="w-12 h-12 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500">Nessuna recensione ancora</p>
            </div>
          </div>
        )}

        {activeTab === 'contact' && (
          <div className="space-y-6">
            <div className="text-center py-8">
              <MessageSquare className="w-12 h-12 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500">Chat disponibile dopo la prenotazione</p>
            </div>
          </div>
        )}
      </div>

      {/* Booking Button */}
      <div className="fixed bottom-20 left-0 right-0 px-4 py-4 bg-white border-t">
        <button
          onClick={handleBooking}
          className="w-full bg-purple-600 hover:bg-purple-700 text-white font-semibold py-4 rounded-lg transition-colors"
        >
          Verifica disponibilità
        </button>
      </div>
    </div>
  )
}

export default RiderDetailPage
