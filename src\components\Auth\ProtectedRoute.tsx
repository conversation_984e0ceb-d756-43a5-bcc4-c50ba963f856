import React, { ReactNode } from 'react'
import { Navigate } from 'react-router-dom'
import { useAuth } from '../../contexts/AuthContext'

interface ProtectedRouteProps {
  children: ReactNode
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isSignedIn, loading, isProfileComplete } = useAuth()

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600"></div>
      </div>
    )
  }

  if (!isSignedIn) {
    return <Navigate to="/login" replace />
  }

  // Se l'utente è autenticato ma non ha completato il profilo
  if (isSignedIn && !isProfileComplete()) {
    return <Navigate to="/profile-setup" replace />
  }

  console.log('User authenticated and profile complete, showing protected content')
  return <>{children}</>
}

export default ProtectedRoute
