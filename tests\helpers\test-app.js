/**
 * Test App Helper
 * Creates a test instance of the Express app
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import { validateEnvironment, getEnv, getSafeConfig } from '../../server/utils/env-validator.js'

/**
 * Create test Express application
 * @returns {Promise<Express>} Configured Express app for testing
 */
export async function createTestApp() {
  // Validate test environment
  try {
    validateEnvironment()
  } catch (error) {
    console.warn('Environment validation failed in test, using defaults:', error.message)
  }

  const app = express()

  // Security middleware (relaxed for testing)
  app.use(helmet({
    contentSecurityPolicy: false, // Disable for testing
    crossOriginEmbedderPolicy: false
  }))

  // CORS configuration for testing
  const corsOptions = {
    origin: function (origin, callback) {
      // Allow all origins in test environment
      callback(null, true)
    },
    credentials: true,
    optionsSuccessStatus: 200
  }

  app.use(cors(corsOptions))
  app.use(express.json({ limit: '10mb' }))
  app.use(express.urlencoded({ extended: true, limit: '10mb' }))

  // Test middleware to inject mock authentication
  app.use((req, res, next) => {
    // Mock authentication for testing
    const authHeader = req.headers.authorization
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7)
      
      if (token === 'valid_test_token') {
        req.auth = {
          userId: 'test_user_id',
          sessionId: 'test_session_id'
        }
      } else if (token === 'admin_test_token') {
        req.auth = {
          userId: 'admin_user_id',
          sessionId: 'admin_session_id'
        }
      }
    }
    next()
  })

  // Mock middleware functions
  const mockAuthenticateClerk = (req, res, next) => {
    if (!req.auth) {
      return res.status(401).json({
        success: false,
        message: 'Token di autenticazione richiesto'
      })
    }
    next()
  }

  const mockOptionalAuth = (req, res, next) => {
    // Optional auth always passes in tests
    next()
  }

  const mockValidateInput = (rules) => (req, res, next) => {
    const errors = []
    
    // Check required fields
    if (rules.required) {
      rules.required.forEach(field => {
        if (!req.body[field]) {
          errors.push(`${field} è richiesto`)
        }
      })
    }

    // Check email format
    if (rules.email) {
      rules.email.forEach(field => {
        const value = req.body[field]
        if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
          errors.push(`${field} deve essere un email valido`)
        }
      })
    }

    if (errors.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Errori di validazione',
        errors
      })
    }

    next()
  }

  const mockRateLimit = (maxRequests, windowMs) => {
    const requests = new Map()
    
    return (req, res, next) => {
      const key = req.ip || 'test-ip'
      const now = Date.now()
      
      if (!requests.has(key)) {
        requests.set(key, [])
      }
      
      const userRequests = requests.get(key)
      const windowStart = now - windowMs
      
      // Remove old requests
      const recentRequests = userRequests.filter(time => time > windowStart)
      
      if (recentRequests.length >= maxRequests) {
        return res.status(429).json({
          success: false,
          message: 'Troppi tentativi. Riprova più tardi.'
        })
      }
      
      recentRequests.push(now)
      requests.set(key, recentRequests)
      
      next()
    }
  }

  // Mock database operations
  const mockDb = {
    getUserByClerkId: async (clerkId) => {
      if (global.testDb && global.testDb.users) {
        for (const user of global.testDb.users.values()) {
          if (user.clerk_id === clerkId) {
            return user
          }
        }
      }
      return null
    },

    createUser: async (userData) => {
      const user = {
        id: global.generateTestData.uuid(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        ...userData
      }
      
      if (global.testDb) {
        global.testDb.addUser(user)
      }
      
      return user
    },

    updateUser: async (clerkId, updateData) => {
      if (global.testDb && global.testDb.users) {
        for (const [id, user] of global.testDb.users.entries()) {
          if (user.clerk_id === clerkId) {
            const updatedUser = {
              ...user,
              ...updateData,
              updated_at: new Date().toISOString()
            }
            global.testDb.users.set(id, updatedUser)
            return updatedUser
          }
        }
      }
      throw new Error('User not found')
    },

    getRiders: async (filters = {}) => {
      if (!global.testDb || !global.testDb.users) {
        return []
      }

      let riders = Array.from(global.testDb.users.values())
        .filter(user => user.type === 'RIDER')

      // Apply filters
      if (filters.city) {
        riders = riders.filter(rider => rider.city === filters.city)
      }
      if (filters.vehicleType) {
        riders = riders.filter(rider => rider.vehicle_type === filters.vehicleType)
      }
      if (filters.available !== undefined) {
        riders = riders.filter(rider => rider.is_available === filters.available)
      }
      if (filters.minRate) {
        riders = riders.filter(rider => rider.hourly_rate >= filters.minRate)
      }
      if (filters.maxRate) {
        riders = riders.filter(rider => rider.hourly_rate <= filters.maxRate)
      }

      // Sort by rating
      riders.sort((a, b) => (b.average_rating || 0) - (a.average_rating || 0))

      return riders
    }
  }

  // Test routes
  app.get('/api/health', (req, res) => {
    res.json({
      status: 'OK',
      message: 'BeMyRider Test API is running',
      environment: 'test',
      timestamp: new Date().toISOString()
    })
  })

  // Users routes
  app.get('/api/users/profile/:clerkId', mockAuthenticateClerk, async (req, res) => {
    try {
      const { clerkId } = req.params

      // Check authorization
      if (req.auth.userId !== clerkId) {
        return res.status(403).json({
          success: false,
          message: 'Accesso negato: puoi accedere solo al tuo profilo'
        })
      }

      const user = await mockDb.getUserByClerkId(clerkId)
      
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'Utente non trovato'
        })
      }

      // Remove sensitive fields
      const { stripe_customer_id, stripe_account_id, ...safeUser } = user

      res.json({
        success: true,
        user: safeUser
      })
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Errore interno del server'
      })
    }
  })

  app.post('/api/users/sync', 
    mockAuthenticateClerk,
    mockRateLimit(100, 15 * 60 * 1000),
    mockValidateInput({
      required: ['clerkId', 'email', 'userType'],
      email: ['email']
    }),
    async (req, res) => {
      try {
        const { clerkId, email, firstName, lastName, name, userType, profileComplete } = req.body

        // Check authorization
        if (req.auth.userId !== clerkId) {
          return res.status(403).json({
            success: false,
            message: 'Accesso negato: puoi sincronizzare solo i tuoi dati'
          })
        }

        // Check if user exists
        let user = await mockDb.getUserByClerkId(clerkId)
        
        if (user) {
          // Update existing user
          user = await mockDb.updateUser(clerkId, {
            email,
            first_name: firstName,
            last_name: lastName,
            display_name: name,
            type: userType,
            profile_complete: profileComplete
          })
        } else {
          // Create new user
          user = await mockDb.createUser({
            clerk_id: clerkId,
            email,
            first_name: firstName,
            last_name: lastName,
            display_name: name,
            type: userType,
            profile_complete: profileComplete || false,
            email_verified: true,
            account_status: 'ACTIVE'
          })
        }

        res.json({
          success: true,
          message: 'Utente sincronizzato con successo',
          user
        })
      } catch (error) {
        res.status(500).json({
          success: false,
          message: 'Errore nella sincronizzazione'
        })
      }
    }
  )

  app.put('/api/users/profile/:clerkId', mockAuthenticateClerk, async (req, res) => {
    try {
      const { clerkId } = req.params
      const updateData = req.body

      // Check authorization
      if (req.auth.userId !== clerkId) {
        return res.status(403).json({
          success: false,
          message: 'Accesso negato: puoi modificare solo il tuo profilo'
        })
      }

      // Validate phone number
      if (updateData.phone && !/^\+?[1-9]\d{1,14}$/.test(updateData.phone)) {
        return res.status(400).json({
          success: false,
          message: 'Errori di validazione',
          errors: ['Numero di telefono non valido']
        })
      }

      // Validate hourly rate
      if (updateData.hourlyRate && updateData.hourlyRate < 0) {
        return res.status(400).json({
          success: false,
          message: 'Errori di validazione',
          errors: ['La tariffa oraria deve essere positiva']
        })
      }

      // Sanitize input
      const sanitizedData = {}
      Object.entries(updateData).forEach(([key, value]) => {
        if (typeof value === 'string') {
          // Remove dangerous HTML but allow safe formatting
          sanitizedData[key] = value.replace(/<script[^>]*>.*?<\/script>/gi, '')
        } else {
          sanitizedData[key] = value
        }
      })

      const user = await mockDb.updateUser(clerkId, sanitizedData)

      res.json({
        success: true,
        message: 'Profilo aggiornato con successo',
        user
      })
    } catch (error) {
      if (error.message === 'User not found') {
        return res.status(404).json({
          success: false,
          message: 'Utente non trovato'
        })
      }
      
      res.status(500).json({
        success: false,
        message: 'Errore nell\'aggiornamento del profilo'
      })
    }
  })

  app.get('/api/users/riders', mockOptionalAuth, async (req, res) => {
    try {
      const filters = {}
      
      // Parse query parameters
      if (req.query.city) filters.city = req.query.city
      if (req.query.vehicleType) filters.vehicleType = req.query.vehicleType
      if (req.query.available) filters.available = req.query.available === 'true'
      if (req.query.minRate) filters.minRate = parseFloat(req.query.minRate)
      if (req.query.maxRate) filters.maxRate = parseFloat(req.query.maxRate)

      const page = parseInt(req.query.page) || 1
      const limit = parseInt(req.query.limit) || 20

      let riders = await mockDb.getRiders(filters)

      // Add mock statistics
      riders = riders.map(rider => ({
        ...rider,
        averageRating: rider.average_rating || 4.5,
        totalReviews: rider.total_reviews || 10,
        totalBookings: rider.total_bookings || 25
      }))

      // Apply pagination
      const total = riders.length
      const startIndex = (page - 1) * limit
      const endIndex = startIndex + limit
      const paginatedRiders = riders.slice(startIndex, endIndex)

      res.json({
        success: true,
        riders: paginatedRiders,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      })
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Errore nel recupero dei rider'
      })
    }
  })

  // Error handling middleware
  app.use((err, req, res, next) => {
    console.error('Test app error:', err)
    res.status(500).json({
      success: false,
      message: 'Errore interno del server'
    })
  })

  // 404 handler
  app.use('*', (req, res) => {
    res.status(404).json({
      success: false,
      message: 'Endpoint non trovato'
    })
  })

  return app
}
