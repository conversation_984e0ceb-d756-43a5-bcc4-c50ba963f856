/**
 * User Synchronization Hook
 * Gestisce la sincronizzazione tra Clerk e Supabase
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { useState } from 'react'
import { useUser } from '@clerk/clerk-react'

const useUserSync = () => {
  const { user } = useUser()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)

  const syncUserToDatabase = async (userType) => {
    if (!user) {
      throw new Error('User not authenticated')
    }

    setLoading(true)
    setError(null)

    try {
      // Prepara i dati dell'utente secondo il formato dell'API
      const userData = {
        clerkId: user.id,
        email: user.primaryEmailAddress?.emailAddress,
        firstName: user.firstName,
        lastName: user.lastName,
        name: `${user.firstName || ''} ${user.lastName || ''}`.trim(),
        userType: userType.toUpperCase(),
        profileComplete: true
      }

      // Chiama l'API backend per sincronizzare l'utente
      const response = await fetch(`${import.meta.env.VITE_API_URL}/users/sync`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await user.getToken()}`
        },
        body: JSON.stringify(userData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Errore durante la sincronizzazione')
      }

      const result = await response.json()

      // Aggiorna i metadati di Clerk
      await user.update({
        publicMetadata: {
          userType: userType,
          onboardingCompleted: true,
          databaseId: result.user.id
        }
      })

      return result.user

    } catch (err) {
      console.error('Errore sincronizzazione utente:', err)
      setError(err.message)
      throw err
    } finally {
      setLoading(false)
    }
  }

  return {
    syncUserToDatabase,
    loading,
    error
  }
}

export default useUserSync
