/**
 * Booking Repository
 * Data access layer for booking operations
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { supabase } from '../config/database.js'

class BookingRepository {
  /**
   * Create a new booking
   */
  async create(bookingData) {
    const { data, error } = await supabase
      .from('bookings')
      .insert([bookingData])
      .select()
      .single()

    if (error) throw error
    return data
  }

  /**
   * Find booking by ID
   */
  async findById(id) {
    const { data, error } = await supabase
      .from('bookings')
      .select('*')
      .eq('id', id)
      .single()

    if (error) throw error
    return data
  }

  /**
   * Find bookings by rider ID
   */
  async findByRiderId(riderId) {
    const { data, error } = await supabase
      .from('bookings')
      .select('*')
      .eq('rider_id', riderId)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data
  }

  /**
   * Find bookings by merchant ID
   */
  async findByMerchantId(merchantId) {
    const { data, error } = await supabase
      .from('bookings')
      .select('*')
      .eq('merchant_id', merchantId)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data
  }

  /**
   * Update booking
   */
  async update(id, updateData) {
    const { data, error } = await supabase
      .from('bookings')
      .update(updateData)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  }

  /**
   * Delete booking
   */
  async delete(id) {
    const { error } = await supabase
      .from('bookings')
      .delete()
      .eq('id', id)

    if (error) throw error
    return true
  }
}

export default new BookingRepository()
