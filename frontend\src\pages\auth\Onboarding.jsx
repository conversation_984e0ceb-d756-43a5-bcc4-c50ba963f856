/**
 * Onboarding Page
 * User onboarding flow
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useUser } from '@clerk/clerk-react'
import { motion } from 'framer-motion'
import { TruckIcon, BuildingStorefrontIcon } from '@heroicons/react/24/outline'

import Button from '@components/ui/Button'
import Card, { CardContent } from '@components/ui/Card'
import useUserSync from '@hooks/useUserSync'

const OnboardingPage = () => {
  const { user } = useUser()
  const navigate = useNavigate()
  const [selectedType, setSelectedType] = useState(null)
  const { syncUserToDatabase, loading, error } = useUserSync()

  // Controlla se c'è un tipo di utente preselezionato
  useEffect(() => {
    const storedUserType = localStorage.getItem('pendingUserType')

    if (storedUserType) {
      setSelectedType(storedUserType === 'ESERCENTE' ? 'merchant' : 'rider')
      localStorage.removeItem('pendingUserType')
    }
  }, [])

  const userTypes = [
    {
      type: 'rider',
      title: 'Sono un Rider',
      description: 'Voglio offrire i miei servizi di consegna e guadagnare',
      icon: TruckIcon,
      color: 'blue',
      features: [
        'Gestisci la tua disponibilità',
        'Ricevi prenotazioni',
        'Guadagna fino a €12.50/ora',
        'Pagamenti settimanali'
      ]
    },
    {
      type: 'merchant',
      title: 'Sono un Esercente',
      description: 'Ho bisogno di rider per la mia attività',
      icon: BuildingStorefrontIcon,
      color: 'orange',
      features: [
        'Trova rider qualificati',
        'Gestisci la tua flotta',
        'Conformità normativa',
        'Analytics dettagliate'
      ]
    }
  ]

  const handleContinue = async () => {
    if (!selectedType) return

    try {
      // Sincronizza l'utente con il database
      await syncUserToDatabase(selectedType)

      // Navigate to appropriate dashboard
      navigate(selectedType === 'rider' ? '/dashboard/rider/dashboard' : '/dashboard/merchant/dashboard')
    } catch (error) {
      console.error('Onboarding error:', error)
      // Mostra l'errore all'utente se necessario
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-bemyrider-600 to-primary-600 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-4xl"
      >
        <div className="text-center mb-12">
          <div className="w-20 h-20 bg-white rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
            <img
              src="/bemyrider-icona-trasp.svg"
              alt="bemyrider"
              className="h-12 w-12"
            />
          </div>
          <h1 className="text-4xl font-bold text-white mb-4">Benvenuto in <span className="bemyrider-logo">bemyrider</span>!</h1>
          <p className="text-xl text-white/80">Scegli il tipo di account che fa per te</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
          {userTypes.map((userType, index) => {
            const Icon = userType.icon
            const isSelected = selectedType === userType.type

            return (
              <motion.div
                key={userType.type}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 + index * 0.1 }}
              >
                <Card
                  className={`cursor-pointer transition-all duration-300 ${
                    isSelected
                      ? `ring-4 ring-${userType.color}-300 shadow-xl scale-105`
                      : 'hover:shadow-lg hover:scale-102'
                  }`}
                  onClick={() => setSelectedType(userType.type)}
                >
                  <CardContent className="p-8 text-center">
                    <div className={`w-16 h-16 bg-${userType.color}-100 rounded-2xl flex items-center justify-center mx-auto mb-6`}>
                      <Icon className={`w-8 h-8 text-${userType.color}-600`} />
                    </div>
                    
                    <h3 className="text-2xl font-bold text-gray-900 mb-3">
                      {userType.title}
                    </h3>
                    
                    <p className="text-gray-600 mb-6">
                      {userType.description}
                    </p>

                    <div className="space-y-3">
                      {userType.features.map((feature, i) => (
                        <div key={i} className="flex items-center space-x-3">
                          <div className={`w-2 h-2 bg-${userType.color}-500 rounded-full`}></div>
                          <span className="text-sm text-gray-700">{feature}</span>
                        </div>
                      ))}
                    </div>

                    {isSelected && (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        className="mt-6"
                      >
                        <div className={`w-8 h-8 bg-${userType.color}-500 rounded-full flex items-center justify-center mx-auto`}>
                          <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                      </motion.div>
                    )}
                  </CardContent>
                </Card>
              </motion.div>
            )
          })}
        </div>

        <div className="text-center">
          {error && (
            <div className="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
              <p className="text-sm">{error}</p>
            </div>
          )}

          <Button
            variant="bemyrider"
            size="xl"
            onClick={handleContinue}
            disabled={!selectedType || loading}
            loading={loading}
            className="px-12 py-4 text-lg"
          >
            {loading ? 'Configurazione in corso...' : 'Continua'}
          </Button>
        </div>

        <div className="text-center mt-8">
          <p className="text-white/60 text-sm">
            Potrai sempre modificare queste impostazioni dal tuo profilo
          </p>
        </div>
      </motion.div>
    </div>
  )
}

export default OnboardingPage
