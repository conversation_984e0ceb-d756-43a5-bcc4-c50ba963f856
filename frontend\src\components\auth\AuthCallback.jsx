/**
 * Auth Callback Component
 * Gestisce il redirect dopo l'autenticazione Clerk
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useUser } from '@clerk/clerk-react'

const AuthCallback = () => {
  const { user, isLoaded } = useUser()
  const navigate = useNavigate()

  useEffect(() => {
    if (!isLoaded) return

    if (user) {
      // Controlla se l'utente ha completato l'onboarding
      const onboardingCompleted = user.publicMetadata?.onboardingCompleted
      const userType = user.publicMetadata?.userType

      if (onboardingCompleted && userType) {
        // Utente già configurato, vai alla dashboard appropriata
        const dashboardPath = (userType === 'RIDER' || userType === 'rider')
          ? '/dashboard/rider/dashboard'
          : '/dashboard/merchant/dashboard'
        navigate(dashboardPath)
      } else {
        // Nuovo utente o onboarding incompleto, vai all'onboarding
        navigate('/onboarding')
      }
    } else {
      // Non autenticato, reindirizza alla homepage
      navigate('/')
    }
  }, [user, isLoaded, navigate])

  // Loading screen
  return (
    <div className="min-h-screen bg-gradient-to-br from-bemyrider-600 to-primary-600 flex items-center justify-center">
      <div className="text-center">
        <div className="w-20 h-20 bg-white rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
          <img 
            src="/bemyrider-icona-trasp.svg" 
            alt="bemyrider" 
            className="h-12 w-12"
          />
        </div>
        <h1 className="text-2xl font-bold text-white mb-4">
          Accesso in corso...
        </h1>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"></div>
      </div>
    </div>
  )
}

export default AuthCallback
