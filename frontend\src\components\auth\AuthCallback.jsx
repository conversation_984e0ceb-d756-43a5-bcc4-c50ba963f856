/**
 * Auth Callback Component
 * Gestisce il redirect dopo l'autenticazione Clerk
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useUser } from '@clerk/clerk-react'

const AuthCallback = () => {
  const { user, isLoaded } = useUser()
  const navigate = useNavigate()

  useEffect(() => {
    if (!isLoaded) return

    console.log('🔄 AuthCallback - User loaded:', !!user)
    console.log('🔄 AuthCallback - User metadata:', user?.publicMetadata)

    if (user) {
      // Controlla se l'utente ha completato l'onboarding
      const onboardingCompleted = user.publicMetadata?.onboardingCompleted
      const userType = user.publicMetadata?.userType

      console.log('🔄 AuthCallback - Onboarding completed:', onboardingCompleted)
      console.log('🔄 AuthCallback - User type:', userType)

      if (onboardingCompleted && userType) {
        // Utente già configurato, vai alla dashboard appropriata
        const dashboardPath = (userType === 'RIDER' || userType === 'rider')
          ? '/dashboard/rider/dashboard'
          : '/dashboard/merchant/dashboard'
        console.log('✅ AuthCallback - Redirecting to dashboard:', dashboardPath)
        navigate(dashboardPath)
      } else {
        // Nuovo utente o onboarding incompleto, vai all'onboarding post-auth
        console.log('🆕 AuthCallback - Redirecting to post-auth onboarding')
        navigate('/post-auth-onboarding')
      }
    } else {
      // Non autenticato, reindirizza alla homepage
      console.log('❌ AuthCallback - No user, redirecting to home')
      navigate('/')
    }
  }, [user, isLoaded, navigate])

  // Loading screen
  return (
    <div className="min-h-screen bg-gradient-to-br from-bemyrider-600 to-primary-600 flex items-center justify-center">
      <div className="text-center">
        <div className="w-20 h-20 bg-white rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
          <img 
            src="/bemyrider-icona-trasp.svg" 
            alt="bemyrider" 
            className="h-12 w-12"
          />
        </div>
        <h1 className="text-2xl font-bold text-white mb-4">
          Accesso in corso...
        </h1>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"></div>
      </div>
    </div>
  )
}

export default AuthCallback
