{"name": "<PERSON><PERSON><PERSON>r", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "server": "node server/index.js", "start": "node start-local.js", "start:frontend": "cd frontend && npm run dev", "start:backend": "node server/index.js", "test": "vitest", "test:unit": "vitest run tests/unit", "test:integration": "vitest run tests/integration", "test:performance": "vitest run tests/performance", "test:security": "vitest run tests/security", "test:watch": "vitest watch", "test:coverage": "vitest run --coverage", "test:ci": "vitest run --coverage --reporter=verbose --reporter=json --outputFile=test-results/results.json", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write .", "format:check": "prettier --check .", "db:setup": "node scripts/setup-database.js", "db:seed": "node scripts/seed-database.js", "db:reset": "node scripts/reset-database.js", "start:test": "NODE_ENV=test node start-test-server.js", "pretest": "npm run db:setup", "posttest": "npm run db:reset"}, "dependencies": {"@clerk/clerk-react": "^5.32.0", "@clerk/clerk-sdk-node": "^4.13.23", "@stripe/react-stripe-js": "^2.4.0", "@stripe/stripe-js": "^2.2.0", "@supabase/supabase-js": "^2.50.0", "axios": "^1.6.2", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "clsx": "^2.0.0", "compression": "^1.8.0", "date-fns": "^2.30.0", "express-rate-limit": "^7.5.0", "express-slow-down": "^2.1.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "isomorphic-dompurify": "^2.25.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.294.0", "morgan": "^1.10.0", "multer": "^2.0.1", "node-fetch": "^3.3.2", "prom-client": "^15.1.3", "react": "^18.2.0", "react-datepicker": "^4.24.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-router-dom": "^6.20.1", "stripe": "^14.25.0", "uuid": "^11.1.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/supertest": "^6.0.3", "@vitejs/plugin-react": "^4.1.1", "@vitest/ui": "^3.2.3", "autoprefixer": "^10.4.16", "c8": "^10.1.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "jest": "^30.0.0", "jspdf": "^2.5.1", "msw": "^2.10.2", "nodemailer": "^6.9.7", "postcss": "^8.4.32", "supertest": "^7.1.1", "tailwindcss": "^3.3.6", "typescript": "~5.8.3", "vite": "^6.3.5", "vitest": "^3.2.3"}}