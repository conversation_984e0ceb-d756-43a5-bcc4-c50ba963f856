/**
 * BeMyRider Business Model Tests
 * Tests for direct engagement platform model
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { describe, it, expect, beforeEach } from 'vitest'
import { PricingService } from '../../server/services/PricingService.js'
import { ReceiptService } from '../../server/services/ReceiptService.js'

describe('BeMyRider Business Model', () => {
  let pricingService
  let receiptService

  beforeEach(() => {
    pricingService = new PricingService()
    receiptService = new ReceiptService()
  })

  describe('🎯 Direct Engagement Model', () => {
    it('should support rider custom hourly rates up to €12.50', async () => {
      const bookingData = {
        vehicleType: 'MOTO',
        duration: 2,
        customRate: 12.50 // Rider sets their own rate
      }

      const pricing = await pricingService.calculateBookingPrice(bookingData)

      expect(pricing.baseRate).toBe(12.50)
      expect(pricing.baseAmount).toBe(25.00) // €12.50 × 2h
      expect(pricing.platformFee).toBe(3.75) // 15% of €25
      expect(pricing.total).toBe(28.75)
    })

    it('should reject hourly rates above €12.50', async () => {
      const bookingData = {
        vehicleType: 'MOTO',
        duration: 2,
        customRate: 15.00 // Above maximum
      }

      await expect(pricingService.calculateBookingPrice(bookingData))
        .rejects.toThrow('Hourly rate cannot exceed €12.5/hour')
    })

    it('should use default rates when no custom rate is set', async () => {
      const vehicles = [
        { type: 'BICI', expectedRate: 8.00 },
        { type: 'MOTO', expectedRate: 10.00 },
        { type: 'AUTO', expectedRate: 12.00 },
        { type: 'FURGONE', expectedRate: 12.50 }
      ]

      for (const vehicle of vehicles) {
        const pricing = await pricingService.calculateBookingPrice({
          vehicleType: vehicle.type,
          duration: 1
        })

        expect(pricing.baseRate).toBe(vehicle.expectedRate)
      }
    })
  })

  describe('💼 Fleet Management Mode', () => {
    it('should support existing rider relationships', async () => {
      // Simulate existing rider with known rate
      const bookingData = {
        vehicleType: 'MOTO',
        duration: 2,
        riderId: 'existing_rider_123',
        customRate: 11.00 // Known rider rate
      }

      const pricing = await pricingService.calculateBookingPrice(bookingData)

      expect(pricing.baseRate).toBe(11.00)
      expect(pricing.total).toBe(25.30) // €22 + €3.30 platform fee
    })

    it('should handle scheduled shifts for fleet management', async () => {
      const shiftData = {
        riderId: 'fleet_rider_456',
        clientId: 'client_789',
        scheduledDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
        duration: 2,
        hourlyRate: 10.50,
        mode: 'fleet_management'
      }

      // This would be handled by BookingService in fleet mode
      expect(shiftData.mode).toBe('fleet_management')
      expect(shiftData.hourlyRate).toBeLessThanOrEqual(12.50)
    })
  })

  describe('📄 Receipt Generation', () => {
    it('should generate prestazione occasionale receipt', async () => {
      const shiftData = {
        id: 'shift_123',
        rider_id: 'rider_456',
        client_id: 'client_789',
        scheduled_date: '2024-01-15T09:00:00Z',
        actual_start: '2024-01-15T09:00:00Z',
        actual_end: '2024-01-15T11:00:00Z',
        duration: 2,
        actual_duration: 2,
        hourly_rate: 12.00,
        vehicle_type: 'MOTO',
        client_business_name: 'Pizzeria Da Mario'
      }

      const receipt = await receiptService.generateReceipt(shiftData)

      expect(receipt).toHaveProperty('number')
      expect(receipt.number).toMatch(/^BMR\d{4}/)
      expect(receipt.type).toBe('prestazione_occasionale')
      expect(receipt.amounts.gross_amount).toBe(24.00) // €12 × 2h
      expect(receipt.amounts.platform_fee).toBe(3.60) // 15% of €24
      expect(receipt.amounts.net_amount).toBe(20.40) // €24 - €3.60
    })

    it('should include legal compliance notes', async () => {
      const shiftData = {
        id: 'shift_123',
        rider_id: 'rider_456',
        client_id: 'client_789',
        actual_start: '2024-01-15T09:00:00Z',
        actual_end: '2024-01-15T11:00:00Z',
        hourly_rate: 10.00
      }

      const receipt = await receiptService.generateReceipt(shiftData)

      expect(receipt.legal_notes).toContain('Prestazione occasionale ai sensi dell\'art. 2222 del Codice Civile')
      expect(receipt.legal_notes).toContain('Operazione non soggetta ad IVA')
      expect(receipt.platform.name).toBe('BeMyRider')
      expect(receipt.platform.role).toBe('Intermediario tecnologico')
    })

    it('should validate required data for receipt generation', async () => {
      const incompleteShiftData = {
        id: 'shift_123',
        rider_id: 'rider_456'
        // Missing required fields
      }

      await expect(receiptService.generateReceipt(incompleteShiftData))
        .rejects.toThrow('Receipt validation failed')
    })
  })

  describe('💰 Earnings Distribution', () => {
    it('should calculate correct rider earnings (85%)', () => {
      const booking = {
        baseAmount: 25.00, // €12.50 × 2h
        platform_fee: 3.75
      }

      const riderEarnings = pricingService.calculateRiderEarnings(booking)
      
      // Rider gets 85% of base amount: €25 × 0.85 = €21.25
      expect(riderEarnings).toBe(21.25)
    })

    it('should calculate platform revenue (15%)', () => {
      const booking = {
        baseAmount: 25.00,
        platform_fee: 3.75
      }

      const platformRevenue = pricingService.calculatePlatformRevenue(booking)
      
      // Platform gets 15%: €3.75
      expect(platformRevenue).toBe(3.75)
    })
  })

  describe('⏰ Shift Duration Validation', () => {
    it('should enforce 1-2 hour shift duration', async () => {
      // Test minimum duration (1 hour)
      const minShift = {
        vehicleType: 'MOTO',
        duration: 1,
        customRate: 10.00
      }

      const minPricing = await pricingService.calculateBookingPrice(minShift)
      expect(minPricing.duration).toBe(1)

      // Test maximum duration (2 hours)
      const maxShift = {
        vehicleType: 'MOTO',
        duration: 2,
        customRate: 10.00
      }

      const maxPricing = await pricingService.calculateBookingPrice(maxShift)
      expect(maxPricing.duration).toBe(2)

      // Test invalid durations
      await expect(pricingService.calculateBookingPrice({
        vehicleType: 'MOTO',
        duration: 0.5
      })).rejects.toThrow('Duration must be between 1 and 2 hours')

      await expect(pricingService.calculateBookingPrice({
        vehicleType: 'MOTO',
        duration: 3
      })).rejects.toThrow('Duration must be between 1 and 2 hours')
    })
  })

  describe('🏢 Business Use Cases', () => {
    it('should support matching mode for new riders', async () => {
      const matchingScenario = {
        mode: 'matching',
        searchCriteria: {
          location: { latitude: 45.4642, longitude: 9.1900 },
          vehicleType: 'MOTO',
          maxRate: 12.00,
          availability: 'immediate'
        }
      }

      // This would be handled by a RiderMatchingService
      expect(matchingScenario.mode).toBe('matching')
      expect(matchingScenario.searchCriteria.maxRate).toBeLessThanOrEqual(12.50)
    })

    it('should support fleet mode for existing relationships', async () => {
      const fleetScenario = {
        mode: 'fleet',
        existingRiders: ['rider_1', 'rider_2', 'rider_3'],
        scheduledShifts: [
          {
            riderId: 'rider_1',
            date: '2024-01-15',
            time: '09:00',
            duration: 2
          }
        ]
      }

      expect(fleetScenario.mode).toBe('fleet')
      expect(fleetScenario.existingRiders).toHaveLength(3)
    })
  })

  describe('📊 Compliance Features', () => {
    it('should track annual earnings for tax compliance', async () => {
      // Mock annual earnings tracking
      const riderAnnualData = {
        riderId: 'rider_123',
        year: 2024,
        totalEarnings: 4500, // Below €5000 threshold
        receiptsGenerated: 45,
        platformFeePaid: 795
      }

      expect(riderAnnualData.totalEarnings).toBeLessThan(5000) // Below VAT threshold
    })

    it('should maintain 10-year receipt retention', () => {
      const retentionPolicy = {
        years: 10,
        storageType: 'digital',
        backupRequired: true,
        accessLevel: 'restricted'
      }

      expect(retentionPolicy.years).toBe(10)
      expect(retentionPolicy.storageType).toBe('digital')
    })
  })
})
