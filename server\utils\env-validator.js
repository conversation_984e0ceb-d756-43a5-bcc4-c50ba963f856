/**
 * Environment Variables Validator
 * Validates and sanitizes environment variables on server startup
 */

import dotenv from 'dotenv'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../../.env') })

/**
 * Required environment variables with their validation rules
 */
const ENV_SCHEMA = {
  // Supabase Configuration
  VITE_SUPABASE_URL: {
    required: true,
    type: 'url',
    pattern: /^https:\/\/[a-z0-9-]+\.supabase\.co$/,
    description: 'Supabase project URL'
  },
  VITE_SUPABASE_ANON_KEY: {
    required: true,
    type: 'jwt',
    pattern: /^eyJ[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,
    description: 'Supabase anonymous key (JWT token)'
  },

  // Clerk Configuration
  CLERK_SECRET_KEY: {
    required: true,
    type: 'string',
    pattern: /^sk_(test|live)_[A-Za-z0-9]+$/,
    description: 'Clerk secret key'
  },
  VITE_CLERK_PUBLISHABLE_KEY: {
    required: true,
    type: 'string',
    pattern: /^pk_(test|live)_[A-Za-z0-9]+$/,
    description: 'Clerk publishable key'
  },

  // Stripe Configuration (optional for MVP)
  STRIPE_SECRET_KEY: {
    required: false,
    type: 'string',
    pattern: /^sk_(test|live)_[A-Za-z0-9]+$/,
    description: 'Stripe secret key'
  },
  VITE_STRIPE_PUBLISHABLE_KEY: {
    required: false,
    type: 'string',
    pattern: /^pk_(test|live)_[A-Za-z0-9]+$/,
    description: 'Stripe publishable key'
  },

  // Server Configuration
  PORT: {
    required: false,
    type: 'number',
    default: 5000,
    min: 1000,
    max: 65535,
    description: 'Server port number'
  },
  NODE_ENV: {
    required: false,
    type: 'enum',
    values: ['development', 'production', 'test'],
    default: 'development',
    description: 'Node environment'
  },
  FRONTEND_URL: {
    required: false,
    type: 'url',
    default: 'http://localhost:3000',
    description: 'Frontend URL for CORS'
  }
}

/**
 * Validation errors collection
 */
class ValidationError extends Error {
  constructor(errors) {
    super(`Environment validation failed:\n${errors.map(e => `  - ${e}`).join('\n')}`)
    this.name = 'ValidationError'
    this.errors = errors
  }
}

/**
 * Validate a single environment variable
 */
function validateEnvVar(key, schema, value) {
  const errors = []

  // Check if required
  if (schema.required && (!value || value.trim() === '')) {
    errors.push(`${key} is required but not set`)
    return errors
  }

  // If not required and not set, use default
  if (!value && schema.default !== undefined) {
    process.env[key] = schema.default.toString()
    return errors
  }

  // Skip validation if optional and not set
  if (!value && !schema.required) {
    return errors
  }

  // Type validation
  switch (schema.type) {
    case 'string':
      if (typeof value !== 'string') {
        errors.push(`${key} must be a string`)
      }
      break

    case 'number':
      const num = parseInt(value, 10)
      if (isNaN(num)) {
        errors.push(`${key} must be a valid number`)
      } else {
        if (schema.min !== undefined && num < schema.min) {
          errors.push(`${key} must be at least ${schema.min}`)
        }
        if (schema.max !== undefined && num > schema.max) {
          errors.push(`${key} must be at most ${schema.max}`)
        }
        // Convert to number in process.env
        process.env[key] = num.toString()
      }
      break

    case 'url':
      try {
        new URL(value)
      } catch {
        errors.push(`${key} must be a valid URL`)
      }
      break

    case 'jwt':
      if (!value.startsWith('eyJ')) {
        errors.push(`${key} must be a valid JWT token`)
      }
      break

    case 'enum':
      if (!schema.values.includes(value)) {
        errors.push(`${key} must be one of: ${schema.values.join(', ')}`)
      }
      break
  }

  // Pattern validation
  if (schema.pattern && !schema.pattern.test(value)) {
    errors.push(`${key} format is invalid (${schema.description})`)
  }

  return errors
}

/**
 * Validate all environment variables
 */
export function validateEnvironment() {
  console.log('🔍 Validating environment variables...')
  
  const allErrors = []

  // Validate each environment variable
  for (const [key, schema] of Object.entries(ENV_SCHEMA)) {
    const value = process.env[key]
    const errors = validateEnvVar(key, schema, value)
    allErrors.push(...errors)
  }

  // Check for unknown dangerous variables
  const dangerousVars = Object.keys(process.env).filter(key => 
    key.includes('PASSWORD') || 
    key.includes('SECRET') || 
    key.includes('PRIVATE') ||
    key.includes('KEY')
  ).filter(key => !ENV_SCHEMA[key])

  if (dangerousVars.length > 0) {
    console.warn('⚠️  Found potentially sensitive environment variables not in schema:')
    dangerousVars.forEach(key => {
      console.warn(`   - ${key}`)
    })
  }

  // Throw error if validation failed
  if (allErrors.length > 0) {
    throw new ValidationError(allErrors)
  }

  console.log('✅ Environment validation passed')
  
  // Log configuration summary (without sensitive values)
  console.log('📋 Configuration summary:')
  console.log(`   - Environment: ${process.env.NODE_ENV}`)
  console.log(`   - Port: ${process.env.PORT}`)
  console.log(`   - Frontend URL: ${process.env.FRONTEND_URL}`)
  console.log(`   - Supabase URL: ${process.env.VITE_SUPABASE_URL}`)
  console.log(`   - Clerk configured: ${process.env.CLERK_SECRET_KEY ? '✅' : '❌'}`)
  console.log(`   - Stripe configured: ${process.env.STRIPE_SECRET_KEY ? '✅' : '❌'}`)
}

/**
 * Get validated environment variable
 */
export function getEnv(key, defaultValue = undefined) {
  const value = process.env[key]
  if (value === undefined && defaultValue !== undefined) {
    return defaultValue
  }
  return value
}

/**
 * Check if we're in production
 */
export function isProduction() {
  return process.env.NODE_ENV === 'production'
}

/**
 * Check if we're in development
 */
export function isDevelopment() {
  return process.env.NODE_ENV === 'development'
}

/**
 * Get safe config object (without sensitive values)
 */
export function getSafeConfig() {
  return {
    nodeEnv: process.env.NODE_ENV,
    port: process.env.PORT,
    frontendUrl: process.env.FRONTEND_URL,
    supabaseUrl: process.env.VITE_SUPABASE_URL,
    hasClerk: !!process.env.CLERK_SECRET_KEY,
    hasStripe: !!process.env.STRIPE_SECRET_KEY
  }
}
