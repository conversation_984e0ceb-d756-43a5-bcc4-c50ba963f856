import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import { 
  User, 
  Settings, 
  CreditCard, 
  FileText, 
  HelpCircle, 
  LogOut,
  Edit,
  Star,
  Calendar,
  Clock,
  Bike
} from 'lucide-react'

const ProfilePage: React.FC = () => {
  const { user, logout, getUserType } = useAuth()
  const navigate = useNavigate()
  const userType = getUserType()
  const [showLogoutConfirm, setShowLogoutConfirm] = useState(false)

  const handleLogout = () => {
    logout()
    navigate('/')
  }

  const menuItems = [
    {
      icon: Edit,
      label: 'Modifica Profilo',
      action: () => navigate('/app/profile/edit'),
      description: 'Aggiorna le tue informazioni personali'
    },
    {
      icon: CreditCard,
      label: 'Pagamenti',
      action: () => navigate('/app/payments'),
      description: userType === 'RIDER' ? 'Gestisci il tuo account Stripe' : 'Metodi di pagamento'
    },
    {
      icon: Calendar,
      label: userType === 'RIDER' ? 'Disponibilità' : 'Le Mie Prenotazioni',
      action: () => navigate(userType === 'RIDER' ? '/app/availability' : '/app/bookings'),
      description: userType === 'RIDER' ? 'Gestisci i tuoi orari' : 'Visualizza le prenotazioni'
    },
    {
      icon: Star,
      label: 'Recensioni',
      action: () => navigate('/app/reviews'),
      description: 'Visualizza le tue recensioni'
    },
    {
      icon: FileText,
      label: 'Documenti',
      action: () => navigate('/app/documents'),
      description: 'Ricevute e fatture'
    },
    {
      icon: Settings,
      label: 'Impostazioni',
      action: () => navigate('/app/settings'),
      description: 'Preferenze e notifiche'
    },
    {
      icon: HelpCircle,
      label: 'Supporto',
      action: () => navigate('/app/support'),
      description: 'Centro assistenza e FAQ'
    }
  ]

  return (
    <div className="bg-gray-50 min-h-screen">
      {/* Profile Header */}
      <div className="bg-white px-4 py-6 border-b">
        <div className="flex items-center space-x-4">
          <div className="w-20 h-20 bg-purple-100 rounded-full flex items-center justify-center">
            <User className="w-10 h-10 text-purple-600" />
          </div>
          
          <div className="flex-1">
            <h1 className="text-xl font-bold text-gray-900">{user?.fullName || user?.firstName || 'Utente'}</h1>
            <p className="text-gray-600">{user?.emailAddresses?.[0]?.emailAddress}</p>
            <div className="flex items-center space-x-2 mt-2">
              <span className={`inline-block px-3 py-1 rounded-full text-xs font-medium ${
                userType === 'RIDER'
                  ? 'bg-green-100 text-green-800'
                  : 'bg-blue-100 text-blue-800'
              }`}>
                {userType === 'RIDER' ? 'Rider' : 'Esercente'}
              </span>
              {userType === 'RIDER' && (
                <span className="inline-block px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                  <Bike className="w-3 h-3 inline mr-1" />
                  Moto
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        {user?.type === 'RIDER' && (
          <div className="grid grid-cols-3 gap-4 mt-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">4.8</div>
              <div className="text-sm text-gray-600">Rating</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">127</div>
              <div className="text-sm text-gray-600">Servizi</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">€12/h</div>
              <div className="text-sm text-gray-600">Tariffa</div>
            </div>
          </div>
        )}
      </div>

      {/* Menu Items */}
      <div className="px-4 py-6">
        <div className="space-y-2">
          {menuItems.map((item, index) => {
            const Icon = item.icon
            return (
              <button
                key={index}
                onClick={item.action}
                className="w-full bg-white rounded-lg p-4 border hover:shadow-md transition-shadow text-left"
              >
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                    <Icon className="w-5 h-5 text-gray-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900">{item.label}</h3>
                    <p className="text-sm text-gray-600">{item.description}</p>
                  </div>
                  <div className="text-gray-400">
                    →
                  </div>
                </div>
              </button>
            )
          })}
        </div>

        {/* Logout Button */}
        <div className="mt-8">
          <button
            onClick={() => setShowLogoutConfirm(true)}
            className="w-full bg-red-50 hover:bg-red-100 text-red-600 rounded-lg p-4 border border-red-200 transition-colors"
          >
            <div className="flex items-center justify-center space-x-2">
              <LogOut className="w-5 h-5" />
              <span className="font-medium">Esci</span>
            </div>
          </button>
        </div>

        {/* App Info */}
        <div className="mt-8 text-center text-sm text-gray-500">
          <p>bemyrider v1.0.0</p>
          <p className="mt-1">© 2025 bemyrider</p>
        </div>
      </div>

      {/* Logout Confirmation Modal */}
      {showLogoutConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 w-full max-w-sm">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Conferma Logout
            </h3>
            <p className="text-gray-600 mb-6">
              Sei sicuro di voler uscire dall'applicazione?
            </p>
            
            <div className="flex space-x-3">
              <button
                onClick={() => setShowLogoutConfirm(false)}
                className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
              >
                Annulla
              </button>
              <button
                onClick={handleLogout}
                className="flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
              >
                Esci
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ProfilePage
