import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { Bike, Car, Truck, Star, Clock, Shield, Store } from 'lucide-react'

const HomePage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-600 to-purple-900">
      {/* Hero Section */}
      <div className="px-6 pt-12 pb-8 text-white">
        <div className="text-center">
          {/* Logo */}
          <div className="mb-6">
            <img
              src="/bemyrider-icona-trasp.svg"
              alt="bemyrider logo"
              className="w-24 h-24 mx-auto mb-4 bg-transparent"
            />
          </div>

          <h1 className="text-4xl font-bold mb-4 bemyrider-logo text-white">bemyrider</h1>
          <p className="text-xl mb-8 opacity-90">
            Il marketplace che connette esercenti locali e rider autonomi per servizi di consegna a tariffa oraria
          </p>

          <div className="flex justify-center space-x-4 mb-8">
            <Bike className="w-8 h-8 text-orange-500" />
            <Car className="w-8 h-8 text-orange-500" />
            <Truck className="w-8 h-8 text-orange-500" />
          </div>
        </div>
      </div>

      {/* Features */}
      <div className="bg-white rounded-t-3xl px-6 py-8 min-h-screen">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="text-center p-6">
            <div className="bg-orange-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <Clock className="w-8 h-8 text-orange-500" />
            </div>
            <h3 className="font-semibold mb-2">Prenotazione Oraria</h3>
            <p className="text-gray-600 text-sm">
              Prenota rider per 1-2 ore quando ne hai bisogno
            </p>
          </div>

          <div className="text-center p-6">
            <div className="bg-orange-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <Star className="w-8 h-8 text-orange-500" />
            </div>
            <h3 className="font-semibold mb-2">Rider Verificati</h3>
            <p className="text-gray-600 text-sm">
              Tutti i rider sono verificati e recensiti
            </p>
          </div>

          <div className="text-center p-6">
            <div className="bg-orange-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <Shield className="w-8 h-8 text-orange-500" />
            </div>
            <h3 className="font-semibold mb-2">Pagamenti Sicuri</h3>
            <p className="text-gray-600 text-sm">
              Pagamenti gestiti tramite Stripe Connect
            </p>
          </div>
        </div>

        {/* Profile Selection */}
        <div className="space-y-4 mb-8">
          <h2 className="text-xl font-bold text-center mb-2">Scegli il tuo profilo</h2>
          <p className="text-center text-gray-600 text-sm mb-6">
            Seleziona il tipo di account per iniziare la registrazione
          </p>

          <Link
            to="/register?type=esercente"
            className="block w-full bg-white border-2 border-purple-600 rounded-lg p-6 hover:bg-purple-50 transition-colors"
          >
            <div className="text-center">
              <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Store className="w-8 h-8 text-purple-600" />
              </div>
              <div className="font-semibold text-lg mb-2 text-purple-600">Sono un Esercente</div>
              <div className="text-sm text-gray-600">
                Ho un'attività e cerco rider per le consegne
              </div>
            </div>
          </Link>

          <Link
            to="/register?type=rider"
            className="block w-full bg-white border-2 border-purple-600 rounded-lg p-6 hover:bg-purple-50 transition-colors"
          >
            <div className="text-center">
              <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Bike className="w-8 h-8 text-purple-600" />
              </div>
              <div className="font-semibold text-lg mb-2 text-purple-600">Sono un Rider</div>
              <div className="text-sm text-gray-600">
                Offro servizi di consegna per esercenti
              </div>
            </div>
          </Link>
        </div>

        {/* Login Link */}
        <div className="text-center">
          <p className="text-gray-600 mb-4">Hai già un account?</p>
          <Link
            to="/login"
            className="block w-full bg-gray-100 hover:bg-gray-200 text-gray-900 font-semibold py-3 px-6 rounded-lg text-center transition-colors"
          >
            Accedi
          </Link>
        </div>

        {/* Info Section */}
        <div className="mt-12 text-center">
          <h2 className="text-2xl font-bold mb-6">Come Funziona</h2>
          
          <div className="space-y-6">
            <div className="flex items-start space-x-4">
              <div className="bg-orange-500 text-white w-8 h-8 rounded-full flex items-center justify-center font-bold text-sm">
                1
              </div>
              <div className="text-left">
                <h4 className="font-semibold">Registrati</h4>
                <p className="text-gray-600 text-sm">
                  Crea il tuo account come esercente o rider
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-4">
              <div className="bg-orange-500 text-white w-8 h-8 rounded-full flex items-center justify-center font-bold text-sm">
                2
              </div>
              <div className="text-left">
                <h4 className="font-semibold">Trova o Offri Servizi</h4>
                <p className="text-gray-600 text-sm">
                  Esercenti trovano rider, rider impostano disponibilità
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-4">
              <div className="bg-orange-500 text-white w-8 h-8 rounded-full flex items-center justify-center font-bold text-sm">
                3
              </div>
              <div className="text-left">
                <h4 className="font-semibold">Paga e Lavora</h4>
                <p className="text-gray-600 text-sm">
                  Pagamenti sicuri e automatici tramite Stripe
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default HomePage
