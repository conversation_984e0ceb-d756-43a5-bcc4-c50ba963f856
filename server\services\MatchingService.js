/**
 * Smart Matching Service
 * Intelligent rider-client matching algorithm for BeMyRider
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { logger } from '../utils/logger.js'
import { recordError, recordHttpRequest } from '../utils/metrics.js'
import { AuditTrail } from '../utils/compliance.js'
import { AvailabilityService } from './AvailabilityService.js'

// ============================================================================
// MATCHING CONFIGURATION
// ============================================================================

const MATCHING_CONFIG = {
  // Scoring weights (total must equal 100)
  SCORING_WEIGHTS: {
    DISTANCE: 40,      // 40% - Proximity is most important
    RATING: 25,        // 25% - Quality of service
    PRICE: 20,         // 20% - Cost consideration
    AVAILABILITY: 10,  // 10% - Time flexibility
    PREFERENCE: 5      // 5% - Client/rider preferences
  },

  // Distance thresholds (in kilometers)
  DISTANCE: {
    MAX_SEARCH_RADIUS: 15,     // Maximum search radius
    OPTIMAL_RADIUS: 5,         // Optimal distance for best score
    PENALTY_THRESHOLD: 10      // Distance where penalty starts
  },

  // Rating thresholds
  RATING: {
    MIN_RATING: 3.0,          // Minimum acceptable rating
    EXCELLENT_THRESHOLD: 4.5,  // Excellent rating threshold
    GOOD_THRESHOLD: 4.0       // Good rating threshold
  },

  // Price considerations
  PRICE: {
    MARKET_RATE: 12.00,       // Market average rate
    BUDGET_FRIENDLY: 10.00,   // Budget-friendly threshold
    PREMIUM_THRESHOLD: 12.50  // Premium rate threshold
  },

  // Matching strategies
  STRATEGIES: {
    DISTANCE_FIRST: 'distance_first',     // Prioritize closest riders
    RATING_FIRST: 'rating_first',         // Prioritize highest rated
    PRICE_FIRST: 'price_first',           // Prioritize lowest price
    BALANCED: 'balanced',                 // Balanced scoring
    CLIENT_PREFERENCE: 'client_preference' // Based on client history
  },

  // Fallback options
  FALLBACK: {
    EXPAND_RADIUS: true,      // Expand search radius if no matches
    RELAX_RATING: true,       // Lower rating requirements
    SUGGEST_ALTERNATIVES: true // Suggest alternative times
  }
}

// ============================================================================
// SMART MATCHING SERVICE CLASS
// ============================================================================

export class MatchingService {
  constructor() {
    this.availabilityService = new AvailabilityService()
  }

  /**
   * Find best rider matches for booking request
   * @param {Object} bookingRequest - Booking request details
   * @param {Object} options - Matching options
   * @returns {Promise<Array>} Ranked list of rider matches
   */
  async findBestMatches(bookingRequest, options = {}) {
    try {
      logger.info('Finding best rider matches', { 
        bookingId: bookingRequest.id,
        location: bookingRequest.location,
        startTime: bookingRequest.startTime
      })

      const startTime = Date.now()

      // Extract search criteria
      const searchCriteria = this.extractSearchCriteria(bookingRequest)

      // Get available riders
      const availableRiders = await this.availabilityService.findAvailableRiders(searchCriteria)

      if (availableRiders.length === 0) {
        return await this.handleNoMatches(bookingRequest, options)
      }

      // Apply matching strategy
      const strategy = options.strategy || MATCHING_CONFIG.STRATEGIES.BALANCED
      const rankedRiders = await this.applyMatchingStrategy(
        availableRiders,
        bookingRequest,
        strategy
      )

      // Apply filters and limits
      const filteredRiders = this.applyFilters(rankedRiders, bookingRequest, options)
      const finalMatches = filteredRiders.slice(0, options.maxResults || 10)

      // Record metrics
      const processingTime = Date.now() - startTime
      recordMetric('matching_processing_time', processingTime)
      recordMetric('matching_candidates_found', availableRiders.length)
      recordMetric('matching_final_matches', finalMatches.length)

      // Log audit trail
      await AuditTrail.logUserAction(
        'MATCHING_PERFORMED',
        bookingRequest.clientId,
        'matching',
        {
          bookingId: bookingRequest.id,
          strategy,
          candidatesFound: availableRiders.length,
          finalMatches: finalMatches.length,
          processingTime
        }
      )

      logger.info('Matching completed', {
        bookingId: bookingRequest.id,
        candidatesFound: availableRiders.length,
        finalMatches: finalMatches.length,
        processingTime
      })

      return finalMatches

    } catch (error) {
      logger.error('Failed to find matches', { 
        error: error.message,
        bookingId: bookingRequest.id 
      })
      recordError('matching_find_matches', 'error')
      throw error
    }
  }

  /**
   * Calculate match score for rider-booking pair
   * @param {Object} rider - Rider data
   * @param {Object} bookingRequest - Booking request
   * @returns {Object} Detailed scoring breakdown
   */
  calculateMatchScore(rider, bookingRequest) {
    const scores = {}
    let totalScore = 0

    // Distance Score (40%)
    scores.distance = this.calculateDistanceScore(rider.distance)
    totalScore += scores.distance * (MATCHING_CONFIG.SCORING_WEIGHTS.DISTANCE / 100)

    // Rating Score (25%)
    scores.rating = this.calculateRatingScore(rider.rating)
    totalScore += scores.rating * (MATCHING_CONFIG.SCORING_WEIGHTS.RATING / 100)

    // Price Score (20%)
    scores.price = this.calculatePriceScore(rider.hourlyRate, bookingRequest.budget)
    totalScore += scores.price * (MATCHING_CONFIG.SCORING_WEIGHTS.PRICE / 100)

    // Availability Score (10%)
    scores.availability = this.calculateAvailabilityScore(rider, bookingRequest)
    totalScore += scores.availability * (MATCHING_CONFIG.SCORING_WEIGHTS.AVAILABILITY / 100)

    // Preference Score (5%)
    scores.preference = this.calculatePreferenceScore(rider, bookingRequest)
    totalScore += scores.preference * (MATCHING_CONFIG.SCORING_WEIGHTS.PREFERENCE / 100)

    return {
      totalScore: Math.round(totalScore * 100) / 100,
      breakdown: scores,
      weights: MATCHING_CONFIG.SCORING_WEIGHTS
    }
  }

  /**
   * Apply matching strategy to rank riders
   * @param {Array} riders - Available riders
   * @param {Object} bookingRequest - Booking request
   * @param {string} strategy - Matching strategy
   * @returns {Promise<Array>} Ranked riders
   */
  async applyMatchingStrategy(riders, bookingRequest, strategy) {
    const scoredRiders = riders.map(rider => {
      const scoring = this.calculateMatchScore(rider, bookingRequest)
      return {
        ...rider,
        matchScore: scoring.totalScore,
        scoreBreakdown: scoring.breakdown,
        matchReason: this.generateMatchReason(scoring, strategy)
      }
    })

    // Sort based on strategy
    switch (strategy) {
      case MATCHING_CONFIG.STRATEGIES.DISTANCE_FIRST:
        return scoredRiders.sort((a, b) => a.distance - b.distance)

      case MATCHING_CONFIG.STRATEGIES.RATING_FIRST:
        return scoredRiders.sort((a, b) => b.rating - a.rating)

      case MATCHING_CONFIG.STRATEGIES.PRICE_FIRST:
        return scoredRiders.sort((a, b) => a.hourlyRate - b.hourlyRate)

      case MATCHING_CONFIG.STRATEGIES.CLIENT_PREFERENCE:
        return await this.applyClientPreferences(scoredRiders, bookingRequest)

      case MATCHING_CONFIG.STRATEGIES.BALANCED:
      default:
        return scoredRiders.sort((a, b) => b.matchScore - a.matchScore)
    }
  }

  /**
   * Handle case when no matches are found
   * @param {Object} bookingRequest - Booking request
   * @param {Object} options - Options
   * @returns {Promise<Array>} Alternative suggestions
   */
  async handleNoMatches(bookingRequest, options) {
    logger.info('No matches found, applying fallback strategies', {
      bookingId: bookingRequest.id
    })

    const suggestions = []

    if (MATCHING_CONFIG.FALLBACK.EXPAND_RADIUS) {
      // Try with expanded radius
      const expandedCriteria = {
        ...this.extractSearchCriteria(bookingRequest),
        maxDistance: MATCHING_CONFIG.DISTANCE.MAX_SEARCH_RADIUS * 1.5
      }

      const expandedRiders = await this.availabilityService.findAvailableRiders(expandedCriteria)
      if (expandedRiders.length > 0) {
        suggestions.push({
          type: 'expanded_radius',
          message: 'Riders available with expanded search area',
          riders: expandedRiders.slice(0, 3)
        })
      }
    }

    if (MATCHING_CONFIG.FALLBACK.SUGGEST_ALTERNATIVES) {
      // Suggest alternative time slots
      const alternatives = await this.findAlternativeTimeSlots(bookingRequest)
      if (alternatives.length > 0) {
        suggestions.push({
          type: 'alternative_times',
          message: 'Alternative time slots available',
          alternatives
        })
      }
    }

    return suggestions
  }

  // ============================================================================
  // SCORING METHODS
  // ============================================================================

  /**
   * Calculate distance score (0-100)
   */
  calculateDistanceScore(distance) {
    if (distance <= MATCHING_CONFIG.DISTANCE.OPTIMAL_RADIUS) {
      return 100
    }

    if (distance >= MATCHING_CONFIG.DISTANCE.MAX_SEARCH_RADIUS) {
      return 0
    }

    // Linear decay from optimal to max radius
    const decayFactor = (MATCHING_CONFIG.DISTANCE.MAX_SEARCH_RADIUS - distance) /
                       (MATCHING_CONFIG.DISTANCE.MAX_SEARCH_RADIUS - MATCHING_CONFIG.DISTANCE.OPTIMAL_RADIUS)

    return Math.max(0, Math.round(decayFactor * 100))
  }

  /**
   * Calculate rating score (0-100)
   */
  calculateRatingScore(rating) {
    if (rating >= MATCHING_CONFIG.RATING.EXCELLENT_THRESHOLD) {
      return 100
    }

    if (rating >= MATCHING_CONFIG.RATING.GOOD_THRESHOLD) {
      return 80
    }

    if (rating >= MATCHING_CONFIG.RATING.MIN_RATING) {
      return 60
    }

    return 0
  }

  /**
   * Calculate price score (0-100)
   */
  calculatePriceScore(hourlyRate, clientBudget) {
    // If client has no budget preference, use market rate
    const targetRate = clientBudget || MATCHING_CONFIG.PRICE.MARKET_RATE

    if (hourlyRate <= targetRate) {
      // Reward rates at or below target
      return 100
    }

    // Penalty for rates above target
    const penalty = (hourlyRate - targetRate) / targetRate
    return Math.max(0, Math.round((1 - penalty) * 100))
  }

  /**
   * Calculate availability score (0-100)
   */
  calculateAvailabilityScore(rider, bookingRequest) {
    // Score based on how well rider's availability matches request
    // For now, return high score if rider is available
    return rider.available ? 100 : 0
  }

  /**
   * Calculate preference score (0-100)
   */
  calculatePreferenceScore(rider, bookingRequest) {
    let score = 50 // Base score

    // Vehicle type preference
    if (bookingRequest.preferredVehicleType && 
        rider.vehicleType === bookingRequest.preferredVehicleType) {
      score += 30
    }

    // Previous positive interactions
    if (rider.previousInteractions && rider.previousInteractions > 0) {
      score += 20
    }

    return Math.min(100, score)
  }

  // ============================================================================
  // HELPER METHODS
  // ============================================================================

  /**
   * Extract search criteria from booking request
   */
  extractSearchCriteria(bookingRequest) {
    return {
      startTime: bookingRequest.startTime,
      endTime: bookingRequest.endTime,
      location: bookingRequest.location,
      vehicleType: bookingRequest.vehicleType,
      maxDistance: MATCHING_CONFIG.DISTANCE.MAX_SEARCH_RADIUS,
      minRating: MATCHING_CONFIG.RATING.MIN_RATING
    }
  }

  /**
   * Apply filters to rider list
   */
  applyFilters(riders, bookingRequest, options) {
    return riders.filter(rider => {
      // Distance filter
      if (rider.distance > (options.maxDistance || MATCHING_CONFIG.DISTANCE.MAX_SEARCH_RADIUS)) {
        return false
      }

      // Rating filter
      if (rider.rating < (options.minRating || MATCHING_CONFIG.RATING.MIN_RATING)) {
        return false
      }

      // Budget filter
      if (bookingRequest.maxBudget && rider.hourlyRate > bookingRequest.maxBudget) {
        return false
      }

      // Vehicle type filter
      if (bookingRequest.requiredVehicleType && 
          rider.vehicleType !== bookingRequest.requiredVehicleType) {
        return false
      }

      return true
    })
  }

  /**
   * Generate match reason explanation
   */
  generateMatchReason(scoring, strategy) {
    const reasons = []

    if (scoring.breakdown.distance >= 80) {
      reasons.push('Very close to your location')
    }

    if (scoring.breakdown.rating >= 90) {
      reasons.push('Excellent rating from previous clients')
    }

    if (scoring.breakdown.price >= 80) {
      reasons.push('Great value for money')
    }

    if (reasons.length === 0) {
      reasons.push('Good overall match for your request')
    }

    return reasons.join(', ')
  }

  /**
   * Apply client-specific preferences
   */
  async applyClientPreferences(riders, bookingRequest) {
    // Get client's historical preferences
    const clientPreferences = await this.getClientPreferences(bookingRequest.clientId)

    // Adjust scores based on preferences
    const adjustedRiders = riders.map(rider => {
      let adjustedScore = rider.matchScore

      // Preferred vehicle type
      if (clientPreferences.preferredVehicleType === rider.vehicleType) {
        adjustedScore += 5
      }

      // Previous positive interactions
      if (clientPreferences.preferredRiders?.includes(rider.id)) {
        adjustedScore += 10
      }

      // Preferred price range
      if (rider.hourlyRate >= clientPreferences.minPrice && 
          rider.hourlyRate <= clientPreferences.maxPrice) {
        adjustedScore += 3
      }

      return {
        ...rider,
        matchScore: Math.min(100, adjustedScore)
      }
    })

    return adjustedRiders.sort((a, b) => b.matchScore - a.matchScore)
  }

  /**
   * Find alternative time slots
   */
  async findAlternativeTimeSlots(bookingRequest) {
    // Mock implementation - suggest nearby time slots
    const alternatives = []
    const requestTime = new Date(bookingRequest.startTime)

    // Suggest 1 hour earlier
    const earlierTime = new Date(requestTime.getTime() - 60 * 60 * 1000)
    alternatives.push({
      startTime: earlierTime.toISOString(),
      endTime: new Date(earlierTime.getTime() + 60 * 60 * 1000).toISOString(),
      reason: '1 hour earlier'
    })

    // Suggest 1 hour later
    const laterTime = new Date(requestTime.getTime() + 60 * 60 * 1000)
    alternatives.push({
      startTime: laterTime.toISOString(),
      endTime: new Date(laterTime.getTime() + 60 * 60 * 1000).toISOString(),
      reason: '1 hour later'
    })

    return alternatives
  }

  /**
   * Get client preferences
   */
  async getClientPreferences(clientId) {
    try {
      // In a real implementation, this would query the database
      const preferences = await this.queryClientPreferences(clientId)

      return {
        preferredVehicleType: preferences.preferred_vehicle_type || null,
        preferredRiders: preferences.preferred_riders || [],
        blockedRiders: preferences.blocked_riders || [],
        minPrice: preferences.min_price || 0,
        maxPrice: preferences.max_price || 15.00,
        maxDistance: preferences.max_distance || MATCHING_CONFIG.DISTANCE.MAX_SEARCH_RADIUS,
        minRating: preferences.min_rating || MATCHING_CONFIG.RATING.MIN_RATING,
        autoAcceptMatches: preferences.auto_accept_matches || false,
        notificationPreferences: preferences.notification_preferences || {}
      }
    } catch (error) {
      logger.error('Failed to get client preferences', { error: error.message, clientId })

      // Return default preferences
      return {
        preferredVehicleType: null,
        preferredRiders: [],
        blockedRiders: [],
        minPrice: 0,
        maxPrice: 15.00,
        maxDistance: MATCHING_CONFIG.DISTANCE.MAX_SEARCH_RADIUS,
        minRating: MATCHING_CONFIG.RATING.MIN_RATING,
        autoAcceptMatches: false,
        notificationPreferences: {}
      }
    }
  }

  /**
   * Update client preferences based on booking history
   */
  async updateClientPreferences(clientId, bookingData) {
    try {
      logger.info('Updating client preferences', { clientId, bookingId: bookingData.id })

      const currentPreferences = await this.getClientPreferences(clientId)
      const updatedPreferences = { ...currentPreferences }

      // Update preferred riders based on positive ratings
      if (bookingData.rating >= 4.5 && bookingData.riderId) {
        if (!updatedPreferences.preferredRiders.includes(bookingData.riderId)) {
          updatedPreferences.preferredRiders.push(bookingData.riderId)
        }
      }

      // Update blocked riders based on negative ratings
      if (bookingData.rating <= 2.0 && bookingData.riderId) {
        if (!updatedPreferences.blockedRiders.includes(bookingData.riderId)) {
          updatedPreferences.blockedRiders.push(bookingData.riderId)
        }
      }

      // Update vehicle type preference
      if (bookingData.rating >= 4.0 && bookingData.vehicleType) {
        updatedPreferences.preferredVehicleType = bookingData.vehicleType
      }

      // Update price range based on successful bookings
      if (bookingData.rating >= 4.0) {
        const paidRate = bookingData.hourlyRate
        if (paidRate < updatedPreferences.maxPrice) {
          updatedPreferences.maxPrice = Math.max(paidRate + 1, updatedPreferences.maxPrice * 0.9)
        }
      }

      await this.saveClientPreferences(clientId, updatedPreferences)

      logger.info('Client preferences updated', { clientId })
      return updatedPreferences

    } catch (error) {
      logger.error('Failed to update client preferences', { error: error.message, clientId })
      throw error
    }
  }

  /**
   * Get rider preferences and constraints
   */
  async getRiderPreferences(riderId) {
    try {
      const preferences = await this.queryRiderPreferences(riderId)

      return {
        preferredAreas: preferences.preferred_areas || [],
        blockedClients: preferences.blocked_clients || [],
        minBookingDuration: preferences.min_booking_duration || 1,
        maxBookingDuration: preferences.max_booking_duration || 2,
        preferredTimeSlots: preferences.preferred_time_slots || [],
        autoAcceptBookings: preferences.auto_accept_bookings || false,
        maxDailyBookings: preferences.max_daily_bookings || 8,
        restDayPreferences: preferences.rest_day_preferences || []
      }
    } catch (error) {
      logger.error('Failed to get rider preferences', { error: error.message, riderId })

      // Return default preferences
      return {
        preferredAreas: [],
        blockedClients: [],
        minBookingDuration: 1,
        maxBookingDuration: 2,
        preferredTimeSlots: [],
        autoAcceptBookings: false,
        maxDailyBookings: 8,
        restDayPreferences: []
      }
    }
  }

  /**
   * Calculate compatibility score between client and rider
   */
  async calculateCompatibilityScore(clientId, riderId) {
    try {
      const clientPrefs = await this.getClientPreferences(clientId)
      const riderPrefs = await this.getRiderPreferences(riderId)

      let compatibilityScore = 50 // Base score

      // Check if rider is in client's preferred list
      if (clientPrefs.preferredRiders.includes(riderId)) {
        compatibilityScore += 30
      }

      // Check if client is in rider's blocked list
      if (riderPrefs.blockedClients.includes(clientId)) {
        compatibilityScore = 0
      }

      // Check if rider is in client's blocked list
      if (clientPrefs.blockedRiders.includes(riderId)) {
        compatibilityScore = 0
      }

      // Historical interaction bonus
      const interactionHistory = await this.getInteractionHistory(clientId, riderId)
      if (interactionHistory.averageRating >= 4.5) {
        compatibilityScore += 20
      } else if (interactionHistory.averageRating >= 4.0) {
        compatibilityScore += 10
      }

      return Math.min(100, Math.max(0, compatibilityScore))

    } catch (error) {
      logger.error('Failed to calculate compatibility score', {
        error: error.message,
        clientId,
        riderId
      })
      return 50 // Default neutral score
    }
  }

  // ============================================================================
  // MOCK DATABASE OPERATIONS
  // ============================================================================

  async queryClientPreferences(clientId) {
    // Mock implementation
    return {
      preferred_vehicle_type: 'MOTO',
      preferred_riders: [],
      blocked_riders: [],
      min_price: 8.00,
      max_price: 12.50,
      max_distance: 10,
      min_rating: 4.0,
      auto_accept_matches: false
    }
  }

  async saveClientPreferences(clientId, preferences) {
    // Mock implementation
    logger.info('Client preferences saved', { clientId, preferences })
  }

  async queryRiderPreferences(riderId) {
    // Mock implementation
    return {
      preferred_areas: ['Milano Centro', 'Porta Garibaldi'],
      blocked_clients: [],
      min_booking_duration: 1,
      max_booking_duration: 2,
      auto_accept_bookings: false,
      max_daily_bookings: 6
    }
  }

  async getInteractionHistory(clientId, riderId) {
    // Mock implementation
    return {
      totalBookings: 3,
      averageRating: 4.7,
      lastInteraction: new Date('2024-12-01').toISOString()
    }
  }
}

export default MatchingService
