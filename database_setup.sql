-- BeMyRider Database Setup
-- Execute these commands in Supabase SQL Editor

-- 1. Add rider availability columns to users table
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS is_online BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS work_schedule J<PERSON>N<PERSON> DEFAULT '{"monday": [], "tuesday": [], "wednesday": [], "thursday": [], "friday": [], "saturday": [], "sunday": []}',
ADD COLUMN IF NOT EXISTS last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- 2. Create bookings table
CREATE TABLE IF NOT EXISTS bookings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  client_id UUID REFERENCES users(id) ON DELETE CASCADE,
  rider_id UUID REFERENCES users(id) ON DELETE CASCADE,
  date TIMESTAMP WITH TIME ZONE NOT NULL,
  duration INTEGER NOT NULL, -- in hours
  address TEXT NOT NULL,
  instructions TEXT,
  hourly_rate DECIMAL(10,2) NOT NULL,
  total_amount DECIMAL(10,2) NOT NULL,
  platform_fee DECIMAL(10,2) DEFAULT 0,
  status VARCHAR(20) DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'CONFIRMED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED')),
  stripe_payment_intent_id VARCHAR(255),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_is_online ON users(is_online) WHERE type = 'RIDER';
CREATE INDEX IF NOT EXISTS idx_users_last_seen ON users(last_seen) WHERE type = 'RIDER';
CREATE INDEX IF NOT EXISTS idx_bookings_client_id ON bookings(client_id);
CREATE INDEX IF NOT EXISTS idx_bookings_rider_id ON bookings(rider_id);
CREATE INDEX IF NOT EXISTS idx_bookings_status ON bookings(status);
CREATE INDEX IF NOT EXISTS idx_bookings_date ON bookings(date);

-- 4. Enable Row Level Security
ALTER TABLE bookings ENABLE ROW LEVEL SECURITY;

-- 5. Create RLS policies for bookings (drop existing ones first to avoid conflicts)
DROP POLICY IF EXISTS "Users can view their own bookings" ON bookings;
DROP POLICY IF EXISTS "Users can insert their own bookings" ON bookings;
DROP POLICY IF EXISTS "Users can update their own bookings" ON bookings;

CREATE POLICY "Users can view their own bookings"
ON bookings FOR SELECT
USING (client_id = auth.uid() OR rider_id = auth.uid());

CREATE POLICY "Users can insert their own bookings"
ON bookings FOR INSERT
WITH CHECK (client_id = auth.uid());

CREATE POLICY "Users can update their own bookings"
ON bookings FOR UPDATE
USING (client_id = auth.uid() OR rider_id = auth.uid());

-- 6. Update existing riders with default availability values
UPDATE users 
SET 
  is_online = COALESCE(is_online, false),
  work_schedule = COALESCE(work_schedule, '{"monday": [], "tuesday": [], "wednesday": [], "thursday": [], "friday": [], "saturday": [], "sunday": []}'),
  last_seen = COALESCE(last_seen, NOW())
WHERE type = 'RIDER';

-- 7. Add comments for documentation
COMMENT ON COLUMN users.is_online IS 'Indicates if rider is currently online and available for bookings';
COMMENT ON COLUMN users.work_schedule IS 'JSON object containing weekly work schedule with time slots for each day';
COMMENT ON COLUMN users.last_seen IS 'Timestamp of when rider was last active on the platform';

COMMENT ON TABLE bookings IS 'Stores booking requests and their status between clients and riders';
COMMENT ON COLUMN bookings.duration IS 'Booking duration in hours';
COMMENT ON COLUMN bookings.status IS 'Booking status: PENDING, CONFIRMED, IN_PROGRESS, COMPLETED, CANCELLED';
COMMENT ON COLUMN bookings.platform_fee IS 'Platform commission fee (usually 15% of total_amount)';

-- 8. Create a function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 9. Create trigger for bookings table (drop existing first)
DROP TRIGGER IF EXISTS update_bookings_updated_at ON bookings;
CREATE TRIGGER update_bookings_updated_at
BEFORE UPDATE ON bookings
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 10. Insert some sample data for testing (optional)
-- Uncomment the following lines if you want sample bookings

/*
INSERT INTO bookings (client_id, rider_id, date, duration, address, hourly_rate, total_amount, platform_fee, status)
SELECT 
  c.id as client_id,
  r.id as rider_id,
  NOW() + INTERVAL '1 day' as date,
  2 as duration,
  'Via Roma 123, Milano' as address,
  15.00 as hourly_rate,
  30.00 as total_amount,
  4.50 as platform_fee,
  'PENDING' as status
FROM users c
CROSS JOIN users r
WHERE c.type = 'ESERCENTE' AND r.type = 'RIDER'
LIMIT 3;
*/
