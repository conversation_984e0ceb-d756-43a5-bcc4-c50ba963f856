import React, { createContext, useContext, ReactNode } from 'react'
import { loadStripe } from '@stripe/stripe-js'
import { Elements } from '@stripe/react-stripe-js'

// For MVP, we'll use a placeholder or disable Stripe if no key is provided
const stripePublishableKey = import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY

// Only load Stripe if we have a valid publishable key (starts with pk_)
const stripePromise = stripePublishableKey && stripePublishableKey.startsWith('pk_')
  ? loadStripe(stripePublishableKey)
  : null

interface StripeContextType {
  stripe: typeof stripePromise
}

const StripeContext = createContext<StripeContextType | undefined>(undefined)

export const useStripe = () => {
  const context = useContext(StripeContext)
  if (context === undefined) {
    throw new Error('useStripe must be used within a StripeProvider')
  }
  return context
}

interface StripeProviderProps {
  children: ReactNode
}

export const StripeProvider: React.FC<StripeProviderProps> = ({ children }) => {
  const value: StripeContextType = {
    stripe: stripePromise
  }

  // If Stripe is not configured, just provide the context without Elements wrapper
  if (!stripePromise) {
    console.warn('⚠️ Stripe not configured - payments will be disabled')
    return (
      <StripeContext.Provider value={value}>
        {children}
      </StripeContext.Provider>
    )
  }

  return (
    <StripeContext.Provider value={value}>
      <Elements stripe={stripePromise}>
        {children}
      </Elements>
    </StripeContext.Provider>
  )
}
