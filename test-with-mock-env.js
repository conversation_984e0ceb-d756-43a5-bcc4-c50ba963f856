/**
 * Test with Mock Environment Variables
 * Tests the system using mock environment variables
 */

import dotenv from 'dotenv'
import { validateEnvironment, getSafeConfig } from './server/utils/env-validator.js'

// Load test environment variables
dotenv.config({ path: '.env.test' })

async function testWithMockEnv() {
  console.log('🧪 Testing with mock environment variables...')
  
  try {
    // Test environment validation
    console.log('🔍 Testing environment validation...')
    validateEnvironment()
    console.log('✅ Environment validation passed with mock values')
    
    // Test configuration
    console.log('\n📋 Testing configuration retrieval...')
    const config = getSafeConfig()
    console.log('Configuration:', JSON.stringify(config, null, 2))
    
    // Verify all required fields are present
    const requiredFields = ['nodeEnv', 'port', 'frontendUrl', 'supabaseUrl', 'hasClerk']
    const missingFields = requiredFields.filter(field => config[field] === undefined)
    
    if (missingFields.length === 0) {
      console.log('✅ All required configuration fields present')
    } else {
      console.log('❌ Missing configuration fields:', missingFields)
    }
    
    // Test environment variable access
    console.log('\n🔑 Testing environment variable access...')
    const testVars = [
      'VITE_SUPABASE_URL',
      'VITE_SUPABASE_ANON_KEY',
      'CLERK_SECRET_KEY',
      'VITE_CLERK_PUBLISHABLE_KEY',
      'PORT',
      'NODE_ENV'
    ]
    
    testVars.forEach(varName => {
      const value = process.env[varName]
      if (value) {
        console.log(`✅ ${varName}: ${varName.includes('KEY') ? '***' + value.slice(-8) : value}`)
      } else {
        console.log(`❌ ${varName}: Not set`)
      }
    })
    
    console.log('\n🎉 Mock environment test completed successfully!')
    
  } catch (error) {
    console.error('❌ Test failed:', error.message)
    if (error.errors) {
      error.errors.forEach(err => console.error(`   - ${err}`))
    }
  }
}

testWithMockEnv()
