/**
 * Fleet Management Page
 * Manage merchant fleet and bulk operations
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { useState } from 'react'
import { motion } from 'framer-motion'
import {
  TruckIcon,
  UserGroupIcon,
  ChartBarIcon,
  ClockIcon,
  StarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  PlusIcon,
  AdjustmentsHorizontalIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline'

import Card, { CardHeader, CardTitle, CardContent } from '@components/ui/Card'
import Button from '@components/ui/Button'
import Header from '@components/layout/Header'

const mockFleetData = {
  overview: {
    totalRiders: 8,
    activeRiders: 6,
    availableRiders: 3,
    busyRiders: 3,
    offlineRiders: 2,
    averageRating: 4.7,
    utilizationRate: 75,
    complianceScore: 88
  },
  riders: [
    {
      id: 1,
      name: '<PERSON>',
      status: 'active',
      rating: 4.8,
      vehicleType: 'MOTO',
      totalShifts: 156,
      thisWeekShifts: 12,
      earnings: 1872.50,
      lastActive: '2024-12-22T14:30:00Z',
      compliance: {
        documentsValid: true,
        licenseExpiry: '2025-01-15',
        insuranceExpiry: '2025-06-30'
      }
    },
    {
      id: 2,
      name: '<PERSON> <PERSON>',
      status: 'busy',
      rating: 4.6,
      vehicleType: 'BICI',
      totalShifts: 89,
      thisWeekShifts: 8,
      earnings: 1067.50,
      lastActive: '2024-12-22T15:00:00Z',
      compliance: {
        documentsValid: true,
        licenseExpiry: '2025-03-20',
        insuranceExpiry: '2024-12-31'
      }
    },
    {
      id: 3,
      name: 'Giuseppe Bianchi',
      status: 'available',
      rating: 4.9,
      vehicleType: 'MOTO',
      totalShifts: 234,
      thisWeekShifts: 15,
      earnings: 2808.00,
      lastActive: '2024-12-22T13:45:00Z',
      compliance: {
        documentsValid: false,
        licenseExpiry: '2024-12-25',
        insuranceExpiry: '2025-04-15'
      }
    }
  ],
  alerts: [
    {
      id: 1,
      type: 'warning',
      message: 'Patente di Giuseppe Bianchi scade tra 3 giorni',
      riderId: 3,
      priority: 'high'
    },
    {
      id: 2,
      type: 'warning',
      message: 'Assicurazione di Luigi Verdi scade tra 9 giorni',
      riderId: 2,
      priority: 'medium'
    }
  ]
}

const MerchantFleet = () => {
  const [selectedTab, setSelectedTab] = useState('overview')
  const [selectedRiders, setSelectedRiders] = useState([])
  const [showBulkActions, setShowBulkActions] = useState(false)

  const getStatusIcon = (status) => {
    switch (status) {
      case 'active':
        return <CheckCircleIcon className="w-5 h-5 text-green-600" />
      case 'busy':
        return <ClockIcon className="w-5 h-5 text-yellow-600" />
      case 'available':
        return <CheckCircleIcon className="w-5 h-5 text-blue-600" />
      case 'offline':
        return <ExclamationTriangleIcon className="w-5 h-5 text-gray-600" />
      default:
        return <ExclamationTriangleIcon className="w-5 h-5 text-gray-600" />
    }
  }

  const getStatusLabel = (status) => {
    switch (status) {
      case 'active': return 'In Turno'
      case 'busy': return 'Occupato'
      case 'available': return 'Disponibile'
      case 'offline': return 'Offline'
      default: return 'Sconosciuto'
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'status-success'
      case 'busy': return 'status-warning'
      case 'available': return 'status-info'
      case 'offline': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const handleRiderSelect = (riderId) => {
    setSelectedRiders(prev => 
      prev.includes(riderId) 
        ? prev.filter(id => id !== riderId)
        : [...prev, riderId]
    )
  }

  const handleBulkAction = (action) => {
    console.log(`Bulk action ${action} for riders:`, selectedRiders)
    setSelectedRiders([])
    setShowBulkActions(false)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header 
        title="Gestione Flotta"
        subtitle="Amministra i tuoi rider e monitora le performance"
        actions={
          <div className="flex space-x-3">
            <Button variant="outline" size="sm" leftIcon={<DocumentTextIcon className="w-4 h-4" />}>
              Report Flotta
            </Button>
            <Button variant="merchant" size="sm" leftIcon={<PlusIcon className="w-4 h-4" />}>
              Aggiungi Rider
            </Button>
          </div>
        }
      />

      <div className="p-6 space-y-6">
        {/* Fleet Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
          >
            <Card className="metric-card card-hover">
              <div className="flex items-center justify-between">
                <div>
                  <p className="metric-label">Rider Totali</p>
                  <p className="metric-value">{mockFleetData.overview.totalRiders}</p>
                  <p className="metric-change metric-change-positive">
                    {mockFleetData.overview.activeRiders} attivi
                  </p>
                </div>
                <div className="p-3 bg-blue-100 rounded-full">
                  <UserGroupIcon className="w-6 h-6 text-blue-600" />
                </div>
              </div>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Card className="metric-card card-hover">
              <div className="flex items-center justify-between">
                <div>
                  <p className="metric-label">Utilizzo Flotta</p>
                  <p className="metric-value">{mockFleetData.overview.utilizationRate}%</p>
                  <p className="metric-change metric-change-positive">
                    +5% dal mese scorso
                  </p>
                </div>
                <div className="p-3 bg-green-100 rounded-full">
                  <ChartBarIcon className="w-6 h-6 text-green-600" />
                </div>
              </div>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card className="metric-card card-hover">
              <div className="flex items-center justify-between">
                <div>
                  <p className="metric-label">Rating Medio</p>
                  <p className="metric-value">{mockFleetData.overview.averageRating}</p>
                  <p className="metric-change metric-change-positive">
                    Eccellente qualità
                  </p>
                </div>
                <div className="p-3 bg-yellow-100 rounded-full">
                  <StarIcon className="w-6 h-6 text-yellow-600" />
                </div>
              </div>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <Card className="metric-card card-hover">
              <div className="flex items-center justify-between">
                <div>
                  <p className="metric-label">Conformità</p>
                  <p className="metric-value">{mockFleetData.overview.complianceScore}%</p>
                  <p className="metric-change metric-change-positive">
                    2 avvisi attivi
                  </p>
                </div>
                <div className="p-3 bg-purple-100 rounded-full">
                  <ExclamationTriangleIcon className="w-6 h-6 text-purple-600" />
                </div>
              </div>
            </Card>
          </motion.div>
        </div>

        {/* Alerts */}
        {mockFleetData.alerts.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <ExclamationTriangleIcon className="w-5 h-5 text-orange-600" />
                  <span>Avvisi Flotta</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {mockFleetData.alerts.map((alert, index) => (
                    <motion.div
                      key={alert.id}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.6 + index * 0.1 }}
                      className={`p-4 rounded-lg border ${
                        alert.priority === 'high' ? 'bg-red-50 border-red-200' : 'bg-orange-50 border-orange-200'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <p className={`font-medium ${
                          alert.priority === 'high' ? 'text-red-800' : 'text-orange-800'
                        }`}>
                          {alert.message}
                        </p>
                        <Button variant="outline" size="sm">
                          Risolvi
                        </Button>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}

        {/* Riders List */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
        >
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center space-x-2">
                  <TruckIcon className="w-5 h-5 text-blue-600" />
                  <span>I Tuoi Rider</span>
                </CardTitle>
                <div className="flex items-center space-x-2">
                  {selectedRiders.length > 0 && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowBulkActions(!showBulkActions)}
                      leftIcon={<AdjustmentsHorizontalIcon className="w-4 h-4" />}
                    >
                      Azioni Multiple ({selectedRiders.length})
                    </Button>
                  )}
                  <Button variant="outline" size="sm">
                    Filtra
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {/* Bulk Actions */}
              {showBulkActions && selectedRiders.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg"
                >
                  <div className="flex items-center space-x-3">
                    <span className="text-sm font-medium text-blue-800">
                      {selectedRiders.length} rider selezionati
                    </span>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm" onClick={() => handleBulkAction('schedule')}>
                        Programma Turni
                      </Button>
                      <Button variant="outline" size="sm" onClick={() => handleBulkAction('message')}>
                        Invia Messaggio
                      </Button>
                      <Button variant="outline" size="sm" onClick={() => handleBulkAction('export')}>
                        Esporta Dati
                      </Button>
                    </div>
                  </div>
                </motion.div>
              )}

              <div className="space-y-4">
                {mockFleetData.riders.map((rider, index) => (
                  <motion.div
                    key={rider.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.8 + index * 0.1 }}
                    className={`p-6 border rounded-lg transition-all ${
                      selectedRiders.includes(rider.id) ? 'border-blue-300 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <input
                          type="checkbox"
                          checked={selectedRiders.includes(rider.id)}
                          onChange={() => handleRiderSelect(rider.id)}
                          className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                        />
                        <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                          <span className="text-white font-bold text-lg">
                            {rider.name.split(' ').map(n => n[0]).join('')}
                          </span>
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <h3 className="text-lg font-semibold text-gray-900">{rider.name}</h3>
                            {getStatusIcon(rider.status)}
                            <span className={`status-badge ${getStatusColor(rider.status)}`}>
                              {getStatusLabel(rider.status)}
                            </span>
                            <div className="flex items-center space-x-1">
                              <StarIcon className="w-4 h-4 text-yellow-500 fill-current" />
                              <span className="text-sm font-medium">{rider.rating}</span>
                            </div>
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm text-gray-600">
                            <div>
                              <span className="font-medium">Veicolo:</span> {rider.vehicleType}
                            </div>
                            <div>
                              <span className="font-medium">Turni totali:</span> {rider.totalShifts}
                            </div>
                            <div>
                              <span className="font-medium">Questa settimana:</span> {rider.thisWeekShifts}
                            </div>
                            <div>
                              <span className="font-medium">Guadagni:</span> €{rider.earnings.toFixed(2)}
                            </div>
                          </div>
                          {!rider.compliance.documentsValid && (
                            <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
                              ⚠️ Documenti non conformi - Azione richiesta
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button variant="outline" size="sm">
                          Dettagli
                        </Button>
                        <Button variant="outline" size="sm">
                          Programma
                        </Button>
                        <Button variant="ghost" size="sm">
                          Messaggio
                        </Button>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}

export default MerchantFleet
