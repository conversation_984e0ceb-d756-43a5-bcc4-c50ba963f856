/**
 * Security Testing Suite
 * Comprehensive security tests for BeMyRider
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import request from 'supertest'
import { createTestApp } from '../helpers/test-app.js'

describe('Security Tests', () => {
  let app

  beforeAll(async () => {
    app = await createTestApp()
  })

  afterAll(async () => {
    // Cleanup
  })

  describe('🔒 Authentication Security', () => {
    it('should reject requests without authentication token', async () => {
      const response = await request(app)
        .get('/api/users/profile/test_user')
        .expect(401)

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('autenticazione')
      })
    })

    it('should reject requests with invalid authentication token', async () => {
      const response = await request(app)
        .get('/api/users/profile/test_user')
        .set('Authorization', 'Bearer invalid_token')
        .expect(401)

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('autenticazione')
      })
    })

    it('should reject requests with malformed authorization header', async () => {
      const response = await request(app)
        .get('/api/users/profile/test_user')
        .set('Authorization', 'InvalidFormat token')
        .expect(401)

      expect(response.body.success).toBe(false)
    })

    it('should prevent access to other users data', async () => {
      const response = await request(app)
        .get('/api/users/profile/other_user_id')
        .set('Authorization', 'Bearer valid_test_token')
        .expect(403)

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('accedere solo al tuo profilo')
      })
    })
  })

  describe('🛡️ Input Validation & Sanitization', () => {
    it('should reject SQL injection attempts', async () => {
      const maliciousInputs = [
        "'; DROP TABLE users; --",
        "1' OR '1'='1",
        "admin'/*",
        "1; DELETE FROM users WHERE 1=1; --"
      ]

      for (const input of maliciousInputs) {
        const response = await request(app)
          .post('/api/users/sync')
          .set('Authorization', 'Bearer valid_test_token')
          .send({
            clerkId: 'test_user_id',
            email: input,
            userType: 'RIDER'
          })
          .expect(400)

        expect(response.body.success).toBe(false)
      }
    })

    it('should reject XSS attempts', async () => {
      const xssPayloads = [
        '<script>alert("xss")</script>',
        '<img src="x" onerror="alert(1)">',
        'javascript:alert("xss")',
        '<svg onload="alert(1)">',
        '"><script>alert("xss")</script>'
      ]

      for (const payload of xssPayloads) {
        const response = await request(app)
          .put('/api/users/profile/test_user_id')
          .set('Authorization', 'Bearer valid_test_token')
          .send({
            firstName: payload,
            lastName: 'Test'
          })

        // Should either reject or sanitize
        if (response.status === 200) {
          expect(response.body.user.firstName).not.toContain('<script>')
        } else {
          expect(response.status).toBe(400)
        }
      }
    })

    it('should validate email format strictly', async () => {
      const invalidEmails = [
        'invalid-email',
        '@domain.com',
        'user@',
        '<EMAIL>',
        'user@domain',
        'user@.com'
      ]

      for (const email of invalidEmails) {
        const response = await request(app)
          .post('/api/users/sync')
          .set('Authorization', 'Bearer valid_test_token')
          .send({
            clerkId: 'test_user_id',
            email,
            userType: 'RIDER'
          })
          .expect(400)

        expect(response.body.errors).toContain(
          expect.stringContaining('email')
        )
      }
    })

    it('should reject oversized requests', async () => {
      const largeData = 'x'.repeat(11 * 1024 * 1024) // 11MB

      const response = await request(app)
        .post('/api/users/sync')
        .set('Authorization', 'Bearer valid_test_token')
        .send({
          clerkId: 'test_user_id',
          email: '<EMAIL>',
          userType: 'RIDER',
          description: largeData
        })
        .expect(413)

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('troppo grande')
      })
    })

    it('should validate UUID format for IDs', async () => {
      const invalidUUIDs = [
        'invalid-uuid',
        '123',
        'not-a-uuid-at-all',
        '12345678-1234-1234-1234-12345678901'
      ]

      for (const uuid of invalidUUIDs) {
        const response = await request(app)
          .get(`/api/users/profile/${uuid}`)
          .set('Authorization', 'Bearer valid_test_token')

        expect(response.status).toBeGreaterThanOrEqual(400)
      }
    })
  })

  describe('🚦 Rate Limiting', () => {
    it('should enforce rate limits on authentication endpoints', async () => {
      const requests = []
      
      // Make multiple rapid requests
      for (let i = 0; i < 10; i++) {
        requests.push(
          request(app)
            .post('/api/users/sync')
            .set('Authorization', 'Bearer valid_test_token')
            .send({
              clerkId: 'test_user_id',
              email: '<EMAIL>',
              userType: 'RIDER'
            })
        )
      }

      const responses = await Promise.all(requests)
      const rateLimitedResponses = responses.filter(r => r.status === 429)

      expect(rateLimitedResponses.length).toBeGreaterThan(0)
    })

    it('should include rate limit headers', async () => {
      const response = await request(app)
        .get('/api/health')

      expect(response.headers).toHaveProperty('x-ratelimit-limit')
      expect(response.headers).toHaveProperty('x-ratelimit-remaining')
    })

    it('should reset rate limits after window expires', async () => {
      // This test would need to wait for the rate limit window
      // In a real scenario, you might mock the time or use shorter windows for testing
      expect(true).toBe(true) // Placeholder
    })
  })

  describe('🔐 Security Headers', () => {
    it('should include security headers', async () => {
      const response = await request(app)
        .get('/api/health')

      // Check for important security headers
      expect(response.headers).toHaveProperty('x-content-type-options', 'nosniff')
      expect(response.headers).toHaveProperty('x-frame-options', 'DENY')
      expect(response.headers).toHaveProperty('x-xss-protection')
      expect(response.headers).toHaveProperty('strict-transport-security')
    })

    it('should include CORS headers correctly', async () => {
      const response = await request(app)
        .options('/api/health')
        .set('Origin', 'http://localhost:3000')

      expect(response.headers).toHaveProperty('access-control-allow-origin')
      expect(response.headers).toHaveProperty('access-control-allow-methods')
    })

    it('should include Content Security Policy', async () => {
      const response = await request(app)
        .get('/api/health')

      expect(response.headers).toHaveProperty('content-security-policy')
    })
  })

  describe('🔍 Information Disclosure', () => {
    it('should not expose sensitive information in error messages', async () => {
      const response = await request(app)
        .get('/api/users/profile/nonexistent_user')
        .set('Authorization', 'Bearer valid_test_token')
        .expect(404)

      // Should not reveal internal details
      expect(response.body.message).not.toContain('database')
      expect(response.body.message).not.toContain('SQL')
      expect(response.body.message).not.toContain('internal')
    })

    it('should not expose stack traces in production', async () => {
      // Mock production environment
      const originalEnv = process.env.NODE_ENV
      process.env.NODE_ENV = 'production'

      const response = await request(app)
        .get('/api/nonexistent-endpoint')
        .expect(404)

      expect(response.body).not.toHaveProperty('stack')
      expect(response.body).not.toHaveProperty('trace')

      // Restore environment
      process.env.NODE_ENV = originalEnv
    })

    it('should not expose server information', async () => {
      const response = await request(app)
        .get('/api/health')

      expect(response.headers).not.toHaveProperty('server')
      expect(response.headers).not.toHaveProperty('x-powered-by')
    })
  })

  describe('🔒 Session Security', () => {
    it('should generate secure session tokens', async () => {
      // Test session token generation
      const { generateSessionToken } = await import('../../server/utils/encryption.js')
      
      const token1 = generateSessionToken()
      const token2 = generateSessionToken()

      expect(token1).not.toBe(token2)
      expect(token1.length).toBeGreaterThan(32)
      expect(token2.length).toBeGreaterThan(32)
    })

    it('should use secure comparison for tokens', async () => {
      const { secureCompare } = await import('../../server/utils/encryption.js')
      
      const token = 'secure_token_123'
      
      expect(secureCompare(token, token)).toBe(true)
      expect(secureCompare(token, 'different_token')).toBe(false)
      expect(secureCompare(token, '')).toBe(false)
      expect(secureCompare('', token)).toBe(false)
    })
  })

  describe('🔐 Data Encryption', () => {
    it('should encrypt and decrypt data correctly', async () => {
      const { encrypt, decrypt } = await import('../../server/utils/encryption.js')
      
      const originalData = 'sensitive information'
      const encrypted = encrypt(originalData)
      const decrypted = decrypt(encrypted)

      expect(encrypted).not.toBe(originalData)
      expect(decrypted).toBe(originalData)
    })

    it('should fail decryption with tampered data', async () => {
      const { encrypt, decrypt } = await import('../../server/utils/encryption.js')
      
      const originalData = 'sensitive information'
      const encrypted = encrypt(originalData)
      const tamperedData = encrypted.slice(0, -5) + 'xxxxx'

      expect(() => decrypt(tamperedData)).toThrow()
    })

    it('should mask sensitive data correctly', async () => {
      const { maskEmail, maskPhone, maskCardNumber } = await import('../../server/utils/encryption.js')
      
      expect(maskEmail('<EMAIL>')).toBe('u***@example.com')
      expect(maskPhone('+39 333 1234567')).toBe('*******4567')
      expect(maskCardNumber('1234567890123456')).toBe('************3456')
    })
  })

  describe('🚨 Security Monitoring', () => {
    it('should detect suspicious patterns in requests', async () => {
      const suspiciousPatterns = [
        '../../../etc/passwd',
        'eval(malicious_code)',
        'javascript:alert(1)',
        'vbscript:msgbox(1)'
      ]

      for (const pattern of suspiciousPatterns) {
        const response = await request(app)
          .post('/api/users/sync')
          .set('Authorization', 'Bearer valid_test_token')
          .send({
            clerkId: 'test_user_id',
            email: '<EMAIL>',
            userType: 'RIDER',
            description: pattern
          })
          .expect(400)

        expect(response.body).toMatchObject({
          success: false,
          message: expect.stringContaining('sospetto')
        })
      }
    })

    it('should log security events', async () => {
      // This would typically check log files or monitoring systems
      // For now, we'll just verify the function exists
      const { logSecurityEvent } = await import('../../server/utils/logger.js')
      
      expect(typeof logSecurityEvent).toBe('function')
    })
  })

  describe('🔍 Vulnerability Scanning', () => {
    it('should not be vulnerable to path traversal', async () => {
      const pathTraversalAttempts = [
        '../../../etc/passwd',
        '..\\..\\..\\windows\\system32\\config\\sam',
        '....//....//....//etc/passwd',
        '%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd'
      ]

      for (const attempt of pathTraversalAttempts) {
        const response = await request(app)
          .get(`/api/files/${attempt}`)
          .set('Authorization', 'Bearer valid_test_token')

        expect(response.status).toBeGreaterThanOrEqual(400)
      }
    })

    it('should not be vulnerable to command injection', async () => {
      const commandInjectionAttempts = [
        '; ls -la',
        '| cat /etc/passwd',
        '&& rm -rf /',
        '`whoami`',
        '$(id)'
      ]

      for (const attempt of commandInjectionAttempts) {
        const response = await request(app)
          .post('/api/users/sync')
          .set('Authorization', 'Bearer valid_test_token')
          .send({
            clerkId: 'test_user_id',
            email: '<EMAIL>',
            userType: 'RIDER',
            firstName: attempt
          })

        // Should either reject or sanitize
        expect(response.status).toBeLessThan(500)
      }
    })

    it('should validate file upload security', async () => {
      // Test file upload restrictions
      const maliciousFiles = [
        { name: 'malware.exe', type: 'application/x-executable' },
        { name: 'script.php', type: 'application/x-php' },
        { name: 'shell.sh', type: 'application/x-sh' }
      ]

      for (const file of maliciousFiles) {
        const response = await request(app)
          .post('/api/upload')
          .set('Authorization', 'Bearer valid_test_token')
          .attach('file', Buffer.from('malicious content'), file.name)

        expect(response.status).toBeGreaterThanOrEqual(400)
      }
    })
  })

  describe('🔐 HTTPS and Transport Security', () => {
    it('should enforce HTTPS in production', async () => {
      // This would typically test HTTPS redirect
      // In a test environment, we verify the security headers
      const response = await request(app)
        .get('/api/health')

      expect(response.headers['strict-transport-security']).toBeDefined()
    })

    it('should use secure cookies', async () => {
      // Test cookie security attributes
      const response = await request(app)
        .post('/api/auth/login')
        .send({ username: 'test', password: 'test' })

      const cookies = response.headers['set-cookie']
      if (cookies) {
        cookies.forEach(cookie => {
          expect(cookie).toMatch(/HttpOnly/i)
          expect(cookie).toMatch(/Secure/i)
          expect(cookie).toMatch(/SameSite/i)
        })
      }
    })
  })
})
