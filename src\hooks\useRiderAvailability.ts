import { useState, useEffect } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { apiService } from '../services/api'
import toast from 'react-hot-toast'

interface TimeSlot {
  start: string // HH:MM format
  end: string   // HH:MM format
}

interface WorkSchedule {
  monday: TimeSlot[]
  tuesday: TimeSlot[]
  wednesday: TimeSlot[]
  thursday: TimeSlot[]
  friday: TimeSlot[]
  saturday: TimeSlot[]
  sunday: TimeSlot[]
}

interface RiderAvailabilityData {
  isOnline: boolean
  workSchedule: WorkSchedule
  lastSeen: string | null
  loading: boolean
  error: string | null
}

const defaultSchedule: WorkSchedule = {
  monday: [],
  tuesday: [],
  wednesday: [],
  thursday: [],
  friday: [],
  saturday: [],
  sunday: []
}

export const useRiderAvailability = () => {
  const { user, getUserType } = useAuth()
  const userType = getUserType()

  const [data, setData] = useState<RiderAvailabilityData>({
    isOnline: false,
    workSchedule: defaultSchedule,
    lastSeen: null,
    loading: false, // Changed to false for immediate loading
    error: null
  })

  // Toggle online/offline status with localStorage persistence
  const toggleAvailability = async () => {
    console.log('🔄 Toggle availability called')

    if (!user?.id) {
      toast.error('User not found')
      return false
    }

    try {
      const newIsOnline = !data.isOnline
      const newData = {
        ...data,
        isOnline: newIsOnline,
        lastSeen: new Date().toISOString()
      }

      // Save to localStorage
      localStorage.setItem(`rider_data_${user.id}`, JSON.stringify(newData))

      setData(newData)

      toast.success(newIsOnline ? 'Sei ora online e disponibile' : 'Sei ora offline')
      console.log('✅ Availability toggled to:', newIsOnline, 'and saved to localStorage')

      return true
    } catch (error: any) {
      console.error('❌ Error toggling availability:', error)
      toast.error('Errore nell\'aggiornamento della disponibilità')
      return false
    }
  }

  // Update work schedule with localStorage persistence
  const updateSchedule = async (newSchedule: WorkSchedule) => {
    console.log('🔄 Update schedule called')

    if (!user?.id) {
      toast.error('User not found')
      return false
    }

    try {
      const newData = {
        ...data,
        workSchedule: newSchedule
      }

      // Save to localStorage
      localStorage.setItem(`rider_data_${user.id}`, JSON.stringify(newData))

      setData(newData)

      toast.success('Orari di lavoro aggiornati con successo')
      console.log('✅ Schedule updated and saved to localStorage:', newSchedule)

      return true
    } catch (error: any) {
      console.error('❌ Error updating schedule:', error)
      toast.error('Errore nell\'aggiornamento degli orari')
      return false
    }
  }

  // Load data from localStorage on initialization
  useEffect(() => {
    console.log('🔄 useRiderAvailability initialized for user:', user?.id, 'type:', userType)

    if (userType === 'RIDER' && user?.id) {
      try {
        // Try to load saved data from localStorage
        const savedData = localStorage.getItem(`rider_data_${user.id}`)

        if (savedData) {
          const parsedData = JSON.parse(savedData)
          console.log('✅ Loaded saved rider data from localStorage:', parsedData)

          setData(prev => ({
            ...prev,
            isOnline: parsedData.isOnline || false,
            workSchedule: parsedData.workSchedule || defaultSchedule,
            lastSeen: parsedData.lastSeen,
            loading: false
          }))
        } else {
          console.log('📝 No saved data found, using defaults')
          setData(prev => ({
            ...prev,
            loading: false
          }))
        }
      } catch (error) {
        console.error('❌ Error loading saved data:', error)
        setData(prev => ({
          ...prev,
          loading: false
        }))
      }
    } else {
      setData(prev => ({
        ...prev,
        loading: false
      }))
    }
  }, [user?.id, userType])

  return {
    ...data,
    toggleAvailability,
    updateSchedule,
    isRider: userType === 'RIDER'
  }
}
