import { useState, useEffect } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { apiService } from '../services/api'
import toast from 'react-hot-toast'

interface DashboardStats {
  totalBookings: number
  activeBookings: number
  totalEarnings?: number
  averageRating?: number
  totalReviews?: number
  totalSpent?: number
  completedBookings?: number
}

interface DashboardData {
  stats: DashboardStats | null
  recentBookings: any[]
  earnings: any | null
  activity: any[]
  loading: boolean
  error: string | null
}

export const useDashboard = () => {
  const { user } = useAuth()
  const [data, setData] = useState<DashboardData>({
    stats: null,
    recentBookings: [],
    earnings: null,
    activity: [],
    loading: true,
    error: null
  })

  const fetchDashboardData = async () => {
    if (!user?.id) {
      setData(prev => ({ ...prev, loading: false, error: 'User not found' }))
      return
    }

    try {
      setData(prev => ({ ...prev, loading: true, error: null }))

      // Fetch all dashboard data in parallel
      const [statsResponse, bookingsResponse, activityResponse] = await Promise.allSettled([
        apiService.getDashboardStats(user.id),
        apiService.getRecentBookings(user.id, 5),
        apiService.getActivity(user.id, 10)
      ])

      // Process stats
      let stats = null
      if (statsResponse.status === 'fulfilled' && statsResponse.value.success) {
        stats = statsResponse.value.data || null
      }

      // Process recent bookings
      let recentBookings: any[] = []
      if (bookingsResponse.status === 'fulfilled' && bookingsResponse.value.success) {
        recentBookings = bookingsResponse.value.data || []
      }

      // Process activity
      let activity: any[] = []
      if (activityResponse.status === 'fulfilled' && activityResponse.value.success) {
        activity = activityResponse.value.data || []
      }

      // Fetch earnings only for riders
      let earnings = null
      const userType = user.unsafeMetadata?.userType
      if (userType === 'RIDER') {
        try {
          const earningsResponse = await apiService.getEarnings(user.id, 'month')
          if (earningsResponse.success) {
            earnings = earningsResponse.data
          }
        } catch (error) {
          console.warn('Failed to fetch earnings:', error)
        }
      }

      setData({
        stats,
        recentBookings,
        earnings,
        activity,
        loading: false,
        error: null
      })

    } catch (error: any) {
      console.error('Error fetching dashboard data:', error)
      setData(prev => ({
        ...prev,
        loading: false,
        error: error.message || 'Failed to load dashboard data'
      }))
      toast.error('Errore nel caricamento dei dati della dashboard')
    }
  }

  const refreshData = () => {
    fetchDashboardData()
  }

  // Mock data fallback for development
  const getMockData = (): DashboardData => {
    const userType = user?.unsafeMetadata?.userType
    
    if (userType === 'RIDER') {
      return {
        stats: {
          totalBookings: 25,
          activeBookings: 3,
          totalEarnings: 1250.50,
          averageRating: 4.7,
          totalReviews: 18
        },
        recentBookings: [
          {
            id: '1',
            client: { name: 'Mario Rossi' },
            date: new Date().toISOString(),
            status: 'CONFIRMED',
            totalAmount: 45.00,
            address: 'Via Roma 123, Milano'
          },
          {
            id: '2',
            client: { name: 'Lucia Bianchi' },
            date: new Date(Date.now() - 86400000).toISOString(),
            status: 'COMPLETED',
            totalAmount: 30.00,
            address: 'Corso Venezia 45, Milano'
          }
        ],
        earnings: {
          period: 'month',
          totalEarnings: 1250.50,
          completedBookings: 22,
          pendingAmount: 120.00,
          pendingCount: 3
        },
        activity: [
          {
            type: 'booking',
            title: 'Nuova prenotazione',
            description: 'Prenotazione da Mario Rossi',
            date: new Date().toISOString(),
            status: 'CONFIRMED'
          },
          {
            type: 'review',
            title: 'Nuova recensione',
            description: '5 stelle - Servizio eccellente!',
            date: new Date(Date.now() - 3600000).toISOString(),
            rating: 5
          }
        ],
        loading: false,
        error: null
      }
    } else {
      return {
        stats: {
          totalBookings: 12,
          activeBookings: 2,
          totalSpent: 680.00,
          completedBookings: 10
        },
        recentBookings: [
          {
            id: '1',
            rider: { name: 'Paolo Cimino', vehicleType: 'MOTO' },
            date: new Date().toISOString(),
            status: 'IN_PROGRESS',
            totalAmount: 35.00,
            address: 'Via Dante 50, Milano'
          }
        ],
        earnings: null,
        activity: [
          {
            type: 'booking',
            title: 'Prenotazione confermata',
            description: 'Prenotazione con Paolo Cimino',
            date: new Date().toISOString(),
            status: 'CONFIRMED'
          }
        ],
        loading: false,
        error: null
      }
    }
  }

  useEffect(() => {
    if (user) {
      // For now, use mock data since the dashboard API endpoints need to be implemented
      // fetchDashboardData()
      
      // Use mock data temporarily
      const mockData = getMockData()
      setData(mockData)
    }
  }, [user])

  return {
    ...data,
    refreshData,
    userType: user?.unsafeMetadata?.userType as 'ESERCENTE' | 'RIDER' | undefined
  }
}

export default useDashboard
