import React, { useEffect } from 'react'
import { Link, useSearchParams, useNavigate } from 'react-router-dom'
import { ArrowLeft, Store, Bike } from 'lucide-react'
import { SignUp } from '@clerk/clerk-react'

const RegisterPage: React.FC = () => {
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const userType = searchParams.get('type') // 'esercente' o 'rider'

  // Reindirizza alla home se non c'è un tipo specificato
  useEffect(() => {
    if (!userType) {
      // Piccolo delay per mostrare il messaggio prima del redirect
      const timer = setTimeout(() => {
        navigate('/', { replace: true })
      }, 2000)

      return () => clearTimeout(timer)
    }
  }, [userType, navigate])

  const getTypeInfo = () => {
    if (userType === 'esercente') {
      return {
        icon: Store,
        title: 'Registrati come Esercente',
        description: 'Crea il tuo account per trovare rider professionali'
      }
    } else if (userType === 'rider') {
      return {
        icon: Bike,
        title: 'Registrati come Rider',
        description: 'Crea il tuo account per offrire servizi di consegna'
      }
    }
    return {
      icon: Store,
      title: 'Crea il tuo account',
      description: 'Unisciti alla community bemyrider'
    }
  }

  const typeInfo = getTypeInfo()
  const TypeIcon = typeInfo.icon

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 bg-white shadow-sm">
        <Link to="/" className="flex items-center text-gray-600">
          <ArrowLeft className="w-5 h-5 mr-2" />
          Torna alla home
        </Link>
        <h1 className="text-lg font-semibold bemyrider-logo">bemyrider</h1>
      </div>

      {/* Register Form */}
      <div className="flex-1 flex items-center justify-center p-6">
        <div className="w-full max-w-md">

          {/* Messaggio di reindirizzamento se non c'è il tipo */}
          {!userType && (
            <div className="text-center">
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-6">
                <div className="text-yellow-800">
                  <h3 className="font-semibold mb-2">Selezione tipo richiesta</h3>
                  <p className="text-sm">
                    Devi prima scegliere se registrarti come Esercente o Rider.
                    Ti stiamo reindirizzando alla home page...
                  </p>
                </div>
              </div>
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto"></div>
            </div>
          )}
          {/* Profile Type Indicator */}
          {userType && (
            <div className="text-center mb-6">
              <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <TypeIcon className="w-8 h-8 text-purple-600" />
              </div>
              <div className="text-sm text-purple-600 font-medium">
                {userType === 'esercente' ? 'ESERCENTE' : 'RIDER'}
              </div>
            </div>
          )}

          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              {typeInfo.title}
            </h2>
            <p className="text-gray-600">
              {typeInfo.description}
            </p>
          </div>

          {userType && (
            <SignUp
              appearance={{
                elements: {
                  formButtonPrimary: 'bg-purple-600 hover:bg-purple-700',
                  card: 'shadow-lg',
                }
              }}
              redirectUrl={`/profile-setup?type=${userType}`}
              signInUrl="/login"
            />
          )}
        </div>
      </div>
    </div>
  )
}



export default RegisterPage
