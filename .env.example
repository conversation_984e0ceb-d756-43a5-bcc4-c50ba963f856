# BeMyRider Environment Variables
# Copy this file to .env and fill in your actual values

# ===== FRONTEND CONFIGURATION =====
# Vite development server URL
VITE_API_URL=http://localhost:5000/api

# Clerk Authentication (Frontend)
VITE_CLERK_PUBLISHABLE_KEY=pk_test_your_clerk_publishable_key_here

# Supabase Configuration (Frontend)
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Stripe Configuration (Frontend)
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here

# ===== BACKEND CONFIGURATION =====
# Server Configuration
PORT=5000
NODE_ENV=development

# Frontend URL for CORS
FRONTEND_URL=http://localhost:3000

# Clerk Authentication (Backend)
CLERK_SECRET_KEY=sk_test_your_clerk_secret_key_here

# Stripe Configuration (Backend)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# ===== SECURITY NOTES =====
# 1. Never commit the actual .env file to version control
# 2. Use different keys for development and production
# 3. Rotate keys regularly
# 4. Keep secret keys secure and never share them
# 5. Use environment-specific values for each deployment

# ===== HOW TO GET THESE VALUES =====
# Clerk Keys: https://dashboard.clerk.com/
# Supabase Keys: https://app.supabase.com/project/your-project/settings/api
# Stripe Keys: https://dashboard.stripe.com/apikeys
