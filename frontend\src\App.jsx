/**
 * Main App Component
 * Root application component with routing and providers
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { ClerkProvider, SignedIn, SignedOut, RedirectToSignIn } from '@clerk/clerk-react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { Toaster } from 'react-hot-toast'

// Layout components
import DashboardLayout from '@components/layout/DashboardLayout'

// Page components (we'll create these next)
import RiderDashboard from '@pages/rider/Dashboard'
import RiderAvailability from '@pages/rider/Availability'
import RiderBookings from '@pages/rider/Bookings'
import RiderEarnings from '@pages/rider/Earnings'
import RiderProfile from '@pages/rider/Profile'

import MerchantDashboard from '@pages/merchant/Dashboard'
import MerchantFindRiders from '@pages/merchant/FindRiders'
import MerchantBookings from '@pages/merchant/Bookings'
import MerchantFleet from '@pages/merchant/Fleet'
import MerchantAnalytics from '@pages/merchant/Analytics'
import MerchantSettings from '@pages/merchant/Settings'

// Auth pages
import Home from '@pages/Home'
import SignInPage from '@pages/auth/SignIn'
import SignUpPage from '@pages/auth/SignUp'
import OnboardingPage from '@pages/auth/Onboarding'
import AuthCallback from '@components/auth/AuthCallback'

// Error pages
import NotFoundPage from '@pages/errors/NotFound'

// Styles
import './index.css'

// Clerk configuration
const clerkPubKey = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY

if (!clerkPubKey) {
  throw new Error('Missing Clerk Publishable Key')
}

// React Query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
})

// Protected Route Component
const ProtectedRoute = ({ children }) => {
  return (
    <SignedIn>
      {children}
    </SignedIn>
  )
}

// Public Route Component (redirect if signed in)
const PublicRoute = ({ children }) => {
  return (
    <SignedOut>
      {children}
    </SignedOut>
  )
}

function App() {
  return (
    <ClerkProvider
      publishableKey={clerkPubKey}
      afterSignUpUrl="/auth-callback"
      afterSignInUrl="/auth-callback"
      signInUrl="/sign-in"
      signUpUrl="/sign-up"
      skipInvitationScreen={true}
    >
      <QueryClientProvider client={queryClient}>
        <Router>
          <div className="App">
            <Routes>
              {/* Public routes */}
              <Route path="/" element={<Home />} />
              <Route path="/auth-callback" element={<AuthCallback />} />
              <Route path="/sign-in" element={
                <PublicRoute>
                  <SignInPage />
                </PublicRoute>
              } />
              <Route path="/sign-up" element={
                <PublicRoute>
                  <SignUpPage />
                </PublicRoute>
              } />

              {/* Protected routes */}
              <Route path="/onboarding" element={
                <ProtectedRoute>
                  <OnboardingPage />
                </ProtectedRoute>
              } />

              {/* Dashboard routes */}
              <Route path="/dashboard" element={
                <ProtectedRoute>
                  <DashboardLayout />
                </ProtectedRoute>
              }>
                {/* Default redirect */}
                <Route index element={<Navigate to="/dashboard/rider/dashboard" replace />} />
                
                {/* Rider routes */}
                <Route path="rider/dashboard" element={<RiderDashboard />} />
                <Route path="rider/availability" element={<RiderAvailability />} />
                <Route path="rider/bookings" element={<RiderBookings />} />
                <Route path="rider/earnings" element={<RiderEarnings />} />
                <Route path="rider/profile" element={<RiderProfile />} />

                {/* Merchant routes */}
                <Route path="merchant/dashboard" element={<MerchantDashboard />} />
                <Route path="merchant/find-riders" element={<MerchantFindRiders />} />
                <Route path="merchant/bookings" element={<MerchantBookings />} />
                <Route path="merchant/fleet" element={<MerchantFleet />} />
                <Route path="merchant/analytics" element={<MerchantAnalytics />} />
                <Route path="merchant/settings" element={<MerchantSettings />} />
              </Route>

              {/* Catch all route */}
              <Route path="*" element={<NotFoundPage />} />
            </Routes>

            {/* Global components */}
            <Toaster
              position="top-right"
              toastOptions={{
                duration: 4000,
                style: {
                  background: '#363636',
                  color: '#fff',
                },
                success: {
                  duration: 3000,
                  iconTheme: {
                    primary: '#10b981',
                    secondary: '#fff',
                  },
                },
                error: {
                  duration: 5000,
                  iconTheme: {
                    primary: '#ef4444',
                    secondary: '#fff',
                  },
                },
              }}
            />

            {/* Note: Homepage is public, no automatic redirect needed */}
          </div>
        </Router>
      </QueryClientProvider>
    </ClerkProvider>
  )
}

export default App
