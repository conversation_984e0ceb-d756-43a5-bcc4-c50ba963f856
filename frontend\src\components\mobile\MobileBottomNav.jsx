/**
 * Mobile Bottom Navigation Component
 * Bottom navigation for mobile devices
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { Link, useLocation } from 'react-router-dom'
import { motion } from 'framer-motion'
import {
  HomeIcon,
  CalendarIcon,
  UserGroupIcon,
  ChartBarIcon,
  MapPinIcon,
  TruckIcon
} from '@heroicons/react/24/outline'
import {
  HomeIcon as HomeIconSolid,
  CalendarIcon as CalendarIconSolid,
  UserGroupIcon as UserGroupIconSolid,
  ChartBarIcon as ChartBarIconSolid,
  MapPinIcon as MapPinIconSolid,
  TruckIcon as TruckIconSolid
} from '@heroicons/react/24/solid'

const navigationItems = {
  rider: [
    { 
      name: 'Dashboard', 
      href: '/rider/dashboard', 
      icon: HomeIcon, 
      iconSolid: HomeIconSolid 
    },
    { 
      name: 'Disponibilità', 
      href: '/rider/availability', 
      icon: CalendarIcon, 
      iconSolid: CalendarIconSolid 
    },
    { 
      name: 'Prenotazioni', 
      href: '/rider/bookings', 
      icon: TruckIcon, 
      iconSolid: TruckIconSolid 
    },
    { 
      name: '<PERSON><PERSON><PERSON><PERSON>', 
      href: '/rider/earnings', 
      icon: ChartBarIcon, 
      iconSolid: ChartBarIconSolid 
    }
  ],
  merchant: [
    { 
      name: 'Dashboard', 
      href: '/merchant/dashboard', 
      icon: HomeIcon, 
      iconSolid: HomeIconSolid 
    },
    { 
      name: 'Trova', 
      href: '/merchant/find-riders', 
      icon: MapPinIcon, 
      iconSolid: MapPinIconSolid 
    },
    { 
      name: 'Flotta', 
      href: '/merchant/fleet', 
      icon: UserGroupIcon, 
      iconSolid: UserGroupIconSolid 
    },
    { 
      name: 'Analytics', 
      href: '/merchant/analytics', 
      icon: ChartBarIcon, 
      iconSolid: ChartBarIconSolid 
    }
  ]
}

const MobileBottomNav = ({ userType = 'rider' }) => {
  const location = useLocation()
  const navigation = navigationItems[userType] || navigationItems.rider

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2 z-40 md:hidden">
      <div className="flex items-center justify-around">
        {navigation.map((item) => {
          const isActive = location.pathname === item.href
          const Icon = isActive ? item.iconSolid : item.icon

          return (
            <Link
              key={item.name}
              to={item.href}
              className="relative flex flex-col items-center justify-center py-2 px-3 min-w-0 flex-1"
            >
              <div className="relative">
                <Icon 
                  className={`w-6 h-6 transition-colors ${
                    isActive 
                      ? userType === 'rider' ? 'text-blue-600' : 'text-orange-600'
                      : 'text-gray-400'
                  }`} 
                />
                {isActive && (
                  <motion.div
                    layoutId="mobileActiveTab"
                    className={`absolute -top-1 -right-1 w-2 h-2 rounded-full ${
                      userType === 'rider' ? 'bg-blue-600' : 'bg-orange-600'
                    }`}
                    transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                  />
                )}
              </div>
              <span 
                className={`text-xs mt-1 font-medium transition-colors ${
                  isActive 
                    ? userType === 'rider' ? 'text-blue-600' : 'text-orange-600'
                    : 'text-gray-400'
                }`}
              >
                {item.name}
              </span>
            </Link>
          )
        })}
      </div>
    </div>
  )
}

export default MobileBottomNav
