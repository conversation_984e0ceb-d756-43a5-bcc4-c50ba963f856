/**
 * Updated Pricing Service Tests
 * Tests for corrected BeMyRider business model
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { describe, it, expect, beforeEach } from 'vitest'
import { PricingService } from '../../server/services/PricingService.js'

describe('PricingService - Updated Business Model', () => {
  let pricingService

  beforeEach(() => {
    pricingService = new PricingService()
  })

  describe('🎯 Core Pricing Logic', () => {
    it('should calculate correct pricing with only base rate + platform fee', async () => {
      const bookingData = {
        vehicleType: 'MOTO',
        duration: 2 // 2 hours
      }

      const pricing = await pricingService.calculateBookingPrice(bookingData)

      // Expected: €25/h × 2h = €50 + 15% platform fee = €57.50
      expect(pricing.baseRate).toBe(25.00)
      expect(pricing.duration).toBe(2)
      expect(pricing.baseAmount).toBe(50.00)
      expect(pricing.platformFee).toBe(7.50) // 15% of €50
      expect(pricing.total).toBe(57.50)
    })

    it('should calculate pricing for minimum duration (1 hour)', async () => {
      const bookingData = {
        vehicleType: 'BICI',
        duration: 1 // 1 hour
      }

      const pricing = await pricingService.calculateBookingPrice(bookingData)

      // Expected: €15/h × 1h = €15 + 15% platform fee = €17.25
      expect(pricing.baseRate).toBe(15.00)
      expect(pricing.duration).toBe(1)
      expect(pricing.baseAmount).toBe(15.00)
      expect(pricing.platformFee).toBe(2.25) // 15% of €15
      expect(pricing.total).toBe(17.25)
    })

    it('should calculate pricing for maximum duration (2 hours)', async () => {
      const bookingData = {
        vehicleType: 'FURGONE',
        duration: 2 // 2 hours
      }

      const pricing = await pricingService.calculateBookingPrice(bookingData)

      // Expected: €50/h × 2h = €100 + 15% platform fee = €115
      expect(pricing.baseRate).toBe(50.00)
      expect(pricing.duration).toBe(2)
      expect(pricing.baseAmount).toBe(100.00)
      expect(pricing.platformFee).toBe(15.00) // 15% of €100
      expect(pricing.total).toBe(115.00)
    })
  })

  describe('🚫 Duration Validation', () => {
    it('should reject duration less than 1 hour', async () => {
      const bookingData = {
        vehicleType: 'MOTO',
        duration: 0.5 // 30 minutes
      }

      await expect(pricingService.calculateBookingPrice(bookingData))
        .rejects.toThrow('Duration must be between 1 and 2 hours')
    })

    it('should reject duration more than 2 hours', async () => {
      const bookingData = {
        vehicleType: 'MOTO',
        duration: 3 // 3 hours
      }

      await expect(pricingService.calculateBookingPrice(bookingData))
        .rejects.toThrow('Duration must be between 1 and 2 hours')
    })

    it('should accept exactly 1 hour', async () => {
      const bookingData = {
        vehicleType: 'MOTO',
        duration: 1
      }

      const pricing = await pricingService.calculateBookingPrice(bookingData)
      expect(pricing.duration).toBe(1)
    })

    it('should accept exactly 2 hours', async () => {
      const bookingData = {
        vehicleType: 'MOTO',
        duration: 2
      }

      const pricing = await pricingService.calculateBookingPrice(bookingData)
      expect(pricing.duration).toBe(2)
    })
  })

  describe('🚗 Vehicle Types', () => {
    it('should use correct base rates for all vehicle types', async () => {
      const vehicles = [
        { type: 'BICI', expectedRate: 15.00 },
        { type: 'MOTO', expectedRate: 25.00 },
        { type: 'AUTO', expectedRate: 35.00 },
        { type: 'FURGONE', expectedRate: 50.00 }
      ]

      for (const vehicle of vehicles) {
        const pricing = await pricingService.calculateBookingPrice({
          vehicleType: vehicle.type,
          duration: 1
        })

        expect(pricing.baseRate).toBe(vehicle.expectedRate)
      }
    })
  })

  describe('💰 Rider Earnings Calculation', () => {
    it('should calculate rider earnings as 85% of base amount', () => {
      const booking = {
        baseAmount: 50.00, // €25/h × 2h
        platform_fee: 7.50
      }

      const riderEarnings = pricingService.calculateRiderEarnings(booking)
      
      // Rider gets 85% of base amount: €50 × 0.85 = €42.50
      expect(riderEarnings).toBe(42.50)
    })

    it('should calculate platform revenue as platform fee only', () => {
      const booking = {
        baseAmount: 50.00,
        platform_fee: 7.50
      }

      const platformRevenue = pricingService.calculatePlatformRevenue(booking)
      
      // Platform gets only the platform fee: €7.50
      expect(platformRevenue).toBe(7.50)
    })
  })

  describe('📊 Pricing Estimate', () => {
    it('should return simplified estimate for frontend', async () => {
      const estimateData = {
        vehicleType: 'MOTO',
        duration: 2
      }

      const estimate = await pricingService.getPricingEstimate(estimateData)

      expect(estimate).toHaveProperty('estimatedTotal', 57.50)
      expect(estimate).toHaveProperty('baseRate', 25.00)
      expect(estimate).toHaveProperty('duration', 2)
      expect(estimate).toHaveProperty('platformFee', 7.50)
      expect(estimate).toHaveProperty('breakdown')
      
      // Should NOT have distance or dynamic pricing properties
      expect(estimate).not.toHaveProperty('distance')
      expect(estimate).not.toHaveProperty('dynamicPricing')
    })
  })

  describe('🔢 Pricing Breakdown', () => {
    it('should provide clear pricing breakdown', async () => {
      const bookingData = {
        vehicleType: 'AUTO',
        duration: 1.5 // 1.5 hours (should be rejected)
      }

      // This should fail validation
      await expect(pricingService.calculateBookingPrice(bookingData))
        .rejects.toThrow('Duration must be between 1 and 2 hours')
    })

    it('should provide correct breakdown for valid booking', async () => {
      const bookingData = {
        vehicleType: 'AUTO',
        duration: 2
      }

      const pricing = await pricingService.calculateBookingPrice(bookingData)

      expect(pricing.breakdown).toHaveProperty('baseCalculation')
      expect(pricing.breakdown).toHaveProperty('platformFee')
      expect(pricing.breakdown).toHaveProperty('total')
      
      expect(pricing.breakdown.baseCalculation).toBe('€35/h × 2h = €70.00')
      expect(pricing.breakdown.platformFee).toBe('+€10.50 (platform fee 15%)')
      expect(pricing.breakdown.total).toBe('€80.50')
    })
  })

  describe('🎯 Business Model Validation', () => {
    it('should not apply any dynamic pricing', async () => {
      const bookingData = {
        vehicleType: 'MOTO',
        duration: 2
      }

      const pricing = await pricingService.calculateBookingPrice(bookingData)

      // Should be exactly base rate + platform fee, no multipliers
      const expectedBase = 25.00 * 2 // €50
      const expectedPlatformFee = expectedBase * 0.15 // €7.50
      const expectedTotal = expectedBase + expectedPlatformFee // €57.50

      expect(pricing.total).toBe(expectedTotal)
    })

    it('should not apply distance fees', async () => {
      const bookingData = {
        vehicleType: 'MOTO',
        duration: 2,
        // These coordinates should be ignored
        pickupCoordinates: { latitude: 45.4642, longitude: 9.1900 },
        deliveryCoordinates: { latitude: 45.4654, longitude: 9.1859 }
      }

      const pricing = await pricingService.calculateBookingPrice(bookingData)

      // Should not have any distance-related fees
      expect(pricing).not.toHaveProperty('distanceFee')
      expect(pricing.total).toBe(57.50) // Only base + platform fee
    })

    it('should not apply minimum booking amount', async () => {
      // Even a very small booking should not have minimum applied
      const bookingData = {
        vehicleType: 'BICI', // Cheapest vehicle
        duration: 1 // Minimum duration
      }

      const pricing = await pricingService.calculateBookingPrice(bookingData)

      // €15 + €2.25 = €17.25 (no minimum applied)
      expect(pricing.total).toBe(17.25)
    })

    it('should not apply VAT', async () => {
      const bookingData = {
        vehicleType: 'MOTO',
        duration: 2
      }

      const pricing = await pricingService.calculateBookingPrice(bookingData)

      // Should not have VAT property
      expect(pricing).not.toHaveProperty('vatAmount')
      expect(pricing).not.toHaveProperty('vat')
    })
  })
})
