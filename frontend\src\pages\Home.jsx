/**
 * Home Page - Landing page per bemyrider
 * Presenta il progetto e permette di scegliere il tipo di registrazione
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { useNavigate } from 'react-router-dom'

const Home = () => {
  const navigate = useNavigate()

  console.log('Home component rendering...')

  const handleSignUp = (userType) => {
    // Salva il tipo di utente nel localStorage per usarlo dopo la registrazione
    localStorage.setItem('pendingUserType', userType)

    // Reindirizza alla pagina di registrazione Clerk
    navigate('/sign-up')
  }

  const features = [
    {
      icon: ClockIcon,
      title: 'Turni 1-2 ore',
      description: 'Servizi brevi e flessibili per massima efficienza'
    },
    {
      icon: CurrencyEuroIcon,
      title: 'Tariffe trasparenti',
      description: 'Massimo €12.50/ora + 15% commissione piattaforma'
    },
    {
      icon: ShieldCheckIcon,
      title: 'Sicuro e legale',
      description: 'Conformità normativa e ricevute automatiche'
    },
    {
      icon: UserGroupIcon,
      title: 'Matching intelligente',
      description: 'Algoritmo avanzato per trovare il rider perfetto'
    }
  ]

  const stats = [
    { number: '500+', label: 'Rider attivi' },
    { number: '200+', label: 'Esercenti' },
    { number: '10k+', label: 'Turni completati' },
    { number: '4.8', label: 'Rating medio' }
  ]

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <h1 className="text-4xl font-bold text-center mb-8">bemyrider</h1>
      <p className="text-center text-xl mb-8">La piattaforma che connette esercenti e rider</p>

      <div className="max-w-4xl mx-auto grid md:grid-cols-2 gap-8">
        <div className="bg-white p-8 rounded-lg shadow">
          <h2 className="text-2xl font-bold mb-4">Sei un Esercente?</h2>
          <p className="mb-6">Trova rider qualificati per la tua attività.</p>
          <button
            onClick={() => handleSignUp('ESERCENTE')}
            className="w-full bg-purple-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-purple-700"
          >
            Registrati come Esercente
          </button>
        </div>

        <div className="bg-white p-8 rounded-lg shadow">
          <h2 className="text-2xl font-bold mb-4">Sei un Rider?</h2>
          <p className="mb-6">Trova opportunità di lavoro flessibili nella tua zona.</p>
          <button
            onClick={() => handleSignUp('RIDER')}
            className="w-full bg-purple-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-purple-700"
          >
            Registrati come Rider
          </button>
        </div>
      </div>
    </div>
  )
}

export default Home
