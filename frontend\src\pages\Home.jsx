/**
 * Home Page - Landing page per bemyrider
 * Presenta il progetto e permette di scegliere il tipo di registrazione
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { motion } from 'framer-motion'
import { useNavigate } from 'react-router-dom'
import {
  TruckIcon,
  UserGroupIcon,
  ClockIcon,
  CurrencyEuroIcon,
  ShieldCheckIcon,
  ArrowRightIcon,
  CheckIcon
} from '@heroicons/react/24/outline'

const Home = () => {
  const navigate = useNavigate()

  console.log('Home component rendering...')

  const handleSignUp = (userType) => {
    // Salva il tipo di utente nel localStorage per usarlo dopo la registrazione
    localStorage.setItem('pendingUserType', userType)

    // Reindirizza alla pagina di registrazione Clerk
    navigate('/sign-up')
  }

  const features = [
    {
      icon: ClockIcon,
      title: 'Turni 1-2 ore',
      description: 'Servizi brevi e flessibili per massima efficienza'
    },
    {
      icon: CurrencyEuroIcon,
      title: 'Tariffe trasparenti',
      description: 'Massimo €12.50/ora + 15% commissione piattaforma'
    },
    {
      icon: ShieldCheckIcon,
      title: 'Sicuro e legale',
      description: 'Conformità normativa e ricevute automatiche'
    },
    {
      icon: UserGroupIcon,
      title: 'Matching intelligente',
      description: 'Algoritmo avanzato per trovare il rider perfetto'
    }
  ]

  const stats = [
    { number: '500+', label: 'Rider attivi' },
    { number: '200+', label: 'Esercenti' },
    { number: '10k+', label: 'Turni completati' },
    { number: '4.8', label: 'Rating medio' }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <img
                src="/bemyrider-icona-trasp.svg"
                alt="bemyrider"
                className="h-8 w-8 mr-3"
              />
              <h1 className="bemyrider-logo text-2xl">bemyrider</h1>
            </div>
            <button
              onClick={() => navigate('/sign-in')}
              className="text-bemyrider-600 hover:text-bemyrider-700 font-medium"
            >
              Accedi
            </button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              La piattaforma che connette
              <span className="text-bemyrider-600"> esercenti</span> e
              <span className="text-bemyrider-600"> rider</span>
            </h1>
            <p className="text-xl text-gray-600 mb-12 max-w-3xl mx-auto">
              bemyrider facilita l'incontro tra attività commerciali e rider autonomi
              per servizi di delivery e trasporto con turni di 1-2 ore.
            </p>
          </motion.div>

          {/* User Type Selection */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto mb-16"
          >
            {/* Esercente Card */}
            <div className="bg-white rounded-2xl shadow-xl p-8 border-2 border-transparent hover:border-bemyrider-200 transition-all duration-300">
              <div className="text-center">
                <div className="bg-bemyrider-100 rounded-full p-4 w-16 h-16 mx-auto mb-6">
                  <TruckIcon className="w-8 h-8 text-bemyrider-600" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  Sei un Esercente?
                </h3>
                <p className="text-gray-600 mb-6">
                  Trova rider qualificati per la tua attività.
                  Gestisci la tua flotta e ottimizza le consegne.
                </p>
                <ul className="text-left space-y-2 mb-8">
                  <li className="flex items-center">
                    <CheckIcon className="w-5 h-5 text-green-500 mr-2" />
                    <span className="text-sm text-gray-600">Ricerca rider per zona</span>
                  </li>
                  <li className="flex items-center">
                    <CheckIcon className="w-5 h-5 text-green-500 mr-2" />
                    <span className="text-sm text-gray-600">Gestione turni e orari</span>
                  </li>
                  <li className="flex items-center">
                    <CheckIcon className="w-5 h-5 text-green-500 mr-2" />
                    <span className="text-sm text-gray-600">Analytics e reportistica</span>
                  </li>
                </ul>
                <button
                  onClick={() => handleSignUp('ESERCENTE')}
                  className="w-full bg-bemyrider-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-bemyrider-700 transition-colors duration-200 flex items-center justify-center"
                >
                  Registrati come Esercente
                  <ArrowRightIcon className="w-5 h-5 ml-2" />
                </button>
              </div>
            </div>

            {/* Rider Card */}
            <div className="bg-white rounded-2xl shadow-xl p-8 border-2 border-transparent hover:border-bemyrider-200 transition-all duration-300">
              <div className="text-center">
                <div className="bg-bemyrider-100 rounded-full p-4 w-16 h-16 mx-auto mb-6">
                  <UserGroupIcon className="w-8 h-8 text-bemyrider-600" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  Sei un Rider?
                </h3>
                <p className="text-gray-600 mb-6">
                  Trova opportunità di lavoro flessibili nella tua zona.
                  Guadagna fino a €12.50/ora con turni di 1-2 ore.
                </p>
                <ul className="text-left space-y-2 mb-8">
                  <li className="flex items-center">
                    <CheckIcon className="w-5 h-5 text-green-500 mr-2" />
                    <span className="text-sm text-gray-600">Turni flessibili 1-2 ore</span>
                  </li>
                  <li className="flex items-center">
                    <CheckIcon className="w-5 h-5 text-green-500 mr-2" />
                    <span className="text-sm text-gray-600">Pagamenti sicuri e puntuali</span>
                  </li>
                  <li className="flex items-center">
                    <CheckIcon className="w-5 h-5 text-green-500 mr-2" />
                    <span className="text-sm text-gray-600">Gestione disponibilità</span>
                  </li>
                </ul>
                <button
                  onClick={() => handleSignUp('RIDER')}
                  className="w-full bg-bemyrider-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-bemyrider-700 transition-colors duration-200 flex items-center justify-center"
                >
                  Registrati come Rider
                  <ArrowRightIcon className="w-5 h-5 ml-2" />
                </button>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Perché scegliere bemyrider?
            </h2>
            <p className="text-xl text-gray-600">
              La soluzione completa per il matching tra esercenti e rider
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center"
              >
                <div className="bg-white rounded-full p-4 w-16 h-16 mx-auto mb-4 shadow-lg">
                  <feature.icon className="w-8 h-8 text-bemyrider-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600">
                  {feature.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-4 gap-8 text-center">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.5 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <div className="text-4xl font-bold text-bemyrider-600 mb-2">
                  {stat.number}
                </div>
                <div className="text-gray-600">
                  {stat.label}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="flex items-center justify-center mb-4">
            <img
              src="/bemyrider-icona-trasp.svg"
              alt="bemyrider"
              className="h-8 w-8 mr-3 filter brightness-0 invert"
            />
            <h3 className="bemyrider-logo text-xl text-white">bemyrider</h3>
          </div>
          <p className="text-gray-400">
            La piattaforma che connette esercenti e rider per servizi di delivery e trasporto
          </p>
        </div>
      </footer>
    </div>
  )
}

export default Home
