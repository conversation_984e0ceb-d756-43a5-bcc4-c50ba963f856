/**
 * Test Runner
 * Runs all test suites in the correct order
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { spawn } from 'child_process'
import { existsSync, mkdirSync } from 'fs'

class TestRunner {
  constructor() {
    this.results = {
      unit: null,
      integration: null,
      performance: null,
      security: null,
      overall: null
    }
    
    this.startTime = Date.now()
  }

  /**
   * Run a test command and capture results
   * @param {string} command - Command to run
   * @param {Array} args - Command arguments
   * @returns {Promise<Object>} Test results
   */
  async runTest(command, args = []) {
    return new Promise((resolve) => {
      const startTime = Date.now()
      const process = spawn(command, args, { 
        stdio: 'pipe',
        shell: true 
      })
      
      let stdout = ''
      let stderr = ''
      
      process.stdout.on('data', (data) => {
        stdout += data.toString()
        console.log(data.toString())
      })
      
      process.stderr.on('data', (data) => {
        stderr += data.toString()
        console.error(data.toString())
      })
      
      process.on('close', (code) => {
        const duration = Date.now() - startTime
        resolve({
          success: code === 0,
          code,
          duration,
          stdout,
          stderr
        })
      })
    })
  }

  /**
   * Run unit tests
   */
  async runUnitTests() {
    console.log('\n🧪 Running Unit Tests...')
    console.log('=' .repeat(50))
    
    const result = await this.runTest('npm', ['run', 'test:unit'])
    this.results.unit = result
    
    if (result.success) {
      console.log('✅ Unit tests passed')
    } else {
      console.log('❌ Unit tests failed')
    }
    
    return result
  }

  /**
   * Run integration tests
   */
  async runIntegrationTests() {
    console.log('\n🔗 Running Integration Tests...')
    console.log('=' .repeat(50))
    
    const result = await this.runTest('npm', ['run', 'test:integration'])
    this.results.integration = result
    
    if (result.success) {
      console.log('✅ Integration tests passed')
    } else {
      console.log('❌ Integration tests failed')
    }
    
    return result
  }

  /**
   * Run performance tests
   */
  async runPerformanceTests() {
    console.log('\n⚡ Running Performance Tests...')
    console.log('=' .repeat(50))
    
    const result = await this.runTest('npm', ['run', 'test:performance'])
    this.results.performance = result
    
    if (result.success) {
      console.log('✅ Performance tests passed')
    } else {
      console.log('❌ Performance tests failed')
    }
    
    return result
  }

  /**
   * Run security tests
   */
  async runSecurityTests() {
    console.log('\n🔒 Running Security Tests...')
    console.log('=' .repeat(50))
    
    const result = await this.runTest('npm', ['run', 'test:security'])
    this.results.security = result
    
    if (result.success) {
      console.log('✅ Security tests passed')
    } else {
      console.log('❌ Security tests failed')
    }
    
    return result
  }

  /**
   * Generate test report
   */
  generateReport() {
    const totalDuration = Date.now() - this.startTime
    const passedTests = Object.values(this.results).filter(r => r && r.success).length
    const totalTests = Object.values(this.results).filter(r => r !== null).length
    
    console.log('\n📊 Test Summary Report')
    console.log('=' .repeat(50))
    console.log(`Total Duration: ${(totalDuration / 1000).toFixed(2)}s`)
    console.log(`Tests Passed: ${passedTests}/${totalTests}`)
    console.log('')
    
    // Individual test results
    Object.entries(this.results).forEach(([testType, result]) => {
      if (result) {
        const status = result.success ? '✅ PASS' : '❌ FAIL'
        const duration = (result.duration / 1000).toFixed(2)
        console.log(`${testType.padEnd(12)} | ${status} | ${duration}s`)
      }
    })
    
    console.log('')
    
    // Overall result
    const allPassed = Object.values(this.results).every(r => !r || r.success)
    if (allPassed) {
      console.log('🎉 All tests passed successfully!')
      this.results.overall = { success: true }
    } else {
      console.log('💥 Some tests failed. Please check the output above.')
      this.results.overall = { success: false }
    }
    
    return this.results.overall.success
  }

  /**
   * Save results to file
   */
  saveResults() {
    // Ensure results directory exists
    if (!existsSync('test-results')) {
      mkdirSync('test-results', { recursive: true })
    }
    
    const reportData = {
      timestamp: new Date().toISOString(),
      duration: Date.now() - this.startTime,
      results: this.results,
      summary: {
        total: Object.values(this.results).filter(r => r !== null).length,
        passed: Object.values(this.results).filter(r => r && r.success).length,
        failed: Object.values(this.results).filter(r => r && !r.success).length
      }
    }
    
    try {
      const fs = await import('fs/promises')
      await fs.writeFile(
        'test-results/test-report.json',
        JSON.stringify(reportData, null, 2)
      )
      console.log('📄 Test report saved to test-results/test-report.json')
    } catch (error) {
      console.error('❌ Failed to save test report:', error.message)
    }
  }

  /**
   * Run all tests in sequence
   */
  async runAll() {
    console.log('🚀 Starting BeMyRider Test Suite')
    console.log('=' .repeat(50))
    console.log(`Start Time: ${new Date().toISOString()}`)
    console.log('')
    
    try {
      // Setup
      console.log('🔧 Setting up test environment...')
      await this.runTest('npm', ['run', 'pretest'])
      
      // Run tests in order
      await this.runUnitTests()
      
      // Only run integration tests if unit tests pass
      if (this.results.unit.success) {
        await this.runIntegrationTests()
        
        // Only run performance tests if integration tests pass
        if (this.results.integration.success) {
          await this.runPerformanceTests()
        }
      }
      
      // Always run security tests (independent)
      await this.runSecurityTests()
      
      // Generate report
      const success = this.generateReport()
      
      // Save results
      await this.saveResults()
      
      // Cleanup
      console.log('\n🧹 Cleaning up test environment...')
      await this.runTest('npm', ['run', 'posttest'])
      
      // Exit with appropriate code
      process.exit(success ? 0 : 1)
      
    } catch (error) {
      console.error('💥 Test runner failed:', error.message)
      process.exit(1)
    }
  }

  /**
   * Run specific test type
   */
  async runSpecific(testType) {
    console.log(`🚀 Running ${testType} tests only`)
    console.log('=' .repeat(50))
    
    try {
      // Setup
      await this.runTest('npm', ['run', 'pretest'])
      
      // Run specific test
      switch (testType) {
        case 'unit':
          await this.runUnitTests()
          break
        case 'integration':
          await this.runIntegrationTests()
          break
        case 'performance':
          await this.runPerformanceTests()
          break
        case 'security':
          await this.runSecurityTests()
          break
        default:
          throw new Error(`Unknown test type: ${testType}`)
      }
      
      // Generate report
      const success = this.generateReport()
      
      // Cleanup
      await this.runTest('npm', ['run', 'posttest'])
      
      process.exit(success ? 0 : 1)
      
    } catch (error) {
      console.error('💥 Test runner failed:', error.message)
      process.exit(1)
    }
  }
}

// CLI interface
if (import.meta.url === `file://${process.argv[1]}`) {
  const runner = new TestRunner()
  const testType = process.argv[2]
  
  if (testType && ['unit', 'integration', 'performance', 'security'].includes(testType)) {
    runner.runSpecific(testType)
  } else {
    runner.runAll()
  }
}

export { TestRunner }
