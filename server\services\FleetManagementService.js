/**
 * Fleet Management Service
 * Tools for managing merchant fleets and bulk operations
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { logger } from '../utils/logger.js'
import { recordError, recordHttpRequest } from '../utils/metrics.js'
import { AuditTrail } from '../utils/compliance.js'
import { AvailabilityService } from './AvailabilityService.js'
import { MatchingService } from './MatchingService.js'

// ============================================================================
// FLEET MANAGEMENT CONFIGURATION
// ============================================================================

const FLEET_CONFIG = {
  // Fleet size limits
  SIZE_LIMITS: {
    SMALL: { min: 1, max: 5 },
    MEDIUM: { min: 6, max: 20 },
    LARGE: { min: 21, max: 100 },
    ENTERPRISE: { min: 101, max: 500 }
  },

  // Bulk operation limits
  BULK_LIMITS: {
    MAX_RIDERS_PER_OPERATION: 50,
    MAX_SHIFTS_PER_DAY: 200,
    MAX_CONCURRENT_OPERATIONS: 5
  },

  // Performance thresholds
  PERFORMANCE: {
    EXCELLENT_UTILIZATION: 85,
    GOOD_UTILIZATION: 70,
    POOR_UTILIZATION: 50,
    MIN_RATING: 4.0,
    MAX_CANCELLATION_RATE: 5
  },

  // Compliance requirements
  COMPLIANCE: {
    MAX_DAILY_HOURS: 8,
    MAX_WEEKLY_HOURS: 40,
    MIN_BREAK_HOURS: 1,
    REQUIRED_DOCUMENTS: ['identity', 'license', 'insurance'],
    RENEWAL_WARNING_DAYS: 30
  }
}

// ============================================================================
// FLEET MANAGEMENT SERVICE CLASS
// ============================================================================

export class FleetManagementService {
  constructor() {
    this.availabilityService = new AvailabilityService()
    this.matchingService = new MatchingService()
  }

  /**
   * Get fleet overview and analytics
   * @param {string} merchantId - Merchant ID
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Fleet overview
   */
  async getFleetOverview(merchantId, options = {}) {
    try {
      logger.info('Getting fleet overview', { merchantId })

      const { startDate, endDate } = options
      const dateRange = this.getDateRange(startDate, endDate)

      // Get fleet data
      const fleetData = await this.getFleetData(merchantId)
      const riders = await this.getFleetRiders(merchantId)
      const shifts = await this.getFleetShifts(merchantId, dateRange)
      const performance = await this.calculateFleetPerformance(merchantId, dateRange)

      const overview = {
        merchantId,
        dateRange,
        fleet: {
          totalRiders: riders.length,
          activeRiders: riders.filter(r => r.status === 'active').length,
          fleetType: this.determineFleetType(riders.length),
          averageRating: this.calculateAverageRating(riders),
          totalVehicles: this.countVehiclesByType(riders)
        },
        operations: {
          totalShifts: shifts.length,
          completedShifts: shifts.filter(s => s.status === 'completed').length,
          cancelledShifts: shifts.filter(s => s.status === 'cancelled').length,
          totalHours: this.calculateTotalHours(shifts),
          utilizationRate: performance.utilizationRate
        },
        performance: {
          ...performance,
          complianceScore: await this.calculateComplianceScore(merchantId),
          efficiency: this.calculateEfficiency(shifts, riders)
        },
        alerts: await this.getFleetAlerts(merchantId)
      }

      recordMetric('fleet_overview_generated', 1)
      return overview

    } catch (error) {
      logger.error('Failed to get fleet overview', { error: error.message, merchantId })
      recordError('fleet_overview', 'error')
      throw error
    }
  }

  /**
   * Bulk schedule shifts for multiple riders
   * @param {string} merchantId - Merchant ID
   * @param {Array} scheduleRequests - Array of schedule requests
   * @returns {Promise<Object>} Bulk scheduling results
   */
  async bulkScheduleShifts(merchantId, scheduleRequests) {
    try {
      logger.info('Bulk scheduling shifts', { 
        merchantId, 
        requestCount: scheduleRequests.length 
      })

      if (scheduleRequests.length > FLEET_CONFIG.BULK_LIMITS.MAX_RIDERS_PER_OPERATION) {
        throw new Error(`Maximum ${FLEET_CONFIG.BULK_LIMITS.MAX_RIDERS_PER_OPERATION} riders per operation`)
      }

      const results = {
        successful: [],
        failed: [],
        warnings: []
      }

      // Process each schedule request
      for (const request of scheduleRequests) {
        try {
          // Validate request
          await this.validateScheduleRequest(request)

          // Check rider availability
          const isAvailable = await this.checkRiderAvailability(request.riderId, request.timeSlot)
          if (!isAvailable) {
            results.failed.push({
              riderId: request.riderId,
              error: 'Rider not available for requested time slot'
            })
            continue
          }

          // Check compliance constraints
          const complianceCheck = await this.checkComplianceConstraints(request.riderId, request.timeSlot)
          if (!complianceCheck.valid) {
            results.warnings.push({
              riderId: request.riderId,
              warning: complianceCheck.reason
            })
          }

          // Create shift
          const shift = await this.createScheduledShift(merchantId, request)
          results.successful.push({
            riderId: request.riderId,
            shiftId: shift.id,
            timeSlot: request.timeSlot
          })

        } catch (error) {
          results.failed.push({
            riderId: request.riderId,
            error: error.message
          })
        }
      }

      // Log audit trail
      await AuditTrail.logUserAction(
        'BULK_SCHEDULE_SHIFTS',
        merchantId,
        'fleet_management',
        {
          totalRequests: scheduleRequests.length,
          successful: results.successful.length,
          failed: results.failed.length,
          warnings: results.warnings.length
        }
      )

      logger.info('Bulk scheduling completed', {
        merchantId,
        successful: results.successful.length,
        failed: results.failed.length
      })

      return results

    } catch (error) {
      logger.error('Failed to bulk schedule shifts', { error: error.message, merchantId })
      throw error
    }
  }

  /**
   * Optimize fleet allocation for given time period
   * @param {string} merchantId - Merchant ID
   * @param {Object} optimizationRequest - Optimization parameters
   * @returns {Promise<Object>} Optimization recommendations
   */
  async optimizeFleetAllocation(merchantId, optimizationRequest) {
    try {
      logger.info('Optimizing fleet allocation', { merchantId })

      const { timeRange, objectives, constraints } = optimizationRequest

      // Get current fleet state
      const riders = await this.getFleetRiders(merchantId)
      const currentShifts = await this.getFleetShifts(merchantId, timeRange)
      const demandForecast = await this.getDemandForecast(merchantId, timeRange)

      // Calculate optimization
      const optimization = {
        currentState: {
          totalRiders: riders.length,
          scheduledShifts: currentShifts.length,
          utilizationRate: this.calculateUtilizationRate(currentShifts, riders)
        },
        recommendations: [],
        projectedImpact: {}
      }

      // Analyze demand vs capacity
      const demandAnalysis = this.analyzeDemandCapacity(demandForecast, riders)
      
      if (demandAnalysis.underutilized.length > 0) {
        optimization.recommendations.push({
          type: 'reduce_capacity',
          description: 'Consider reducing shifts for underutilized periods',
          affectedRiders: demandAnalysis.underutilized,
          potentialSavings: this.calculatePotentialSavings(demandAnalysis.underutilized)
        })
      }

      if (demandAnalysis.overdemand.length > 0) {
        optimization.recommendations.push({
          type: 'increase_capacity',
          description: 'Consider adding more riders for high-demand periods',
          suggestedAdditionalRiders: demandAnalysis.overdemand.length,
          potentialRevenue: this.calculatePotentialRevenue(demandAnalysis.overdemand)
        })
      }

      // Skill-based optimization
      const skillOptimization = await this.optimizeBySkills(riders, demandForecast)
      if (skillOptimization.recommendations.length > 0) {
        optimization.recommendations.push(...skillOptimization.recommendations)
      }

      // Calculate projected impact
      optimization.projectedImpact = {
        utilizationImprovement: this.calculateUtilizationImprovement(optimization.recommendations),
        costSavings: this.calculateCostSavings(optimization.recommendations),
        revenueIncrease: this.calculateRevenueIncrease(optimization.recommendations)
      }

      return optimization

    } catch (error) {
      logger.error('Failed to optimize fleet allocation', { error: error.message, merchantId })
      throw error
    }
  }

  /**
   * Get fleet performance analytics
   * @param {string} merchantId - Merchant ID
   * @param {Object} timeRange - Time range for analysis
   * @returns {Promise<Object>} Performance analytics
   */
  async getFleetAnalytics(merchantId, timeRange) {
    try {
      logger.info('Getting fleet analytics', { merchantId })

      const riders = await this.getFleetRiders(merchantId)
      const shifts = await this.getFleetShifts(merchantId, timeRange)

      const analytics = {
        overview: {
          totalRiders: riders.length,
          totalShifts: shifts.length,
          totalHours: this.calculateTotalHours(shifts),
          averageShiftDuration: this.calculateAverageShiftDuration(shifts)
        },
        performance: {
          utilizationRate: this.calculateUtilizationRate(shifts, riders),
          completionRate: this.calculateCompletionRate(shifts),
          averageRating: this.calculateAverageRating(riders),
          onTimePerformance: this.calculateOnTimePerformance(shifts)
        },
        trends: {
          daily: this.calculateDailyTrends(shifts),
          weekly: this.calculateWeeklyTrends(shifts),
          monthly: this.calculateMonthlyTrends(shifts)
        },
        riderPerformance: riders.map(rider => ({
          riderId: rider.id,
          name: rider.name,
          totalShifts: shifts.filter(s => s.rider_id === rider.id).length,
          averageRating: rider.rating,
          utilizationRate: this.calculateRiderUtilization(rider.id, shifts),
          earnings: this.calculateRiderEarnings(rider.id, shifts)
        })),
        compliance: {
          documentsStatus: await this.getDocumentsComplianceStatus(riders),
          workingHoursCompliance: this.checkWorkingHoursCompliance(shifts),
          alerts: await this.getComplianceAlerts(merchantId)
        }
      }

      return analytics

    } catch (error) {
      logger.error('Failed to get fleet analytics', { error: error.message, merchantId })
      throw error
    }
  }

  /**
   * Manage rider assignments and reassignments
   * @param {string} merchantId - Merchant ID
   * @param {Array} assignments - Assignment operations
   * @returns {Promise<Object>} Assignment results
   */
  async manageRiderAssignments(merchantId, assignments) {
    try {
      logger.info('Managing rider assignments', { 
        merchantId, 
        assignmentCount: assignments.length 
      })

      const results = {
        successful: [],
        failed: [],
        warnings: []
      }

      for (const assignment of assignments) {
        try {
          switch (assignment.operation) {
            case 'assign':
              await this.assignRiderToFleet(merchantId, assignment.riderId)
              results.successful.push({
                operation: 'assign',
                riderId: assignment.riderId,
                message: 'Rider assigned to fleet'
              })
              break

            case 'reassign':
              await this.reassignRider(assignment.riderId, assignment.fromShift, assignment.toShift)
              results.successful.push({
                operation: 'reassign',
                riderId: assignment.riderId,
                message: 'Rider reassigned successfully'
              })
              break

            case 'remove':
              await this.removeRiderFromFleet(merchantId, assignment.riderId)
              results.successful.push({
                operation: 'remove',
                riderId: assignment.riderId,
                message: 'Rider removed from fleet'
              })
              break

            default:
              throw new Error(`Unknown operation: ${assignment.operation}`)
          }

        } catch (error) {
          results.failed.push({
            operation: assignment.operation,
            riderId: assignment.riderId,
            error: error.message
          })
        }
      }

      return results

    } catch (error) {
      logger.error('Failed to manage rider assignments', { error: error.message, merchantId })
      throw error
    }
  }

  // ============================================================================
  // HELPER METHODS
  // ============================================================================

  /**
   * Determine fleet type based on size
   */
  determineFleetType(riderCount) {
    if (riderCount <= FLEET_CONFIG.SIZE_LIMITS.SMALL.max) return 'SMALL'
    if (riderCount <= FLEET_CONFIG.SIZE_LIMITS.MEDIUM.max) return 'MEDIUM'
    if (riderCount <= FLEET_CONFIG.SIZE_LIMITS.LARGE.max) return 'LARGE'
    return 'ENTERPRISE'
  }

  /**
   * Calculate average rating for riders
   */
  calculateAverageRating(riders) {
    if (riders.length === 0) return 0
    const totalRating = riders.reduce((sum, rider) => sum + (rider.rating || 0), 0)
    return Math.round((totalRating / riders.length) * 100) / 100
  }

  /**
   * Count vehicles by type
   */
  countVehiclesByType(riders) {
    const counts = {}
    riders.forEach(rider => {
      const vehicleType = rider.vehicle_type || 'UNKNOWN'
      counts[vehicleType] = (counts[vehicleType] || 0) + 1
    })
    return counts
  }

  /**
   * Calculate total hours from shifts
   */
  calculateTotalHours(shifts) {
    return shifts.reduce((total, shift) => {
      return total + (shift.duration || 1)
    }, 0)
  }

  /**
   * Calculate utilization rate
   */
  calculateUtilizationRate(shifts, riders) {
    if (riders.length === 0) return 0
    
    const totalPossibleHours = riders.length * 8 * 30 // Assuming 8 hours/day, 30 days
    const totalWorkedHours = this.calculateTotalHours(shifts)
    
    return Math.round((totalWorkedHours / totalPossibleHours) * 100)
  }

  /**
   * Get date range helper
   */
  getDateRange(startDate, endDate) {
    const now = new Date()
    return {
      startDate: startDate || new Date(now.getFullYear(), now.getMonth(), 1).toISOString(),
      endDate: endDate || now.toISOString()
    }
  }

  // ============================================================================
  // MOCK DATABASE OPERATIONS
  // ============================================================================

  async getFleetData(merchantId) {
    // Mock implementation
    return {
      id: merchantId,
      name: 'Test Fleet',
      type: 'restaurant',
      created_at: '2024-01-01T00:00:00Z'
    }
  }

  async getFleetRiders(merchantId) {
    // Mock implementation
    return [
      {
        id: 'rider_1',
        name: 'Mario Rossi',
        status: 'active',
        rating: 4.8,
        vehicle_type: 'MOTO',
        total_shifts: 45
      },
      {
        id: 'rider_2',
        name: 'Luigi Verdi',
        status: 'active',
        rating: 4.6,
        vehicle_type: 'BICI',
        total_shifts: 38
      }
    ]
  }

  async getFleetShifts(merchantId, dateRange) {
    // Mock implementation
    return [
      {
        id: 'shift_1',
        rider_id: 'rider_1',
        status: 'completed',
        duration: 2,
        start_time: '2024-12-22T10:00:00Z',
        end_time: '2024-12-22T12:00:00Z'
      },
      {
        id: 'shift_2',
        rider_id: 'rider_2',
        status: 'completed',
        duration: 1,
        start_time: '2024-12-22T14:00:00Z',
        end_time: '2024-12-22T15:00:00Z'
      }
    ]
  }

  async calculateFleetPerformance(merchantId, dateRange) {
    // Mock implementation
    return {
      utilizationRate: 75,
      completionRate: 95,
      averageRating: 4.7,
      onTimePerformance: 92
    }
  }

  async calculateComplianceScore(merchantId) {
    // Mock implementation
    return 88
  }

  async getFleetAlerts(merchantId) {
    // Mock implementation
    return [
      {
        type: 'warning',
        message: 'Rider license expires in 15 days',
        riderId: 'rider_1',
        priority: 'medium'
      }
    ]
  }

  // Additional mock methods for fleet management
  async validateScheduleRequest(request) {
    if (!request.riderId || !request.timeSlot) {
      throw new Error('Rider ID and time slot are required')
    }
  }

  async checkRiderAvailability(riderId, timeSlot) {
    // Mock implementation - assume rider is available
    return true
  }

  async checkComplianceConstraints(riderId, timeSlot) {
    // Mock implementation
    return { valid: true, reason: null }
  }

  async createScheduledShift(merchantId, request) {
    // Mock implementation
    return {
      id: `shift_${Date.now()}`,
      merchant_id: merchantId,
      rider_id: request.riderId,
      ...request.timeSlot
    }
  }

  async getDemandForecast(merchantId, timeRange) {
    // Mock implementation
    return [
      { hour: 12, demand: 8, capacity: 6 },
      { hour: 13, demand: 10, capacity: 6 },
      { hour: 19, demand: 12, capacity: 8 }
    ]
  }

  analyzeDemandCapacity(forecast, riders) {
    return {
      underutilized: [],
      overdemand: forecast.filter(f => f.demand > f.capacity)
    }
  }

  async optimizeBySkills(riders, forecast) {
    return { recommendations: [] }
  }

  calculateUtilizationImprovement(recommendations) {
    return 15 // Mock 15% improvement
  }

  calculateCostSavings(recommendations) {
    return 1200 // Mock €1200 savings
  }

  calculateRevenueIncrease(recommendations) {
    return 2500 // Mock €2500 increase
  }

  calculatePotentialSavings(underutilized) {
    return underutilized.length * 100 // Mock calculation
  }

  calculatePotentialRevenue(overdemand) {
    return overdemand.length * 150 // Mock calculation
  }

  calculateCompletionRate(shifts) {
    const completed = shifts.filter(s => s.status === 'completed').length
    return shifts.length > 0 ? Math.round((completed / shifts.length) * 100) : 0
  }

  calculateOnTimePerformance(shifts) {
    // Mock implementation
    return 92
  }

  calculateDailyTrends(shifts) {
    // Mock implementation
    return {}
  }

  calculateWeeklyTrends(shifts) {
    // Mock implementation
    return {}
  }

  calculateMonthlyTrends(shifts) {
    // Mock implementation
    return {}
  }

  calculateRiderUtilization(riderId, shifts) {
    const riderShifts = shifts.filter(s => s.rider_id === riderId)
    return riderShifts.length * 10 // Mock calculation
  }

  calculateRiderEarnings(riderId, shifts) {
    const riderShifts = shifts.filter(s => s.rider_id === riderId)
    return riderShifts.reduce((total, shift) => total + (shift.duration * 12), 0)
  }

  async getDocumentsComplianceStatus(riders) {
    return {
      compliant: riders.length - 1,
      nonCompliant: 1,
      expiringSoon: 1
    }
  }

  checkWorkingHoursCompliance(shifts) {
    return {
      compliant: shifts.length - 2,
      violations: 2
    }
  }

  async getComplianceAlerts(merchantId) {
    return [
      {
        type: 'document_expiry',
        message: 'Driver license expiring soon',
        count: 1
      }
    ]
  }

  calculateAverageShiftDuration(shifts) {
    if (shifts.length === 0) return 0
    const totalDuration = shifts.reduce((sum, shift) => sum + (shift.duration || 1), 0)
    return Math.round((totalDuration / shifts.length) * 100) / 100
  }

  calculateEfficiency(shifts, riders) {
    // Mock efficiency calculation
    return 78
  }

  async assignRiderToFleet(merchantId, riderId) {
    logger.info('Rider assigned to fleet', { merchantId, riderId })
  }

  async reassignRider(riderId, fromShift, toShift) {
    logger.info('Rider reassigned', { riderId, fromShift, toShift })
  }

  async removeRiderFromFleet(merchantId, riderId) {
    logger.info('Rider removed from fleet', { merchantId, riderId })
  }
}

export default FleetManagementService
