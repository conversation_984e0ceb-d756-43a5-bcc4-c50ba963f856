/**
 * Sign Up Page
 * Authentication sign up page
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { SignUp } from '@clerk/clerk-react'
import { motion } from 'framer-motion'

const SignUpPage = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-bemyrider-600 to-primary-600 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md"
      >
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-white rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
            <span className="text-2xl font-bold text-bemyrider-600">BR</span>
          </div>
          <h1 className="text-3xl font-bold text-white mb-2">BeMyRider</h1>
          <p className="text-white/80">Crea il tuo account</p>
        </div>

        <div className="bg-white rounded-2xl shadow-xl p-8">
          <SignUp
            appearance={{
              elements: {
                formButtonPrimary: 'bg-blue-600 hover:bg-blue-700 text-white',
                card: 'shadow-none bg-transparent',
                headerTitle: 'hidden',
                headerSubtitle: 'hidden',
                formFieldInput: 'border border-gray-300 rounded-md px-3 py-2 w-full',
                formFieldLabel: 'block text-sm font-medium text-gray-700 mb-1',
                formField: 'mb-4'
              }
            }}
          />
        </div>

        <div className="text-center mt-6">
          <p className="text-white/60 text-sm">
            © 2024 BeMyRider. Tutti i diritti riservati.
          </p>
        </div>
      </motion.div>
    </div>
  )
}

export default SignUpPage
