/**
 * Sign Up Page
 * Authentication sign up page
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { SignUp } from '@clerk/clerk-react'
import { motion } from 'framer-motion'
import { useEffect, useState } from 'react'
import { useSearchParams, useNavigate } from 'react-router-dom'
import { ArrowLeftIcon } from '@heroicons/react/24/outline'

const SignUpPage = () => {
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const [accountType, setAccountType] = useState(null)

  useEffect(() => {
    // Controlla se c'è un tipo di account nei parametri URL
    const typeFromUrl = searchParams.get('type')

    // Controlla se c'è un tipo di account nel localStorage
    const typeFromStorage = localStorage.getItem('pendingUserType')

    const selectedType = typeFromUrl || typeFromStorage

    if (selectedType) {
      setAccountType(selectedType)
      // Assicurati che sia salvato nel localStorage per l'auth-callback
      localStorage.setItem('pendingUserType', selectedType)
    } else {
      // Se non c'è nessun tipo selezionato, reindirizza all'onboarding
      navigate('/onboarding')
    }
  }, [searchParams, navigate])

  if (!accountType) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-bemyrider-600 to-primary-600 flex items-center justify-center">
        <div className="text-white text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
          <p>Caricamento...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-bemyrider-600 to-primary-600 flex flex-col">
      {/* Header con back button */}
      <div className="p-6">
        <button
          onClick={() => navigate('/onboarding')}
          className="flex items-center text-white/80 hover:text-white transition-colors duration-200"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-2" />
          Torna alla selezione
        </button>
      </div>

      <div className="flex-1 flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="w-full max-w-md"
        >
          <div className="text-center mb-8">
            <div className="w-16 h-16 bg-white rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
              <img
                src="/bemyrider-icona-trasp.svg"
                alt="bemyrider"
                className="h-10 w-10"
              />
            </div>
            <h1 className="text-3xl font-bold text-white mb-2">
              <span className="bemyrider-logo">bemyrider</span>
            </h1>
            <p className="text-white/80">
              Registrazione come <strong>{accountType}</strong>
            </p>
          </div>

          <div className="bg-white rounded-2xl shadow-xl p-8">
            <SignUp
              appearance={{
                elements: {
                  formButtonPrimary: 'bg-bemyrider-600 hover:bg-bemyrider-700 text-white',
                  card: 'shadow-none bg-transparent',
                  headerTitle: 'hidden',
                  headerSubtitle: 'hidden',
                  formFieldInput: 'border border-gray-300 rounded-md px-3 py-2 w-full focus:ring-2 focus:ring-bemyrider-500 focus:border-bemyrider-500',
                  formFieldLabel: 'block text-sm font-medium text-gray-700 mb-1',
                  formField: 'mb-4',
                  socialButtonsBlockButton: 'border border-gray-300 hover:bg-gray-50',
                  dividerLine: 'bg-gray-300',
                  dividerText: 'text-gray-500'
                }
              }}
            />
          </div>

          <div className="text-center mt-6">
            <p className="text-white/60 text-sm">
              © 2024 bemyrider. Tutti i diritti riservati.
            </p>
          </div>
        </motion.div>
      </div>
    </div>
  )
}

export default SignUpPage
