/**
 * Sign Up Page
 * Authentication sign up page
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { SignUp } from '@clerk/clerk-react'
import { motion } from 'framer-motion'

const SignUpPage = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-bemyrider-600 to-primary-600 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-lg"
      >
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-white rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
            <span className="text-2xl font-bold text-bemyrider-600">BR</span>
          </div>
          <h1 className="text-3xl font-bold text-white mb-2">BeMyRider</h1>
          <p className="text-white/80">Crea il tuo account</p>
        </div>

        <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
          <SignUp
            appearance={{
              elements: {
                formButtonPrimary: 'bg-bemyrider-600 hover:bg-bemyrider-700 text-white font-medium py-3 px-4 rounded-lg transition-colors',
                card: 'shadow-none border-0 bg-transparent p-8',
                headerTitle: 'hidden',
                headerSubtitle: 'hidden',
                socialButtonsBlockButton: 'border border-gray-300 hover:border-gray-400 text-gray-700 bg-white',
                formFieldInput: 'border border-gray-300 rounded-lg px-3 py-2 focus:border-bemyrider-600 focus:ring-1 focus:ring-bemyrider-600',
                footerActionLink: 'text-bemyrider-600 hover:text-bemyrider-700'
              },
              layout: {
                socialButtonsPlacement: 'top'
              }
            }}
          />
        </div>

        <div className="text-center mt-6">
          <p className="text-white/60 text-sm">
            © 2024 BeMyRider. Tutti i diritti riservati.
          </p>
        </div>
      </motion.div>
    </div>
  )
}

export default SignUpPage
