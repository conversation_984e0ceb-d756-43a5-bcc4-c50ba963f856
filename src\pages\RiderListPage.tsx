import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Search, Filter, Star, MapPin, RefreshCw } from 'lucide-react'
import { apiService } from '../services/api'
import toast from 'react-hot-toast'

interface Rider {
  id: string
  clerk_id?: string
  name?: string
  first_name?: string
  last_name?: string
  email?: string
  city?: string
  vehicle_type?: string
  hourly_rate?: number
  is_available?: boolean
  description?: string
  averageRating?: number
  totalReviews?: number
  totalBookings?: number
}

const RiderListPage: React.FC = () => {
  const [riders, setRiders] = useState<Rider[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedVehicleType, setSelectedVehicleType] = useState('SCOOTER')
  const navigate = useNavigate()

  const vehicleTypes = [
    { key: 'BICI', label: 'BICI' },
    { key: 'E_BIKE', label: 'E-BIKE' },
    { key: 'SCOOTER', label: 'SCOOTER' },
    { key: 'MOTO', label: 'MOTO' },
    { key: 'AUTO', label: 'AUTO' },
    { key: 'FURGONE', label: 'FURGONE' }
  ]

  useEffect(() => {
    fetchRiders()
  }, [selectedVehicleType])

  const fetchRiders = async () => {
    try {
      setLoading(true)
      setError(null)

      const filters: any = {}
      if (selectedVehicleType) filters.vehicleType = selectedVehicleType
      if (searchQuery.trim()) filters.city = searchQuery.trim()

      const response = await apiService.getRiders(filters)

      if (response.success && response.data) {
        setRiders(response.data.riders)
      } else {
        throw new Error(response.error || 'Failed to fetch riders')
      }
    } catch (error: any) {
      console.error('Error fetching riders:', error)
      setError(error.message || 'Errore nel caricamento dei rider')
      toast.error('Errore nel caricamento dei rider')

      // Fallback to empty array
      setRiders([])
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = () => {
    fetchRiders()
  }

  const filteredRiders = riders.filter(rider =>
    (rider.name || '').toLowerCase().includes(searchQuery.toLowerCase()) ||
    (rider.city || '').toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <div className="bg-gray-50 min-h-screen">
      {/* Search Bar */}
      <div className="bg-white px-4 py-4 border-b">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-xl font-bold text-gray-900">Trova Rider</h1>
          <button
            onClick={fetchRiders}
            className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
            title="Aggiorna lista"
          >
            <RefreshCw className="w-5 h-5" />
          </button>
        </div>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <input
            type="text"
            placeholder="Cerca per città..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            className="w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          />
          <button
            onClick={handleSearch}
            className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-orange-500 text-white p-2 rounded-lg"
          >
            <Filter size={16} />
          </button>
        </div>
      </div>

      {/* Vehicle Type Tabs */}
      <div className="bg-white px-4 py-2 border-b">
        <div className="flex space-x-1">
          {vehicleTypes.map((type) => (
            <button
              key={type.key}
              onClick={() => setSelectedVehicleType(type.key)}
              className={`px-6 py-2 rounded-lg font-medium transition-colors ${
                selectedVehicleType === type.key
                  ? 'bg-purple-600 text-white'
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              {type.label}
            </button>
          ))}
        </div>
      </div>

      {/* Riders List */}
      <div className="px-4 py-4">
        {loading ? (
          <div className="flex justify-center py-8">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Caricamento rider...</p>
            </div>
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <div className="text-red-500 mb-4">
              <Search className="w-12 h-12 mx-auto mb-2" />
              <p className="text-lg font-medium">Errore nel caricamento</p>
              <p className="text-sm text-gray-600 mt-1">{error}</p>
            </div>
            <button
              onClick={fetchRiders}
              className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center mx-auto"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Riprova
            </button>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredRiders.map((rider) => (
              <div
                key={rider.id}
                onClick={() => navigate(`/app/riders/${rider.clerk_id || rider.id}`)}
                className="bg-white rounded-lg p-4 border border-gray-200 cursor-pointer hover:shadow-md transition-shadow"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center">
                      <span className="text-gray-600 font-medium">
                        {(rider.name || rider.first_name || 'R').charAt(0).toUpperCase()}
                      </span>
                    </div>

                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900">
                        {rider.name || `${rider.first_name || ''} ${rider.last_name || ''}`.trim() || 'Rider'}
                      </h3>
                      <div className="flex items-center space-x-1 mt-1">
                        <Star className="w-4 h-4 text-orange-400 fill-current" />
                        <span className="text-sm text-gray-600">{rider.averageRating?.toFixed(1) || '0.0'}</span>
                        <span className="text-xs text-gray-500">({rider.totalReviews || 0} recensioni)</span>
                      </div>
                      <div className="flex items-center space-x-2 mt-1">
                        <MapPin className="w-4 h-4 text-gray-400" />
                        <span className="text-sm text-gray-600">{rider.city || 'Città non specificata'}</span>
                        {rider.vehicle_type && (
                          <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                            {rider.vehicle_type}
                          </span>
                        )}
                      </div>
                      {rider.description && (
                        <p className="text-xs text-gray-500 mt-1 line-clamp-2">
                          {rider.description}
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="text-right">
                    <div className="text-lg font-bold text-gray-900">
                      €{rider.hourly_rate?.toFixed(2) || '0.00'}/h
                    </div>
                    <div className={`text-xs font-medium ${
                      rider.is_available ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {rider.is_available ? 'Disponibile' : 'Non disponibile'}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {rider.totalBookings || 0} prenotazioni
                    </div>
                  </div>
                </div>
              </div>
            ))}

            {filteredRiders.length === 0 && !loading && (
              <div className="text-center py-8">
                <Search className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500 text-lg font-medium">Nessun rider trovato</p>
                <p className="text-gray-400 text-sm mt-1">
                  Prova a cambiare i filtri di ricerca
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

export default RiderListPage
