/**
 * Booking Service
 * Core business logic for booking management
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import BookingRepository from '../repositories/BookingRepository.js'
import { UserRepository } from '../repositories/UserRepository.js'
import { PricingService } from './PricingService.js'
import { NotificationService } from './NotificationService.js'
import { PaymentService } from './PaymentService.js'
import { AuditTrail } from '../utils/compliance.js'
import { logger } from '../utils/logger.js'
import { recordBooking, recordError } from '../utils/metrics.js'

// ============================================================================
// BOOKING STATES
// ============================================================================

export const BOOKING_STATES = {
  PENDING: 'PENDING',
  ACCEPTED: 'ACCEPTED',
  IN_PROGRESS: 'IN_PROGRESS',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED',
  EXPIRED: 'EXPIRED'
}

export const BOOKING_TRANSITIONS = {
  [BOOKING_STATES.PENDING]: [BOOKING_STATES.ACCEPTED, BOOKING_STATES.CANCELLED, BOOKING_STATES.EXPIRED],
  [BOOKING_STATES.ACCEPTED]: [BOOKING_STATES.IN_PROGRESS, BOOKING_STATES.CANCELLED],
  [BOOKING_STATES.IN_PROGRESS]: [BOOKING_STATES.COMPLETED, BOOKING_STATES.CANCELLED],
  [BOOKING_STATES.COMPLETED]: [],
  [BOOKING_STATES.CANCELLED]: [],
  [BOOKING_STATES.EXPIRED]: []
}

// ============================================================================
// BOOKING SERVICE CLASS
// ============================================================================

export class BookingService {
  constructor() {
    this.bookingRepository = new BookingRepository()
    this.userRepository = new UserRepository()
    this.pricingService = new PricingService()
    this.notificationService = new NotificationService()
    this.paymentService = new PaymentService()
  }

  /**
   * Create a new booking
   * @param {Object} bookingData - Booking details
   * @param {string} clientId - Client user ID
   * @returns {Promise<Object>} Created booking
   */
  async createBooking(bookingData, clientId) {
    try {
      logger.info('Creating new booking', { clientId, bookingData })

      // Validate booking data
      await this.validateBookingData(bookingData)

      // Validate client
      const client = await this.userRepository.findById(clientId)
      if (!client || client.type !== 'ESERCENTE') {
        throw new Error('Invalid client or client type')
      }

      // Find available riders
      const availableRiders = await this.findAvailableRiders(bookingData)
      if (availableRiders.length === 0) {
        throw new Error('No available riders found for this booking')
      }

      // Calculate pricing
      const pricing = await this.pricingService.calculateBookingPrice(bookingData)

      // Generate booking number
      const bookingNumber = await this.generateBookingNumber()

      // Create booking record
      const booking = await this.bookingRepository.create({
        booking_number: bookingNumber,
        client_id: clientId,
        rider_id: null, // Will be assigned when accepted
        service_type: bookingData.serviceType,
        vehicle_type: bookingData.vehicleType,
        scheduled_date: bookingData.scheduledDate,
        duration: bookingData.duration,
        pickup_address: bookingData.pickupAddress,
        pickup_coordinates: bookingData.pickupCoordinates,
        delivery_address: bookingData.deliveryAddress,
        delivery_coordinates: bookingData.deliveryCoordinates,
        special_instructions: bookingData.specialInstructions,
        base_rate: pricing.baseRate,
        distance_fee: pricing.distanceFee,
        platform_fee: pricing.platformFee,
        dynamic_pricing_multiplier: pricing.dynamicMultiplier,
        subtotal: pricing.subtotal,
        total_amount: pricing.total,
        status: BOOKING_STATES.PENDING,
        payment_status: 'PENDING',
        expires_at: new Date(Date.now() + 15 * 60 * 1000) // 15 minutes
      })

      // Create payment intent with destination charges
      const paymentIntent = await this.paymentService.createPaymentIntent({
        totalAmount: pricing.total,        // Total charged to merchant (e.g., €28.75)
        riderAmount: pricing.baseAmount,   // Amount for rider (e.g., €25.00)
        platformFee: pricing.platformFee,  // Platform fee (e.g., €3.75)
        riderId: availableRiders[0]?.id,   // Will be updated when rider accepts
        bookingId: booking.id,
        clientId: clientId
      })

      // Update booking with payment intent
      await this.bookingRepository.updateById(booking.id, {
        payment_intent_id: paymentIntent.id,
        payment_client_secret: paymentIntent.client_secret
      })

      // Notify available riders
      await this.notifyAvailableRiders(booking, availableRiders)

      // Schedule auto-expiration
      await this.scheduleBookingExpiration(booking.id)

      // Log audit trail
      await AuditTrail.logUserAction(
        'BOOKING_CREATED',
        clientId,
        'booking',
        { bookingId: booking.id, bookingNumber },
        null
      )

      // Record metrics
      recordBooking(BOOKING_STATES.PENDING, 'ESERCENTE', pricing.total)

      logger.info('Booking created successfully', { 
        bookingId: booking.id, 
        bookingNumber,
        totalAmount: pricing.total 
      })

      return {
        ...booking,
        payment_client_secret: paymentIntent.client_secret,
        available_riders: availableRiders.length
      }

    } catch (error) {
      logger.error('Failed to create booking', { error: error.message, clientId, bookingData })
      recordError('booking_creation', 'error')
      throw error
    }
  }

  /**
   * Accept a booking (rider action)
   * @param {string} bookingId - Booking ID
   * @param {string} riderId - Rider ID
   * @returns {Promise<Object>} Updated booking
   */
  async acceptBooking(bookingId, riderId) {
    try {
      logger.info('Rider accepting booking', { bookingId, riderId })

      // Get booking
      const booking = await this.bookingRepository.findById(bookingId)
      if (!booking) {
        throw new Error('Booking not found')
      }

      // Validate state transition
      if (!this.canTransitionTo(booking.status, BOOKING_STATES.ACCEPTED)) {
        throw new Error(`Cannot accept booking in ${booking.status} state`)
      }

      // Validate rider
      const rider = await this.userRepository.findById(riderId)
      if (!rider || rider.type !== 'RIDER' || !rider.is_available) {
        throw new Error('Invalid rider or rider not available')
      }

      // Check if booking is still available
      if (booking.rider_id && booking.rider_id !== riderId) {
        throw new Error('Booking already accepted by another rider')
      }

      // Update booking
      const updatedBooking = await this.bookingRepository.updateById(bookingId, {
        rider_id: riderId,
        status: BOOKING_STATES.ACCEPTED,
        accepted_at: new Date().toISOString()
      })

      // Update rider availability
      await this.userRepository.updateAvailability(riderId, true, false)

      // Notify client
      await this.notificationService.notifyBookingAccepted(booking, rider)

      // Notify other riders that booking is no longer available
      await this.notificationService.notifyBookingUnavailable(bookingId)

      // Log audit trail
      await AuditTrail.logUserAction(
        'BOOKING_ACCEPTED',
        riderId,
        'booking',
        { bookingId, clientId: booking.client_id },
        null
      )

      logger.info('Booking accepted successfully', { bookingId, riderId })

      return updatedBooking

    } catch (error) {
      logger.error('Failed to accept booking', { error: error.message, bookingId, riderId })
      recordError('booking_acceptance', 'error')
      throw error
    }
  }

  /**
   * Start a booking (rider action)
   * @param {string} bookingId - Booking ID
   * @param {string} riderId - Rider ID
   * @param {Object} startData - Start location and time
   * @returns {Promise<Object>} Updated booking
   */
  async startBooking(bookingId, riderId, startData = {}) {
    try {
      logger.info('Starting booking', { bookingId, riderId })

      // Get booking
      const booking = await this.bookingRepository.findById(bookingId)
      if (!booking || booking.rider_id !== riderId) {
        throw new Error('Booking not found or not assigned to this rider')
      }

      // Validate state transition
      if (!this.canTransitionTo(booking.status, BOOKING_STATES.IN_PROGRESS)) {
        throw new Error(`Cannot start booking in ${booking.status} state`)
      }

      // Update booking
      const updatedBooking = await this.bookingRepository.updateById(bookingId, {
        status: BOOKING_STATES.IN_PROGRESS,
        actual_start: new Date().toISOString(),
        start_location: startData.location,
        start_coordinates: startData.coordinates
      })

      // Confirm payment
      await this.paymentService.confirmPayment(booking.payment_intent_id)

      // Notify client
      await this.notificationService.notifyBookingStarted(booking)

      // Log audit trail
      await AuditTrail.logUserAction(
        'BOOKING_STARTED',
        riderId,
        'booking',
        { bookingId, clientId: booking.client_id },
        null
      )

      logger.info('Booking started successfully', { bookingId, riderId })

      return updatedBooking

    } catch (error) {
      logger.error('Failed to start booking', { error: error.message, bookingId, riderId })
      recordError('booking_start', 'error')
      throw error
    }
  }

  /**
   * Complete a booking (rider action)
   * @param {string} bookingId - Booking ID
   * @param {string} riderId - Rider ID
   * @param {Object} completionData - Completion details
   * @returns {Promise<Object>} Updated booking
   */
  async completeBooking(bookingId, riderId, completionData = {}) {
    try {
      logger.info('Completing booking', { bookingId, riderId })

      // Get booking
      const booking = await this.bookingRepository.findById(bookingId)
      if (!booking || booking.rider_id !== riderId) {
        throw new Error('Booking not found or not assigned to this rider')
      }

      // Validate state transition
      if (!this.canTransitionTo(booking.status, BOOKING_STATES.COMPLETED)) {
        throw new Error(`Cannot complete booking in ${booking.status} state`)
      }

      // Calculate actual duration
      const actualStart = new Date(booking.actual_start)
      const actualEnd = new Date()
      const actualDuration = Math.round((actualEnd - actualStart) / (1000 * 60)) // minutes

      // Update booking
      const updatedBooking = await this.bookingRepository.updateById(bookingId, {
        status: BOOKING_STATES.COMPLETED,
        actual_end: actualEnd.toISOString(),
        actual_duration: actualDuration,
        end_location: completionData.location,
        end_coordinates: completionData.coordinates,
        completion_notes: completionData.notes,
        payment_status: 'COMPLETED'
      })

      // Get transfer details (transfer already completed via destination charges)
      const transferDetails = await this.paymentService.getTransferDetails(booking)

      // Update rider availability
      await this.userRepository.updateAvailability(riderId, true, true)

      // Notify client
      await this.notificationService.notifyBookingCompleted(booking)

      // Request ratings
      await this.notificationService.requestRatings(booking)

      // Log audit trail
      await AuditTrail.logUserAction(
        'BOOKING_COMPLETED',
        riderId,
        'booking',
        { bookingId, clientId: booking.client_id, actualDuration },
        null
      )

      // Record metrics
      recordBooking(BOOKING_STATES.COMPLETED, 'RIDER', booking.total_amount)

      logger.info('Booking completed successfully', { 
        bookingId, 
        riderId, 
        actualDuration 
      })

      return updatedBooking

    } catch (error) {
      logger.error('Failed to complete booking', { error: error.message, bookingId, riderId })
      recordError('booking_completion', 'error')
      throw error
    }
  }

  /**
   * Cancel a booking
   * @param {string} bookingId - Booking ID
   * @param {string} userId - User ID (client or rider)
   * @param {string} reason - Cancellation reason
   * @returns {Promise<Object>} Updated booking
   */
  async cancelBooking(bookingId, userId, reason) {
    try {
      logger.info('Cancelling booking', { bookingId, userId, reason })

      // Get booking
      const booking = await this.bookingRepository.findById(bookingId)
      if (!booking) {
        throw new Error('Booking not found')
      }

      // Validate user can cancel
      if (booking.client_id !== userId && booking.rider_id !== userId) {
        throw new Error('User not authorized to cancel this booking')
      }

      // Validate state transition
      if (!this.canTransitionTo(booking.status, BOOKING_STATES.CANCELLED)) {
        throw new Error(`Cannot cancel booking in ${booking.status} state`)
      }

      // Calculate cancellation fee
      const cancellationFee = await this.calculateCancellationFee(booking, userId)

      // Update booking
      const updatedBooking = await this.bookingRepository.updateById(bookingId, {
        status: BOOKING_STATES.CANCELLED,
        cancelled_at: new Date().toISOString(),
        cancelled_by: userId,
        cancellation_reason: reason,
        cancellation_fee: cancellationFee
      })

      // Process refund/cancellation fee
      await this.paymentService.processCancellation(booking, cancellationFee)

      // Update rider availability if assigned
      if (booking.rider_id) {
        await this.userRepository.updateAvailability(booking.rider_id, true, true)
      }

      // Notify relevant parties
      await this.notificationService.notifyBookingCancelled(booking, reason)

      // Log audit trail
      await AuditTrail.logUserAction(
        'BOOKING_CANCELLED',
        userId,
        'booking',
        { bookingId, reason, cancellationFee },
        null
      )

      logger.info('Booking cancelled successfully', { 
        bookingId, 
        userId, 
        reason,
        cancellationFee 
      })

      return updatedBooking

    } catch (error) {
      logger.error('Failed to cancel booking', { error: error.message, bookingId, userId })
      recordError('booking_cancellation', 'error')
      throw error
    }
  }

  // ============================================================================
  // HELPER METHODS
  // ============================================================================

  /**
   * Validate booking data
   */
  async validateBookingData(data) {
    const errors = []

    // Required fields
    if (!data.serviceType) errors.push('Service type is required')
    if (!data.vehicleType) errors.push('Vehicle type is required')
    if (!data.scheduledDate) errors.push('Scheduled date is required')
    if (!data.duration) errors.push('Duration is required')
    if (!data.pickupAddress) errors.push('Pickup address is required')

    // Date validation
    const scheduledDate = new Date(data.scheduledDate)
    const now = new Date()
    const minBookingTime = new Date(now.getTime() + 30 * 60 * 1000) // 30 minutes from now

    if (scheduledDate < minBookingTime) {
      errors.push('Booking must be at least 30 minutes in advance')
    }

    // Duration validation (1-2 hours only)
    if (data.duration < 1 || data.duration > 2) {
      errors.push('Duration must be between 1 and 2 hours')
    }

    if (errors.length > 0) {
      throw new Error(`Validation failed: ${errors.join(', ')}`)
    }
  }

  /**
   * Find available riders for booking
   */
  async findAvailableRiders(bookingData) {
    const location = {
      latitude: bookingData.pickupCoordinates?.latitude,
      longitude: bookingData.pickupCoordinates?.longitude
    }

    if (!location.latitude || !location.longitude) {
      throw new Error('Pickup coordinates are required')
    }

    return await this.userRepository.getAvailableRidersNearLocation(
      location,
      25, // 25km radius
      {
        date: bookingData.scheduledDate,
        duration: bookingData.duration,
        vehicleType: bookingData.vehicleType,
        limit: 10
      }
    )
  }

  /**
   * Generate unique booking number
   */
  async generateBookingNumber() {
    const year = new Date().getFullYear()
    const timestamp = Date.now().toString().slice(-6)
    const random = Math.random().toString(36).substr(2, 4).toUpperCase()
    return `BMR${year}${timestamp}${random}`
  }

  /**
   * Check if state transition is valid
   */
  canTransitionTo(currentState, newState) {
    return BOOKING_TRANSITIONS[currentState]?.includes(newState) || false
  }

  /**
   * Calculate cancellation fee
   */
  async calculateCancellationFee(booking, userId) {
    const isClient = booking.client_id === userId
    const isRider = booking.rider_id === userId

    if (isClient) {
      // Client cancellation: platform fee is never refunded
      return booking.platform_fee
    } else if (isRider) {
      // Rider cancellation: pays 50% of total booking as penalty
      return booking.total_amount * 0.5
    }

    return 0
  }

  /**
   * Notify available riders about new booking
   */
  async notifyAvailableRiders(booking, riders) {
    for (const rider of riders) {
      await this.notificationService.notifyNewBookingAvailable(booking, rider)
    }
  }

  /**
   * Schedule booking expiration
   */
  async scheduleBookingExpiration(bookingId) {
    // In a real implementation, this would use a job queue like Bull or Agenda
    setTimeout(async () => {
      try {
        const booking = await this.bookingRepository.findById(bookingId)
        if (booking && booking.status === BOOKING_STATES.PENDING) {
          await this.expireBooking(bookingId)
        }
      } catch (error) {
        logger.error('Failed to expire booking', { error: error.message, bookingId })
      }
    }, 15 * 60 * 1000) // 15 minutes
  }

  /**
   * Expire a booking
   */
  async expireBooking(bookingId) {
    try {
      await this.bookingRepository.updateById(bookingId, {
        status: BOOKING_STATES.EXPIRED,
        expired_at: new Date().toISOString()
      })

      // Cancel payment intent
      const booking = await this.bookingRepository.findById(bookingId)
      if (booking.payment_intent_id) {
        await this.paymentService.cancelPaymentIntent(booking.payment_intent_id)
      }

      logger.info('Booking expired', { bookingId })
    } catch (error) {
      logger.error('Failed to expire booking', { error: error.message, bookingId })
    }
  }
}

export default BookingService
