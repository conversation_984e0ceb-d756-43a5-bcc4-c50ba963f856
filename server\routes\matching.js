/**
 * Matching Routes
 * API endpoints for smart rider-client matching
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import express from 'express'
import { MatchingService } from '../services/MatchingService.js'
import { authenticateClerk } from '../middleware/auth.js'
import { validationRules, handleValidationErrors, createRateLimit } from '../middleware/security.js'
import { body } from 'express-validator'

const router = express.Router()
const matchingService = new MatchingService()

// Apply rate limiting
router.use(createRateLimit)

// ============================================================================
// MATCHING ENDPOINTS
// ============================================================================

/**
 * Find best rider matches for booking request
 */
router.post('/find-matches',
  authenticateClerk,
  [
    body('startTime').notEmpty().withMessage('Start time is required'),
    body('endTime').notEmpty().withMessage('End time is required'),
    body('location').notEmpty().withMessage('Location is required')
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const bookingRequest = {
        ...req.body,
        clientId: req.auth.userId,
        id: `booking_${Date.now()}`
      }

      const options = {
        strategy: req.body.strategy || 'balanced',
        maxResults: parseInt(req.body.maxResults) || 10,
        maxDistance: parseFloat(req.body.maxDistance) || 15,
        minRating: parseFloat(req.body.minRating) || 3.0
      }

      const matches = await matchingService.findBestMatches(bookingRequest, options)

      res.json({
        success: true,
        matches,
        searchCriteria: {
          location: bookingRequest.location,
          startTime: bookingRequest.startTime,
          endTime: bookingRequest.endTime,
          strategy: options.strategy
        },
        matchCount: matches.length
      })
    } catch (error) {
      console.error('Error finding matches:', error)
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to find matches'
      })
    }
  }
)

/**
 * Calculate match score for specific rider-booking pair
 */
router.post('/calculate-score',
  authenticateClerk,
  [
    body('riderId').notEmpty().withMessage('Rider ID is required'),
    body('bookingRequest').notEmpty().withMessage('Booking request is required')
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { riderId, bookingRequest } = req.body

      // Get rider data (mock for now)
      const rider = {
        id: riderId,
        name: 'Mario Rossi',
        rating: 4.8,
        hourlyRate: 12.00,
        distance: 2.5,
        vehicleType: 'MOTO',
        available: true
      }

      const scoring = matchingService.calculateMatchScore(rider, {
        ...bookingRequest,
        clientId: req.auth.userId
      })

      res.json({
        success: true,
        riderId,
        scoring
      })
    } catch (error) {
      console.error('Error calculating score:', error)
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to calculate match score'
      })
    }
  }
)

/**
 * Get compatibility score between client and rider
 */
router.get('/compatibility/:riderId',
  authenticateClerk,
  async (req, res) => {
    try {
      const clientId = req.auth.userId
      const { riderId } = req.params

      const compatibilityScore = await matchingService.calculateCompatibilityScore(clientId, riderId)

      res.json({
        success: true,
        clientId,
        riderId,
        compatibilityScore
      })
    } catch (error) {
      console.error('Error calculating compatibility:', error)
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to calculate compatibility'
      })
    }
  }
)

// ============================================================================
// PREFERENCE MANAGEMENT
// ============================================================================

/**
 * Get client preferences
 */
router.get('/preferences',
  authenticateClerk,
  async (req, res) => {
    try {
      const clientId = req.auth.userId
      const preferences = await matchingService.getClientPreferences(clientId)

      res.json({
        success: true,
        preferences
      })
    } catch (error) {
      console.error('Error getting preferences:', error)
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to get preferences'
      })
    }
  }
)

/**
 * Update client preferences
 */
router.put('/preferences',
  authenticateClerk,
  async (req, res) => {
    try {
      const clientId = req.auth.userId
      const preferences = req.body

      // Validate preferences
      if (preferences.maxPrice && preferences.maxPrice > 15.00) {
        return res.status(400).json({
          success: false,
          message: 'Maximum price cannot exceed €15.00'
        })
      }

      if (preferences.minRating && (preferences.minRating < 1 || preferences.minRating > 5)) {
        return res.status(400).json({
          success: false,
          message: 'Rating must be between 1 and 5'
        })
      }

      await matchingService.saveClientPreferences(clientId, preferences)

      res.json({
        success: true,
        message: 'Preferences updated successfully',
        preferences
      })
    } catch (error) {
      console.error('Error updating preferences:', error)
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to update preferences'
      })
    }
  }
)

/**
 * Add rider to preferred list
 */
router.post('/preferences/preferred-riders',
  authenticateClerk,
  [
    body('riderId').notEmpty().withMessage('Rider ID is required')
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const clientId = req.auth.userId
      const { riderId } = req.body

      const preferences = await matchingService.getClientPreferences(clientId)
      
      if (!preferences.preferredRiders.includes(riderId)) {
        preferences.preferredRiders.push(riderId)
        await matchingService.saveClientPreferences(clientId, preferences)
      }

      res.json({
        success: true,
        message: 'Rider added to preferred list',
        preferredRiders: preferences.preferredRiders
      })
    } catch (error) {
      console.error('Error adding preferred rider:', error)
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to add preferred rider'
      })
    }
  }
)

/**
 * Remove rider from preferred list
 */
router.delete('/preferences/preferred-riders/:riderId',
  authenticateClerk,
  async (req, res) => {
    try {
      const clientId = req.auth.userId
      const { riderId } = req.params

      const preferences = await matchingService.getClientPreferences(clientId)
      preferences.preferredRiders = preferences.preferredRiders.filter(id => id !== riderId)
      
      await matchingService.saveClientPreferences(clientId, preferences)

      res.json({
        success: true,
        message: 'Rider removed from preferred list',
        preferredRiders: preferences.preferredRiders
      })
    } catch (error) {
      console.error('Error removing preferred rider:', error)
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to remove preferred rider'
      })
    }
  }
)

/**
 * Block rider
 */
router.post('/preferences/blocked-riders',
  authenticateClerk,
  [
    body('riderId').notEmpty().withMessage('Rider ID is required'),
    body('reason').notEmpty().withMessage('Reason is required')
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const clientId = req.auth.userId
      const { riderId, reason } = req.body

      const preferences = await matchingService.getClientPreferences(clientId)
      
      if (!preferences.blockedRiders.includes(riderId)) {
        preferences.blockedRiders.push(riderId)
        
        // Remove from preferred list if present
        preferences.preferredRiders = preferences.preferredRiders.filter(id => id !== riderId)
        
        await matchingService.saveClientPreferences(clientId, preferences)
      }

      // Log the blocking action
      console.log(`Client ${clientId} blocked rider ${riderId}. Reason: ${reason}`)

      res.json({
        success: true,
        message: 'Rider blocked successfully',
        blockedRiders: preferences.blockedRiders
      })
    } catch (error) {
      console.error('Error blocking rider:', error)
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to block rider'
      })
    }
  }
)

// ============================================================================
// ANALYTICS AND INSIGHTS
// ============================================================================

/**
 * Get matching analytics
 */
router.get('/analytics',
  authenticateClerk,
  async (req, res) => {
    try {
      const clientId = req.auth.userId
      const { startDate, endDate } = req.query

      // Mock analytics data
      const analytics = {
        period: { startDate, endDate },
        totalSearches: 45,
        averageMatchesPerSearch: 6.2,
        topMatchingFactors: [
          { factor: 'Distance', weight: 40, impact: 'High' },
          { factor: 'Rating', weight: 25, impact: 'Medium' },
          { factor: 'Price', weight: 20, impact: 'Medium' }
        ],
        preferredVehicleTypes: {
          'MOTO': 65,
          'BICI': 25,
          'AUTO': 10
        },
        averageMatchScore: 78.5,
        successfulBookings: 38,
        conversionRate: 84.4
      }

      res.json({
        success: true,
        analytics
      })
    } catch (error) {
      console.error('Error getting analytics:', error)
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to get analytics'
      })
    }
  }
)

/**
 * Get rider performance insights
 */
router.get('/insights/rider-performance',
  authenticateClerk,
  async (req, res) => {
    try {
      const { riderId } = req.query

      if (!riderId) {
        return res.status(400).json({
          success: false,
          message: 'Rider ID is required'
        })
      }

      // Mock performance insights
      const insights = {
        riderId,
        overallScore: 87.5,
        strengths: [
          'Consistently high ratings (4.8/5)',
          'Very reliable (98% completion rate)',
          'Quick response time (avg 2 minutes)'
        ],
        improvements: [
          'Could improve punctuality (5% late arrivals)',
          'Limited availability on weekends'
        ],
        matchingStats: {
          totalMatches: 156,
          averageMatchScore: 82.3,
          topMatchingReasons: [
            'Close proximity',
            'Excellent rating',
            'Competitive pricing'
          ]
        },
        clientFeedback: {
          averageRating: 4.8,
          totalReviews: 142,
          positiveComments: [
            'Very professional',
            'Always on time',
            'Friendly service'
          ]
        }
      }

      res.json({
        success: true,
        insights
      })
    } catch (error) {
      console.error('Error getting rider insights:', error)
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to get rider insights'
      })
    }
  }
)

export default router
