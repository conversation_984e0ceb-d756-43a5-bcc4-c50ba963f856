/**
 * Find Riders Page
 * Search and book available riders
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { useState } from 'react'
import { motion } from 'framer-motion'
import {
  MagnifyingGlassIcon,
  MapPinIcon,
  ClockIcon,
  StarIcon,
  TruckIcon,
  CurrencyEuroIcon,
  FunnelIcon,
  AdjustmentsHorizontalIcon
} from '@heroicons/react/24/outline'

import Card, { CardHeader, CardTitle, CardContent } from '@components/ui/Card'
import Button from '@components/ui/Button'
import Input from '@components/ui/Input'
import Header from '@components/layout/Header'
import BookingModal from '@components/booking/BookingModal'

const mockRiders = [
  {
    id: 1,
    name: '<PERSON>',
    rating: 4.8,
    hourlyRate: 12.00,
    distance: 2.5,
    vehicleType: 'MOTO',
    available: true,
    totalShifts: 156,
    responseTime: '2 min',
    matchScore: 95,
    profileImage: null,
    specialties: ['Consegne veloci', 'Zona centro'],
    nextAvailable: '2024-12-22T14:00:00Z'
  },
  {
    id: 2,
    name: '<PERSON>',
    rating: 4.6,
    hourlyRate: 11.50,
    distance: 3.2,
    vehicleType: 'BICI',
    available: true,
    totalShifts: 89,
    responseTime: '5 min',
    matchScore: 87,
    profileImage: null,
    specialties: ['Eco-friendly', 'Zona Brera'],
    nextAvailable: '2024-12-22T15:00:00Z'
  },
  {
    id: 3,
    name: 'Giuseppe Bianchi',
    rating: 4.9,
    hourlyRate: 12.50,
    distance: 4.1,
    vehicleType: 'MOTO',
    available: false,
    totalShifts: 234,
    responseTime: '1 min',
    matchScore: 92,
    profileImage: null,
    specialties: ['Esperienza premium', 'Consegne notturne'],
    nextAvailable: '2024-12-22T18:00:00Z'
  }
]

const MerchantFindRiders = () => {
  const [searchCriteria, setSearchCriteria] = useState({
    startTime: '',
    endTime: '',
    location: '',
    vehicleType: '',
    maxDistance: 10,
    minRating: 4.0,
    maxRate: 12.50
  })
  const [filteredRiders, setFilteredRiders] = useState(mockRiders)
  const [showFilters, setShowFilters] = useState(false)
  const [selectedRider, setSelectedRider] = useState(null)
  const [showBookingModal, setShowBookingModal] = useState(false)

  const handleSearch = () => {
    // Filter riders based on criteria
    let filtered = mockRiders.filter(rider => {
      if (searchCriteria.vehicleType && rider.vehicleType !== searchCriteria.vehicleType) return false
      if (rider.distance > searchCriteria.maxDistance) return false
      if (rider.rating < searchCriteria.minRating) return false
      if (rider.hourlyRate > searchCriteria.maxRate) return false
      return true
    })

    // Sort by match score
    filtered.sort((a, b) => b.matchScore - a.matchScore)
    setFilteredRiders(filtered)
  }

  const handleBookRider = (rider) => {
    setSelectedRider(rider)
    setShowBookingModal(true)
  }

  const handleBookingConfirm = async (bookingData) => {
    console.log('Booking confirmed:', bookingData)
    // API call to create booking
    setShowBookingModal(false)
    setSelectedRider(null)
  }

  const getVehicleIcon = (type) => {
    return <TruckIcon className="w-4 h-4" />
  }

  const formatDateTime = (dateString) => {
    return new Date(dateString).toLocaleString('it-IT', {
      day: 'numeric',
      month: 'short',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header 
        title="Trova Rider"
        subtitle="Cerca e prenota rider disponibili nella tua zona"
      />

      <div className="p-6 space-y-6">
        {/* Search Form */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center space-x-2">
                <MagnifyingGlassIcon className="w-5 h-5 text-blue-600" />
                <span>Criteri di Ricerca</span>
              </CardTitle>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
                leftIcon={<FunnelIcon className="w-4 h-4" />}
              >
                Filtri Avanzati
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Basic Search */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Input
                  label="Data e Ora Inizio"
                  type="datetime-local"
                  value={searchCriteria.startTime}
                  onChange={(e) => setSearchCriteria({...searchCriteria, startTime: e.target.value})}
                />
                <Input
                  label="Data e Ora Fine"
                  type="datetime-local"
                  value={searchCriteria.endTime}
                  onChange={(e) => setSearchCriteria({...searchCriteria, endTime: e.target.value})}
                />
                <Input
                  label="Zona/Indirizzo"
                  placeholder="es. Milano Centro"
                  value={searchCriteria.location}
                  onChange={(e) => setSearchCriteria({...searchCriteria, location: e.target.value})}
                  leftIcon={<MapPinIcon className="w-4 h-4" />}
                />
                <div>
                  <label className="form-label">Tipo Veicolo</label>
                  <select 
                    className="form-input"
                    value={searchCriteria.vehicleType}
                    onChange={(e) => setSearchCriteria({...searchCriteria, vehicleType: e.target.value})}
                  >
                    <option value="">Tutti</option>
                    <option value="MOTO">Moto</option>
                    <option value="BICI">Bicicletta</option>
                    <option value="AUTO">Auto</option>
                  </select>
                </div>
              </div>

              {/* Advanced Filters */}
              {showFilters && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t border-gray-200"
                >
                  <Input
                    label="Distanza Massima (km)"
                    type="number"
                    min="1"
                    max="50"
                    value={searchCriteria.maxDistance}
                    onChange={(e) => setSearchCriteria({...searchCriteria, maxDistance: parseFloat(e.target.value)})}
                  />
                  <Input
                    label="Rating Minimo"
                    type="number"
                    min="1"
                    max="5"
                    step="0.1"
                    value={searchCriteria.minRating}
                    onChange={(e) => setSearchCriteria({...searchCriteria, minRating: parseFloat(e.target.value)})}
                  />
                  <Input
                    label="Tariffa Massima (€/h)"
                    type="number"
                    min="5"
                    max="15"
                    step="0.50"
                    value={searchCriteria.maxRate}
                    onChange={(e) => setSearchCriteria({...searchCriteria, maxRate: parseFloat(e.target.value)})}
                  />
                </motion.div>
              )}

              <div className="flex justify-end">
                <Button variant="merchant" onClick={handleSearch} leftIcon={<MagnifyingGlassIcon className="w-4 h-4" />}>
                  Cerca Rider
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Search Results */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Rider Disponibili ({filteredRiders.length})</span>
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <AdjustmentsHorizontalIcon className="w-4 h-4" />
                <span>Ordinati per compatibilità</span>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {filteredRiders.map((rider, index) => (
                <motion.div
                  key={rider.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className={`p-6 border rounded-lg transition-all cursor-pointer ${
                    rider.available 
                      ? 'border-gray-200 hover:border-blue-300 hover:shadow-md' 
                      : 'border-gray-100 bg-gray-50'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-4 mb-3">
                        <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                          <span className="text-white font-bold text-lg">
                            {rider.name.split(' ').map(n => n[0]).join('')}
                          </span>
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-3">
                            <h3 className="text-lg font-semibold text-gray-900">{rider.name}</h3>
                            <div className="flex items-center space-x-1">
                              <StarIcon className="w-4 h-4 text-yellow-500 fill-current" />
                              <span className="text-sm font-medium">{rider.rating}</span>
                              <span className="text-sm text-gray-500">({rider.totalShifts} turni)</span>
                            </div>
                            <span className={`status-badge ${rider.available ? 'status-success' : 'status-warning'}`}>
                              {rider.available ? 'Disponibile' : 'Occupato'}
                            </span>
                          </div>
                          <div className="flex items-center space-x-6 text-sm text-gray-600 mt-1">
                            <span className="flex items-center space-x-1">
                              <MapPinIcon className="w-4 h-4" />
                              <span>{rider.distance} km di distanza</span>
                            </span>
                            <span className="flex items-center space-x-1">
                              {getVehicleIcon(rider.vehicleType)}
                              <span>{rider.vehicleType}</span>
                            </span>
                            <span className="flex items-center space-x-1">
                              <ClockIcon className="w-4 h-4" />
                              <span>Risponde in {rider.responseTime}</span>
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-4 mb-3">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <span className="text-sm font-medium text-gray-700">Compatibilità:</span>
                            <div className="flex-1 bg-gray-200 rounded-full h-2">
                              <div 
                                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${rider.matchScore}%` }}
                              ></div>
                            </div>
                            <span className="text-sm font-medium text-blue-600">{rider.matchScore}%</span>
                          </div>
                          <div className="flex flex-wrap gap-2">
                            {rider.specialties.map((specialty, i) => (
                              <span key={i} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                                {specialty}
                              </span>
                            ))}
                          </div>
                        </div>
                      </div>

                      {!rider.available && (
                        <div className="text-sm text-gray-600 mb-3">
                          <span className="flex items-center space-x-1">
                            <ClockIcon className="w-4 h-4" />
                            <span>Prossima disponibilità: {formatDateTime(rider.nextAvailable)}</span>
                          </span>
                        </div>
                      )}
                    </div>

                    <div className="text-right space-y-3">
                      <div>
                        <p className="text-2xl font-bold text-gray-900">€{rider.hourlyRate.toFixed(2)}</p>
                        <p className="text-sm text-gray-600">per ora</p>
                      </div>
                      <div className="space-y-2">
                        {rider.available ? (
                          <>
                            <Button 
                              variant="merchant" 
                              size="sm" 
                              fullWidth
                              onClick={() => handleBookRider(rider)}
                            >
                              Prenota Ora
                            </Button>
                            <Button variant="outline" size="sm" fullWidth>
                              Visualizza Profilo
                            </Button>
                          </>
                        ) : (
                          <>
                            <Button variant="outline" size="sm" fullWidth disabled>
                              Non Disponibile
                            </Button>
                            <Button variant="ghost" size="sm" fullWidth>
                              Prenota per Dopo
                            </Button>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            {filteredRiders.length === 0 && (
              <div className="text-center py-12">
                <MagnifyingGlassIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Nessun rider trovato</h3>
                <p className="text-gray-600 mb-4">
                  Prova a modificare i criteri di ricerca o espandere l'area di ricerca
                </p>
                <Button variant="outline" onClick={() => setSearchCriteria({
                  ...searchCriteria,
                  maxDistance: searchCriteria.maxDistance + 5,
                  minRating: Math.max(3.0, searchCriteria.minRating - 0.5)
                })}>
                  Espandi Ricerca
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Booking Modal */}
        <BookingModal
          isOpen={showBookingModal}
          onClose={() => {
            setShowBookingModal(false)
            setSelectedRider(null)
          }}
          rider={selectedRider}
          onConfirm={handleBookingConfirm}
        />
      </div>
    </div>
  )
}

export default MerchantFindRiders
