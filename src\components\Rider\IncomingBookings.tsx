import React from 'react'
import { Calendar, Clock, MapPin, User, Euro, CheckCircle, XCircle } from 'lucide-react'

interface IncomingBooking {
  id: string
  client: {
    name: string
    first_name?: string
    last_name?: string
  }
  date: string
  duration: number
  address: string
  instructions?: string
  hourly_rate: number
  total_amount: number
  status: 'PENDING' | 'CONFIRMED' | 'CANCELLED'
  created_at: string
}

interface IncomingBookingsProps {
  bookings: IncomingBooking[]
  loading: boolean
  onAccept?: (bookingId: string) => void
  onReject?: (bookingId: string) => void
}

const IncomingBookings: React.FC<IncomingBookingsProps> = ({
  bookings,
  loading,
  onAccept,
  onReject
}) => {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('it-IT', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const formatTime = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleTimeString('it-IT', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'CONFIRMED':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'CANCELLED':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'In attesa'
      case 'CONFIRMED':
        return 'Confermata'
      case 'CANCELLED':
        return 'Annullata'
      default:
        return status
    }
  }

  if (loading) {
    return (
      <div className="bg-white rounded-lg p-6 border border-gray-200">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-20 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg p-6 border border-gray-200">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <Calendar className="w-5 h-5 mr-2 text-purple-600" />
            Prenotazioni in Arrivo
          </h3>
          <p className="text-sm text-gray-600 mt-1">
            Gestisci le richieste di prenotazione degli esercenti
          </p>
        </div>
        
        {bookings.length > 0 && (
          <span className="bg-purple-100 text-purple-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
            {bookings.filter(b => b.status === 'PENDING').length} in attesa
          </span>
        )}
      </div>

      {bookings.length === 0 ? (
        <div className="text-center py-8">
          <Calendar className="w-12 h-12 text-gray-300 mx-auto mb-4" />
          <p className="text-gray-500 text-lg font-medium">Nessuna prenotazione</p>
          <p className="text-gray-400 text-sm mt-1">
            Le nuove richieste di prenotazione appariranno qui
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {bookings.map((booking) => (
            <div key={booking.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow">
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <User className="w-4 h-4 text-gray-500" />
                    <span className="font-medium text-gray-900">
                      {booking.client.name || `${booking.client.first_name || ''} ${booking.client.last_name || ''}`.trim() || 'Cliente'}
                    </span>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-600">
                    <div className="flex items-center space-x-2">
                      <Calendar className="w-4 h-4" />
                      <span>{formatDate(booking.date)}</span>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Clock className="w-4 h-4" />
                      <span>{formatTime(booking.date)} ({booking.duration} ora{booking.duration > 1 ? 'e' : ''})</span>
                    </div>
                    
                    <div className="flex items-center space-x-2 md:col-span-2">
                      <MapPin className="w-4 h-4" />
                      <span className="truncate">{booking.address}</span>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Euro className="w-4 h-4" />
                      <span className="font-medium">€{booking.total_amount.toFixed(2)} totale</span>
                    </div>
                  </div>

                  {booking.instructions && (
                    <div className="mt-3 p-2 bg-gray-50 rounded text-sm">
                      <span className="font-medium text-gray-700">Istruzioni: </span>
                      <span className="text-gray-600">{booking.instructions}</span>
                    </div>
                  )}
                </div>

                <div className="ml-4 flex flex-col items-end space-y-2">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(booking.status)}`}>
                    {getStatusText(booking.status)}
                  </span>

                  {booking.status === 'PENDING' && onAccept && onReject && (
                    <div className="flex space-x-2">
                      <button
                        onClick={() => onAccept(booking.id)}
                        className="flex items-center space-x-1 bg-green-100 hover:bg-green-200 text-green-700 px-3 py-1 rounded text-sm font-medium transition-colors"
                      >
                        <CheckCircle className="w-4 h-4" />
                        <span>Accetta</span>
                      </button>
                      
                      <button
                        onClick={() => onReject(booking.id)}
                        className="flex items-center space-x-1 bg-red-100 hover:bg-red-200 text-red-700 px-3 py-1 rounded text-sm font-medium transition-colors"
                      >
                        <XCircle className="w-4 h-4" />
                        <span>Rifiuta</span>
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export default IncomingBookings
