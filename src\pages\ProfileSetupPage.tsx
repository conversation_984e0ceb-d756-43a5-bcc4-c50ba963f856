import React, { useState, useEffect } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { Store, Bike } from 'lucide-react'
import { useAuth } from '../contexts/AuthContext'

const ProfileSetupPage: React.FC = () => {
  const [searchParams] = useSearchParams()
  const preselectedType = searchParams.get('type') // 'esercente' o 'rider'
  const [selectedType, setSelectedType] = useState<'ESERCENTE' | 'RIDER' | null>(null)
  const [loading, setLoading] = useState(false)
  const { updateUserProfile, user, isSignedIn, loading: authLoading, getUserType, isProfileComplete } = useAuth()
  const navigate = useNavigate()

  // Preseleziona il tipo se arriva dalla registrazione o se già impostato
  useEffect(() => {
    if (preselectedType === 'esercente') {
      setSelectedType('ESERCENTE')
    } else if (preselectedType === 'rider') {
      setSelectedType('RIDER')
    } else {
      // Controlla se l'utente ha già un tipo impostato
      const currentType = getUserType()
      if (currentType) {
        setSelectedType(currentType)
      }
    }
  }, [preselectedType, getUserType])

  // Reindirizza se il profilo è già completo
  useEffect(() => {
    if (!authLoading && isSignedIn && isProfileComplete()) {
      navigate('/app', { replace: true })
    }
  }, [authLoading, isSignedIn, isProfileComplete, navigate])

  const handleSubmit = async () => {
    if (!selectedType) return

    setLoading(true)
    try {
      const success = await updateUserProfile({ type: selectedType })
      if (success) {
        navigate('/app')
      }
    } finally {
      setLoading(false)
    }
  }

  // Mostra loading se sta verificando l'autenticazione
  if (authLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Caricamento...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-6">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <img 
            src="/logo.svg" 
            alt="bemyrider logo" 
            className="w-16 h-16 mx-auto mb-4"
          />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            {preselectedType ? 'Conferma il tuo profilo' : 'Completa il tuo profilo'}
          </h2>
          <p className="text-gray-600">
            {preselectedType
              ? 'Conferma il tipo di account selezionato'
              : 'Seleziona il tipo di account per iniziare'
            }
          </p>
          {preselectedType && (
            <div className="mt-4 text-sm text-purple-600 bg-purple-50 rounded-lg p-3">
              Hai scelto di registrarti come <strong>{preselectedType === 'esercente' ? 'Esercente' : 'Rider'}</strong>
            </div>
          )}
        </div>

        <div className="space-y-4 mb-8">
          {/* Esercente Option */}
          <div
            className={`w-full border-2 rounded-lg p-6 transition-all ${
              selectedType === 'ESERCENTE'
                ? 'border-purple-600 bg-purple-50'
                : preselectedType && preselectedType !== 'esercente'
                  ? 'border-gray-200 bg-gray-50 opacity-50'
                  : 'border-gray-300'
            } ${!preselectedType ? 'cursor-pointer hover:border-gray-400' : ''}`}
            onClick={!preselectedType ? () => setSelectedType('ESERCENTE') : undefined}
          >
            <div className="text-center">
              <Store className={`w-12 h-12 mx-auto mb-3 ${
                selectedType === 'ESERCENTE' ? 'text-purple-600' : 'text-gray-400'
              }`} />
              <div className={`font-semibold text-lg mb-1 ${
                selectedType === 'ESERCENTE' ? 'text-gray-900' : 'text-gray-500'
              }`}>
                Esercente
                {preselectedType === 'esercente' && (
                  <span className="ml-2 text-sm text-purple-600">✓ Selezionato</span>
                )}
              </div>
              <div className="text-sm text-gray-600">
                Ho un'attività e cerco rider per le consegne
              </div>
            </div>
          </div>

          {/* Rider Option */}
          <div
            className={`w-full border-2 rounded-lg p-6 transition-all ${
              selectedType === 'RIDER'
                ? 'border-purple-600 bg-purple-50'
                : preselectedType && preselectedType !== 'rider'
                  ? 'border-gray-200 bg-gray-50 opacity-50'
                  : 'border-gray-300'
            } ${!preselectedType ? 'cursor-pointer hover:border-gray-400' : ''}`}
            onClick={!preselectedType ? () => setSelectedType('RIDER') : undefined}
          >
            <div className="text-center">
              <Bike className={`w-12 h-12 mx-auto mb-3 ${
                selectedType === 'RIDER' ? 'text-purple-600' : 'text-gray-400'
              }`} />
              <div className={`font-semibold text-lg mb-1 ${
                selectedType === 'RIDER' ? 'text-gray-900' : 'text-gray-500'
              }`}>
                Rider
                {preselectedType === 'rider' && (
                  <span className="ml-2 text-sm text-purple-600">✓ Selezionato</span>
                )}
              </div>
              <div className="text-sm text-gray-600">
                Offro servizi di consegna per esercenti
              </div>
            </div>
          </div>
        </div>

        <button
          onClick={handleSubmit}
          disabled={!selectedType || loading}
          className="w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading
            ? 'Configurazione in corso...'
            : preselectedType
              ? 'Conferma e Continua'
              : 'Continua'
          }
        </button>

        <div className="mt-6 text-center text-sm text-gray-500">
          Potrai sempre modificare queste impostazioni nel tuo profilo
        </div>
      </div>
    </div>
  )
}

export default ProfileSetupPage
