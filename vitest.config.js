/**
 * Vitest Configuration
 * Enterprise-grade testing setup for BeMyRider
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { defineConfig } from 'vitest/config'
import path from 'path'

export default defineConfig({
  test: {
    // Test environment
    environment: 'node',
    
    // Global test setup
    globals: true,
    
    // Test file patterns
    include: [
      'tests/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts}',
      'src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts}',
      'server/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts}'
    ],
    
    // Exclude patterns
    exclude: [
      'node_modules',
      'dist',
      'build',
      'coverage',
      '.git'
    ],
    
    // Setup files
    setupFiles: [
      './tests/setup/global-setup.js',
      './tests/setup/test-environment.js'
    ],
    
    // Coverage configuration
    coverage: {
      provider: 'c8',
      reporter: ['text', 'json', 'html', 'lcov'],
      reportsDirectory: './coverage',
      exclude: [
        'node_modules/',
        'tests/',
        'coverage/',
        'dist/',
        'build/',
        '**/*.config.js',
        '**/*.config.ts',
        '**/types.ts',
        '**/types.js'
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      }
    },
    
    // Test timeout
    testTimeout: 30000,
    hookTimeout: 30000,
    
    // Parallel execution
    threads: true,
    maxThreads: 4,
    minThreads: 1,
    
    // Watch mode
    watch: false,
    
    // Reporter configuration
    reporter: ['verbose', 'json', 'html'],
    outputFile: {
      json: './test-results/results.json',
      html: './test-results/results.html'
    },
    
    // Mock configuration
    clearMocks: true,
    restoreMocks: true,
    
    // Environment variables for testing
    env: {
      NODE_ENV: 'test',
      VITE_SUPABASE_URL: 'https://test-project.supabase.co',
      VITE_SUPABASE_ANON_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test',
      CLERK_SECRET_KEY: 'sk_test_mock_key_for_testing',
      VITE_CLERK_PUBLISHABLE_KEY: 'pk_test_mock_key_for_testing',
      PORT: '5001', // Different port for testing
      FRONTEND_URL: 'http://localhost:3000'
    }
  },
  
  // Resolve configuration
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@server': path.resolve(__dirname, './server'),
      '@tests': path.resolve(__dirname, './tests')
    }
  },
  
  // Define configuration
  define: {
    __TEST__: true
  }
})
