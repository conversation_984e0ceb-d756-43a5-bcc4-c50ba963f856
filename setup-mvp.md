# 🚀 Setup MVP Rapido - bemyrider

## ✅ Checklist Setup

### 1. Database Supabase
- [ ] Account creato su supabase.com
- [ ] Progetto "bemyrider" creato
- [ ] Database URL copiata
- [ ] File `server/.env` aggiornato con DATABASE_URL

### 2. Stripe Setup
- [ ] Account creato su stripe.com
- [ ] Stripe Connect attivato
- [ ] Chiavi API copiate (pk_test_ e sk_test_)
- [ ] File `server/.env` aggiornato con STRIPE_SECRET_KEY
- [ ] File `.env` aggiornato con VITE_STRIPE_PUBLISHABLE_KEY

### 3. <PERSON><PERSON><PERSON> da Eseguire
```bash
# 1. Setup database
npm run db:generate
npm run db:push
npm run db:seed

# 2. Riavvia server backend
# Ctrl+C per fermare il server corrente, poi:
npm run server

# 3. Test login con account di esempio:
# Email: <EMAIL>
# Password: password123
```

## 🎯 Test Demo Completa

### Account di Test Creati
1. **Esercente**: <EMAIL> / password123
2. **Rider**: <EMAIL> / password123

### Flusso di Test
1. **Login** come esercente
2. **Cerca rider** nella sezione "Trova Rider"
3. **Visualizza profilo** di un rider
4. **Crea prenotazione** per 1-2 ore
5. **Test pagamento** con carta di test Stripe

### Carte di Test Stripe
- **Successo**: 4242 4242 4242 4242
- **Fallimento**: 4000 0000 0000 0002
- **3D Secure**: 4000 0025 0000 3155

## 🐛 Troubleshooting

### Database Connection Error
```bash
# Verifica che DATABASE_URL sia corretto
# Formato: postgresql://postgres:<EMAIL>:5432/postgres
```

### Stripe Error
```bash
# Verifica che le chiavi inizino con:
# STRIPE_SECRET_KEY=sk_test_...
# VITE_STRIPE_PUBLISHABLE_KEY=pk_test_...
```

### Server Non Risponde
```bash
# Riavvia backend
npm run server

# Verifica che sia su porta 5000
# http://localhost:5000/api/health
```

## 🎉 Demo Ready!

Una volta completati tutti i passaggi, avrai:
- ✅ Database funzionante con dati di esempio
- ✅ Autenticazione reale
- ✅ Sistema di prenotazioni
- ✅ Integrazione Stripe base
- ✅ Demo completa funzionante

**Tempo stimato**: 15-20 minuti
**Risultato**: MVP demo-ready per presentazioni!
