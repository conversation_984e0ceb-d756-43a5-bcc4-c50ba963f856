/**
 * Compliance and Audit System
 * GDPR, PCI DSS, and audit trail implementation
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { auditLogger, securityLogger } from './logger.js'
import { encryptPII, decryptPII, maskEmail, maskPhone } from './encryption.js'
import { getEnv } from './env-validator.js'

// ============================================================================
// COMPLIANCE CONFIGURATION
// ============================================================================

const COMPLIANCE_STANDARDS = {
  GDPR: {
    dataRetentionPeriod: 365 * 24 * 60 * 60 * 1000, // 1 year in milliseconds
    consentRequired: true,
    rightToBeForgotten: true,
    dataPortability: true,
    breachNotificationTime: 72 * 60 * 60 * 1000 // 72 hours
  },
  
  PCI_DSS: {
    cardDataRetention: 0, // No card data storage
    encryptionRequired: true,
    accessLogging: true,
    regularTesting: true
  },
  
  SOC2: {
    accessControls: true,
    dataClassification: true,
    incidentResponse: true,
    changeManagement: true
  }
}

const PII_FIELDS = [
  'email',
  'phone',
  'firstName',
  'lastName',
  'address',
  'taxCode',
  'iban',
  'dateOfBirth'
]

const SENSITIVE_OPERATIONS = [
  'USER_CREATE',
  'USER_UPDATE',
  'USER_DELETE',
  'PAYMENT_PROCESS',
  'DATA_EXPORT',
  'DATA_DELETE',
  'ADMIN_ACCESS',
  'SECURITY_CHANGE'
]

// ============================================================================
// AUDIT TRAIL SYSTEM
// ============================================================================

class AuditTrail {
  /**
   * Log user action for audit trail
   */
  static async logUserAction(action, userId, resource, details = {}, req = null) {
    const auditEntry = {
      timestamp: new Date().toISOString(),
      action,
      userId,
      resource,
      details: this.sanitizeAuditDetails(details),
      metadata: {
        ip: req?.ip || req?.connection?.remoteAddress,
        userAgent: req?.headers?.['user-agent'],
        sessionId: req?.auth?.sessionId,
        correlationId: req?.correlationId
      }
    }

    // Log to audit system
    auditLogger.info('User Action', auditEntry)

    // Store in database for compliance
    await this.storeAuditRecord(auditEntry)

    // Check if this is a sensitive operation
    if (SENSITIVE_OPERATIONS.includes(action)) {
      securityLogger.warn('Sensitive Operation', auditEntry)
    }

    return auditEntry
  }

  /**
   * Log admin action with enhanced tracking
   */
  static async logAdminAction(action, adminId, targetUserId, resource, details = {}, req = null) {
    const auditEntry = {
      timestamp: new Date().toISOString(),
      action,
      adminId,
      targetUserId,
      resource,
      details: this.sanitizeAuditDetails(details),
      metadata: {
        ip: req?.ip || req?.connection?.remoteAddress,
        userAgent: req?.headers?.['user-agent'],
        sessionId: req?.auth?.sessionId,
        correlationId: req?.correlationId
      },
      adminAction: true
    }

    // Enhanced logging for admin actions
    auditLogger.warn('Admin Action', auditEntry)
    securityLogger.warn('Admin Action Performed', auditEntry)

    await this.storeAuditRecord(auditEntry)

    return auditEntry
  }

  /**
   * Log data access for compliance
   */
  static async logDataAccess(userId, dataType, purpose, details = {}, req = null) {
    const auditEntry = {
      timestamp: new Date().toISOString(),
      action: 'DATA_ACCESS',
      userId,
      dataType,
      purpose,
      details: this.sanitizeAuditDetails(details),
      metadata: {
        ip: req?.ip || req?.connection?.remoteAddress,
        userAgent: req?.headers?.['user-agent'],
        sessionId: req?.auth?.sessionId,
        correlationId: req?.correlationId
      }
    }

    auditLogger.info('Data Access', auditEntry)
    await this.storeAuditRecord(auditEntry)

    return auditEntry
  }

  /**
   * Sanitize audit details to remove sensitive information
   */
  static sanitizeAuditDetails(details) {
    const sanitized = { ...details }

    // Remove or mask sensitive fields
    PII_FIELDS.forEach(field => {
      if (sanitized[field]) {
        if (field === 'email') {
          sanitized[field] = maskEmail(sanitized[field])
        } else if (field === 'phone') {
          sanitized[field] = maskPhone(sanitized[field])
        } else {
          sanitized[field] = '[REDACTED]'
        }
      }
    })

    // Remove passwords and tokens
    delete sanitized.password
    delete sanitized.token
    delete sanitized.secret
    delete sanitized.key

    return sanitized
  }

  /**
   * Store audit record in database
   */
  static async storeAuditRecord(auditEntry) {
    try {
      // In a real implementation, this would store in the audit_logs table
      // For now, we'll just log it
      console.log('AUDIT_RECORD:', JSON.stringify(auditEntry))
    } catch (error) {
      securityLogger.error('Failed to store audit record', { error: error.message })
    }
  }

  /**
   * Generate audit report for compliance
   */
  static async generateAuditReport(startDate, endDate, userId = null) {
    try {
      // Query audit logs from database
      const auditLogs = await this.queryAuditLogs(startDate, endDate, userId)

      const report = {
        period: { startDate, endDate },
        userId,
        summary: {
          totalActions: auditLogs.length,
          uniqueUsers: new Set(auditLogs.map(log => log.userId)).size,
          actionTypes: this.groupBy(auditLogs, 'action'),
          sensitiveOperations: auditLogs.filter(log => 
            SENSITIVE_OPERATIONS.includes(log.action)
          ).length
        },
        logs: auditLogs
      }

      return report
    } catch (error) {
      securityLogger.error('Failed to generate audit report', { error: error.message })
      throw error
    }
  }

  /**
   * Query audit logs from database
   */
  static async queryAuditLogs(startDate, endDate, userId = null) {
    // Mock implementation - in reality, this would query the database
    return []
  }

  /**
   * Group array by field
   */
  static groupBy(array, field) {
    return array.reduce((acc, item) => {
      const key = item[field]
      acc[key] = (acc[key] || 0) + 1
      return acc
    }, {})
  }
}

// ============================================================================
// GDPR COMPLIANCE
// ============================================================================

class GDPRCompliance {
  /**
   * Process data subject request (Article 15 - Right of access)
   */
  static async processDataSubjectRequest(userId, requestType) {
    await AuditTrail.logUserAction('GDPR_REQUEST', userId, 'user_data', {
      requestType,
      article: this.getGDPRArticle(requestType)
    })

    switch (requestType) {
      case 'ACCESS':
        return await this.exportUserData(userId)
      case 'RECTIFICATION':
        return await this.prepareDataRectification(userId)
      case 'ERASURE':
        return await this.processRightToBeForgotten(userId)
      case 'PORTABILITY':
        return await this.exportPortableData(userId)
      case 'RESTRICTION':
        return await this.restrictDataProcessing(userId)
      default:
        throw new Error('Invalid GDPR request type')
    }
  }

  /**
   * Export all user data (Article 15)
   */
  static async exportUserData(userId) {
    try {
      // Collect all user data from various tables
      const userData = {
        profile: await this.getUserProfile(userId),
        bookings: await this.getUserBookings(userId),
        reviews: await this.getUserReviews(userId),
        payments: await this.getUserPayments(userId),
        notifications: await this.getUserNotifications(userId),
        auditLogs: await this.getUserAuditLogs(userId)
      }

      // Decrypt PII fields for export
      userData.profile = decryptPII(userData.profile, PII_FIELDS)

      await AuditTrail.logDataAccess(userId, 'FULL_EXPORT', 'GDPR_ACCESS_REQUEST')

      return {
        exportDate: new Date().toISOString(),
        userId,
        data: userData,
        format: 'JSON',
        gdprCompliant: true
      }
    } catch (error) {
      securityLogger.error('Failed to export user data', { userId, error: error.message })
      throw error
    }
  }

  /**
   * Process right to be forgotten (Article 17)
   */
  static async processRightToBeForgotten(userId) {
    try {
      // Check if user has active bookings or legal obligations
      const hasActiveBookings = await this.hasActiveBookings(userId)
      const hasLegalObligations = await this.hasLegalObligations(userId)

      if (hasActiveBookings || hasLegalObligations) {
        return {
          status: 'REJECTED',
          reason: 'Active bookings or legal obligations prevent deletion',
          canDeleteAfter: await this.calculateDeletionDate(userId)
        }
      }

      // Anonymize user data instead of hard delete
      await this.anonymizeUserData(userId)

      await AuditTrail.logUserAction('GDPR_ERASURE', userId, 'user_data', {
        method: 'ANONYMIZATION',
        article: 'Article 17'
      })

      return {
        status: 'COMPLETED',
        method: 'ANONYMIZATION',
        completedAt: new Date().toISOString()
      }
    } catch (error) {
      securityLogger.error('Failed to process right to be forgotten', { userId, error: error.message })
      throw error
    }
  }

  /**
   * Export portable data (Article 20)
   */
  static async exportPortableData(userId) {
    try {
      const portableData = {
        profile: await this.getUserProfile(userId),
        bookings: await this.getUserBookings(userId),
        reviews: await this.getUserReviews(userId)
      }

      // Format for portability (structured, machine-readable)
      const exportData = {
        format: 'JSON',
        version: '1.0',
        exportDate: new Date().toISOString(),
        userId,
        data: portableData
      }

      await AuditTrail.logDataAccess(userId, 'PORTABLE_EXPORT', 'GDPR_PORTABILITY_REQUEST')

      return exportData
    } catch (error) {
      securityLogger.error('Failed to export portable data', { userId, error: error.message })
      throw error
    }
  }

  /**
   * Anonymize user data
   */
  static async anonymizeUserData(userId) {
    const anonymizedData = {
      email: `anonymized_${Date.now()}@deleted.local`,
      firstName: 'DELETED',
      lastName: 'USER',
      phone: null,
      address: null,
      dateOfBirth: null,
      deletedAt: new Date().toISOString(),
      gdprDeleted: true
    }

    // Update user record with anonymized data
    // In real implementation, this would update the database
    console.log('Anonymizing user data:', { userId, anonymizedData })
  }

  /**
   * Get GDPR article for request type
   */
  static getGDPRArticle(requestType) {
    const articles = {
      ACCESS: 'Article 15',
      RECTIFICATION: 'Article 16',
      ERASURE: 'Article 17',
      RESTRICTION: 'Article 18',
      PORTABILITY: 'Article 20'
    }
    return articles[requestType] || 'Unknown'
  }

  // Mock data access methods (would be real database queries)
  static async getUserProfile(userId) { return {} }
  static async getUserBookings(userId) { return [] }
  static async getUserReviews(userId) { return [] }
  static async getUserPayments(userId) { return [] }
  static async getUserNotifications(userId) { return [] }
  static async getUserAuditLogs(userId) { return [] }
  static async hasActiveBookings(userId) { return false }
  static async hasLegalObligations(userId) { return false }
  static async calculateDeletionDate(userId) { return new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) }
}

// ============================================================================
// DATA BREACH NOTIFICATION
// ============================================================================

class DataBreachNotification {
  /**
   * Report data breach
   */
  static async reportBreach(breachDetails) {
    const breach = {
      id: this.generateBreachId(),
      timestamp: new Date().toISOString(),
      ...breachDetails,
      status: 'REPORTED',
      notificationsSent: []
    }

    // Log security incident
    securityLogger.error('Data Breach Reported', breach)

    // Assess breach severity
    const assessment = await this.assessBreachSeverity(breach)
    breach.assessment = assessment

    // Notify authorities if required (within 72 hours for GDPR)
    if (assessment.requiresAuthorityNotification) {
      await this.notifyAuthorities(breach)
    }

    // Notify affected users if required
    if (assessment.requiresUserNotification) {
      await this.notifyAffectedUsers(breach)
    }

    return breach
  }

  /**
   * Assess breach severity
   */
  static async assessBreachSeverity(breach) {
    const assessment = {
      riskLevel: 'LOW',
      affectedUsers: breach.affectedUsers || 0,
      dataTypes: breach.dataTypes || [],
      requiresAuthorityNotification: false,
      requiresUserNotification: false,
      timeline: {
        discovered: breach.timestamp,
        reportDeadline: new Date(Date.now() + COMPLIANCE_STANDARDS.GDPR.breachNotificationTime).toISOString()
      }
    }

    // Assess risk based on data types
    const sensitiveDataTypes = ['email', 'phone', 'payment', 'identity']
    const hasSensitiveData = assessment.dataTypes.some(type => 
      sensitiveDataTypes.includes(type)
    )

    if (hasSensitiveData || assessment.affectedUsers > 100) {
      assessment.riskLevel = 'HIGH'
      assessment.requiresAuthorityNotification = true
      assessment.requiresUserNotification = true
    } else if (assessment.affectedUsers > 10) {
      assessment.riskLevel = 'MEDIUM'
      assessment.requiresUserNotification = true
    }

    return assessment
  }

  /**
   * Generate breach ID
   */
  static generateBreachId() {
    return `BREACH_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Notify authorities
   */
  static async notifyAuthorities(breach) {
    // In production, this would integrate with relevant authorities
    console.log('Notifying authorities of data breach:', breach.id)
  }

  /**
   * Notify affected users
   */
  static async notifyAffectedUsers(breach) {
    // In production, this would send notifications to affected users
    console.log('Notifying affected users of data breach:', breach.id)
  }
}

// ============================================================================
// COMPLIANCE MIDDLEWARE
// ============================================================================

/**
 * Middleware to ensure compliance with data protection regulations
 */
export const complianceMiddleware = (req, res, next) => {
  // Add compliance headers
  res.setHeader('X-Content-Type-Options', 'nosniff')
  res.setHeader('X-Frame-Options', 'DENY')
  res.setHeader('X-XSS-Protection', '1; mode=block')

  // Log data access for audit trail
  if (req.auth?.userId && req.method !== 'OPTIONS') {
    AuditTrail.logDataAccess(
      req.auth.userId,
      'API_ACCESS',
      `${req.method} ${req.originalUrl}`,
      { endpoint: req.originalUrl },
      req
    ).catch(error => {
      securityLogger.error('Failed to log data access', { error: error.message })
    })
  }

  next()
}

// ============================================================================
// EXPORTS
// ============================================================================

export {
  AuditTrail,
  GDPRCompliance,
  DataBreachNotification,
  COMPLIANCE_STANDARDS,
  PII_FIELDS,
  SENSITIVE_OPERATIONS
}

export default {
  AuditTrail,
  GDPRCompliance,
  DataBreachNotification,
  complianceMiddleware
}
