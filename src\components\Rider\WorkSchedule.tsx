import React, { useState } from 'react'
import { Clock, Plus, Trash2, Save } from 'lucide-react'

interface TimeSlot {
  start: string // HH:MM format
  end: string   // HH:MM format
}

interface WorkSchedule {
  monday: TimeSlot[]
  tuesday: TimeSlot[]
  wednesday: TimeSlot[]
  thursday: TimeSlot[]
  friday: TimeSlot[]
  saturday: TimeSlot[]
  sunday: TimeSlot[]
}

interface WorkScheduleProps {
  schedule: WorkSchedule
  onSave: (schedule: WorkSchedule) => Promise<boolean>
  loading: boolean
}

const WorkScheduleComponent: React.FC<WorkScheduleProps> = ({
  schedule,
  onSave,
  loading
}) => {
  const [localSchedule, setLocalSchedule] = useState<WorkSchedule>(schedule)
  const [hasChanges, setHasChanges] = useState(false)

  const dayNames = {
    monday: 'Lunedì',
    tuesday: 'Martedì', 
    wednesday: 'Mercoledì',
    thursday: 'Giovedì',
    friday: 'Venerdì',
    saturday: 'Sabato',
    sunday: 'Domenica'
  }

  const addTimeSlot = (day: keyof WorkSchedule) => {
    const newSlot: TimeSlot = { start: '09:00', end: '17:00' }
    setLocalSchedule(prev => ({
      ...prev,
      [day]: [...prev[day], newSlot]
    }))
    setHasChanges(true)
  }

  const removeTimeSlot = (day: keyof WorkSchedule, index: number) => {
    setLocalSchedule(prev => ({
      ...prev,
      [day]: prev[day].filter((_, i) => i !== index)
    }))
    setHasChanges(true)
  }

  const updateTimeSlot = (day: keyof WorkSchedule, index: number, field: 'start' | 'end', value: string) => {
    setLocalSchedule(prev => ({
      ...prev,
      [day]: prev[day].map((slot, i) => 
        i === index ? { ...slot, [field]: value } : slot
      )
    }))
    setHasChanges(true)
  }

  const handleSave = async () => {
    const success = await onSave(localSchedule)
    if (success) {
      setHasChanges(false)
    }
  }

  const isValidTimeSlot = (slot: TimeSlot) => {
    return slot.start < slot.end
  }

  const hasInvalidSlots = () => {
    return Object.values(localSchedule).some(daySlots =>
      daySlots.some(slot => !isValidTimeSlot(slot))
    )
  }

  return (
    <div className="bg-white rounded-lg p-6 border border-gray-200">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <Clock className="w-5 h-5 mr-2 text-purple-600" />
            Orari di Lavoro
          </h3>
          <p className="text-sm text-gray-600 mt-1">
            Imposta i tuoi orari di disponibilità per ogni giorno della settimana
          </p>
        </div>

        {hasChanges && (
          <button
            onClick={handleSave}
            disabled={loading || hasInvalidSlots()}
            className={`
              flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200
              ${hasInvalidSlots() 
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-purple-600 hover:bg-purple-700 text-white hover:shadow-sm'
              }
              ${loading ? 'opacity-50 cursor-not-allowed' : ''}
            `}
          >
            {loading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current" />
            ) : (
              <Save className="w-4 h-4" />
            )}
            <span>Salva Orari</span>
          </button>
        )}
      </div>

      <div className="space-y-4">
        {Object.entries(dayNames).map(([dayKey, dayName]) => {
          const daySlots = localSchedule[dayKey as keyof WorkSchedule]
          
          return (
            <div key={dayKey} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium text-gray-900">{dayName}</h4>
                <button
                  onClick={() => addTimeSlot(dayKey as keyof WorkSchedule)}
                  className="flex items-center space-x-1 text-sm text-purple-600 hover:text-purple-700"
                >
                  <Plus className="w-4 h-4" />
                  <span>Aggiungi Orario</span>
                </button>
              </div>

              {daySlots.length === 0 ? (
                <p className="text-sm text-gray-500 italic">Nessun orario impostato</p>
              ) : (
                <div className="space-y-2">
                  {daySlots.map((slot, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <div className="flex items-center space-x-2">
                        <input
                          type="time"
                          value={slot.start}
                          onChange={(e) => updateTimeSlot(dayKey as keyof WorkSchedule, index, 'start', e.target.value)}
                          className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                        />
                        <span className="text-gray-500">-</span>
                        <input
                          type="time"
                          value={slot.end}
                          onChange={(e) => updateTimeSlot(dayKey as keyof WorkSchedule, index, 'end', e.target.value)}
                          className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                        />
                      </div>

                      {!isValidTimeSlot(slot) && (
                        <span className="text-xs text-red-500">Orario non valido</span>
                      )}

                      <button
                        onClick={() => removeTimeSlot(dayKey as keyof WorkSchedule, index)}
                        className="text-red-500 hover:text-red-700 p-1"
                        title="Rimuovi orario"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )
        })}
      </div>

      {hasInvalidSlots() && (
        <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-sm text-red-700">
            ⚠️ Alcuni orari non sono validi. L'orario di inizio deve essere precedente a quello di fine.
          </p>
        </div>
      )}

      <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h5 className="font-medium text-blue-900 mb-2">💡 Suggerimenti:</h5>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• Puoi impostare più fasce orarie per lo stesso giorno</li>
          <li>• Gli esercenti vedranno solo i tuoi orari di disponibilità</li>
          <li>• Puoi modificare gli orari in qualsiasi momento</li>
        </ul>
      </div>
    </div>
  )
}

export default WorkScheduleComponent
