/**
 * Global Styles
 * Main CSS file with Tailwind imports and custom styles
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Google Fonts - Manrope */
@import url('https://fonts.googleapis.com/css2?family=Manrope:wght@200;300;400;500;600;700;800&display=swap');

/* Base styles */
@layer base {
  html {
    font-family: 'Manrope', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900 antialiased;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }
}

/* Component styles */
@layer components {
  /* Custom button styles */
  .btn-gradient {
    @apply bg-gradient-to-r from-bemyrider-600 to-primary-600 text-white font-medium px-6 py-3 rounded-lg shadow-lg hover:from-bemyrider-700 hover:to-primary-700 transform hover:scale-105 transition-all duration-200;
  }
  
  /* Card hover effects */
  .card-hover {
    @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
  }
  
  /* Glass morphism effect */
  .glass {
    @apply bg-white bg-opacity-80 backdrop-blur-sm border border-white border-opacity-20;
  }
  
  /* Gradient text */
  .text-gradient {
    @apply bg-gradient-to-r from-bemyrider-600 to-primary-600 bg-clip-text text-transparent;
  }

  /* bemyrider logo official styling */
  .bemyrider-logo {
    font-family: 'Manrope', sans-serif;
    font-weight: 700;
    letter-spacing: -0.02em;
    @apply text-bemyrider-600;
  }
  
  /* Loading spinner */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
  }
  
  /* Status badges */
  .status-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .status-success {
    @apply bg-green-100 text-green-800;
  }
  
  .status-warning {
    @apply bg-yellow-100 text-yellow-800;
  }
  
  .status-error {
    @apply bg-red-100 text-red-800;
  }
  
  .status-info {
    @apply bg-blue-100 text-blue-800;
  }
  
  /* Form styles */
  .form-group {
    @apply space-y-1;
  }
  
  .form-label {
    @apply block text-sm font-medium text-gray-700;
  }
  
  .form-input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm;
  }
  
  .form-error {
    @apply text-sm text-red-600;
  }
  
  /* Dashboard grid */
  .dashboard-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6;
  }
  
  /* Metric cards */
  .metric-card {
    @apply bg-white rounded-lg shadow-soft p-6 border border-gray-200;
  }
  
  .metric-value {
    @apply text-3xl font-bold text-gray-900;
  }
  
  .metric-label {
    @apply text-sm font-medium text-gray-600;
  }
  
  .metric-change {
    @apply text-sm font-medium;
  }
  
  .metric-change-positive {
    @apply text-green-600;
  }
  
  .metric-change-negative {
    @apply text-red-600;
  }
}

/* Utility styles */
@layer utilities {
  /* Animation delays */
  .delay-100 {
    animation-delay: 100ms;
  }
  
  .delay-200 {
    animation-delay: 200ms;
  }
  
  .delay-300 {
    animation-delay: 300ms;
  }
  
  /* Custom shadows */
  .shadow-soft {
    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
  }
  
  .shadow-medium {
    box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }
  
  .shadow-large {
    box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.15), 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  }
  
  /* Responsive text */
  .text-responsive {
    @apply text-sm sm:text-base lg:text-lg;
  }
  
  /* Truncate text */
  .truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

/* Custom animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Animation classes */
.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fadeInDown {
  animation: fadeInDown 0.6s ease-out;
}

.animate-slideInLeft {
  animation: slideInLeft 0.6s ease-out;
}

.animate-slideInRight {
  animation: slideInRight 0.6s ease-out;
}

.animate-pulse-slow {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
  /* Dark mode styles will be added here in future updates */
}
