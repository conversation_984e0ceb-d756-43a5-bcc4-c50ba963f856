/**
 * BeMyRider Local Development Starter
 * Starts both frontend and backend servers
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { spawn } from 'child_process'
import { fileURLToPath } from 'url'
import { dirname, join } from 'path'
import fs from 'fs'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function checkEnvironment() {
  log('\n🔍 Checking environment setup...', 'cyan')
  
  // Check if .env file exists
  const envPath = join(__dirname, '.env')
  if (!fs.existsSync(envPath)) {
    log('❌ .env file not found!', 'red')
    log('📝 Please copy .env.example to .env and configure your keys:', 'yellow')
    log('   cp .env.example .env', 'yellow')
    log('\n📋 Required environment variables:', 'yellow')
    log('   - VITE_CLERK_PUBLISHABLE_KEY', 'yellow')
    log('   - CLERK_SECRET_KEY', 'yellow')
    log('   - VITE_SUPABASE_URL', 'yellow')
    log('   - VITE_SUPABASE_ANON_KEY', 'yellow')
    log('   - VITE_STRIPE_PUBLISHABLE_KEY', 'yellow')
    log('   - STRIPE_SECRET_KEY', 'yellow')
    process.exit(1)
  }
  
  // Check if node_modules exists
  const nodeModulesPath = join(__dirname, 'node_modules')
  if (!fs.existsSync(nodeModulesPath)) {
    log('❌ Dependencies not installed!', 'red')
    log('📦 Please run: npm install', 'yellow')
    process.exit(1)
  }
  
  // Check frontend node_modules
  const frontendNodeModulesPath = join(__dirname, 'frontend', 'node_modules')
  if (!fs.existsSync(frontendNodeModulesPath)) {
    log('❌ Frontend dependencies not installed!', 'red')
    log('📦 Please run: cd frontend && npm install', 'yellow')
    process.exit(1)
  }
  
  log('✅ Environment setup looks good!', 'green')
}

function startServer(name, command, args, color, cwd = __dirname) {
  log(`\n🚀 Starting ${name}...`, color)
  
  const process = spawn(command, args, {
    cwd,
    stdio: 'pipe',
    shell: true
  })
  
  process.stdout.on('data', (data) => {
    const output = data.toString().trim()
    if (output) {
      log(`[${name}] ${output}`, color)
    }
  })
  
  process.stderr.on('data', (data) => {
    const output = data.toString().trim()
    if (output && !output.includes('ExperimentalWarning')) {
      log(`[${name}] ${output}`, 'red')
    }
  })
  
  process.on('close', (code) => {
    if (code !== 0) {
      log(`❌ ${name} exited with code ${code}`, 'red')
    } else {
      log(`✅ ${name} stopped gracefully`, 'green')
    }
  })
  
  return process
}

function displayStartupInfo() {
  log('\n' + '='.repeat(60), 'cyan')
  log('🎉 BEMYRIDER LOCAL DEVELOPMENT ENVIRONMENT', 'bright')
  log('='.repeat(60), 'cyan')
  log('\n📍 Application URLs:', 'bright')
  log('   🎨 Frontend:  http://localhost:3000', 'green')
  log('   🖥️  Backend:   http://localhost:3001', 'blue')
  log('   🏥 Health:    http://localhost:3001/api/health', 'blue')
  log('\n🔧 Development Tools:', 'bright')
  log('   📊 React DevTools: Available in browser', 'yellow')
  log('   🔍 Network Tab: Monitor API calls', 'yellow')
  log('   🐛 Console: Check for errors/logs', 'yellow')
  log('\n📋 Test Accounts:', 'bright')
  log('   👤 Demo User: <EMAIL>', 'magenta')
  log('   🔑 Auth: Use Clerk sign-in flow', 'magenta')
  log('\n⚡ Quick Actions:', 'bright')
  log('   • Press Ctrl+C to stop all servers', 'yellow')
  log('   • Check browser console for errors', 'yellow')
  log('   • Monitor terminal for API logs', 'yellow')
  log('\n' + '='.repeat(60), 'cyan')
}

async function main() {
  try {
    log('🚀 BeMyRider Local Development Starter', 'bright')
    
    // Check environment
    checkEnvironment()
    
    // Start backend server
    const backendProcess = startServer(
      'Backend',
      'node',
      ['server/index.js'],
      'blue'
    )
    
    // Wait a bit for backend to start
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Start frontend server
    const frontendProcess = startServer(
      'Frontend',
      'npm',
      ['run', 'dev'],
      'green',
      join(__dirname, 'frontend')
    )
    
    // Wait a bit for frontend to start
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    // Display startup info
    displayStartupInfo()
    
    // Handle graceful shutdown
    process.on('SIGINT', () => {
      log('\n🛑 Shutting down servers...', 'yellow')
      backendProcess.kill('SIGINT')
      frontendProcess.kill('SIGINT')
      setTimeout(() => {
        log('👋 Goodbye!', 'green')
        process.exit(0)
      }, 1000)
    })
    
    // Keep the process alive
    process.stdin.resume()
    
  } catch (error) {
    log(`❌ Error starting development environment: ${error.message}`, 'red')
    process.exit(1)
  }
}

// Run the starter
main().catch(error => {
  log(`❌ Fatal error: ${error.message}`, 'red')
  process.exit(1)
})
