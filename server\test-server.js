import express from 'express'
import cors from 'cors'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config()

const app = express()
const PORT = process.env.PORT || 5000

// Middleware
app.use(cors())
app.use(express.json())
app.use(express.urlencoded({ extended: true }))

// Mock database for testing
const mockUsers = []
const mockBookings = []

// Health check
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'BeMyRider API is running' })
})

// Users API - Mock implementation
app.post('/api/users/sync', (req, res) => {
  try {
    const userData = req.body
    
    // Check if user exists
    const existingUserIndex = mockUsers.findIndex(u => u.clerkId === userData.clerkId)
    
    if (existingUserIndex >= 0) {
      // Update existing user
      mockUsers[existingUserIndex] = { ...mockUsers[existingUserIndex], ...userData }
      res.json({
        success: true,
        user: mockUsers[existingUserIndex],
        message: 'Utente aggiornato con successo'
      })
    } else {
      // Create new user
      const newUser = {
        id: `user_${Date.now()}`,
        ...userData,
        createdAt: new Date(),
        updatedAt: new Date()
      }
      mockUsers.push(newUser)
      res.json({
        success: true,
        user: newUser,
        message: 'Utente creato con successo'
      })
    }
  } catch (error) {
    console.error('Error syncing user:', error)
    res.status(500).json({
      success: false,
      message: 'Errore nella sincronizzazione utente'
    })
  }
})

app.get('/api/users/profile/:clerkId', (req, res) => {
  try {
    const { clerkId } = req.params
    const user = mockUsers.find(u => u.clerkId === clerkId)
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Utente non trovato'
      })
    }
    
    res.json({
      success: true,
      user
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Errore nel recupero del profilo utente'
    })
  }
})

app.get('/api/users/riders', (req, res) => {
  try {
    const riders = mockUsers.filter(u => u.userType === 'RIDER')
    
    res.json({
      success: true,
      riders: riders.map(rider => ({
        ...rider,
        averageRating: 4.5,
        totalReviews: 10,
        totalBookings: 25
      })),
      pagination: {
        page: 1,
        limit: 10,
        total: riders.length,
        pages: 1
      }
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Errore nel recupero dei rider'
    })
  }
})

// Bookings API - Mock implementation
app.post('/api/bookings', (req, res) => {
  try {
    const bookingData = req.body
    
    const newBooking = {
      id: `booking_${Date.now()}`,
      ...bookingData,
      status: 'PENDING',
      createdAt: new Date(),
      updatedAt: new Date()
    }
    
    mockBookings.push(newBooking)
    
    res.json({
      success: true,
      message: 'Prenotazione creata con successo',
      booking: newBooking
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Errore nella creazione della prenotazione'
    })
  }
})

app.get('/api/bookings', (req, res) => {
  try {
    const { clerkId } = req.query
    
    // Mock: return all bookings for testing
    res.json({
      success: true,
      bookings: mockBookings,
      pagination: {
        page: 1,
        limit: 10,
        total: mockBookings.length,
        pages: 1
      }
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Errore nel recupero delle prenotazioni'
    })
  }
})

// Dashboard API - Mock implementation
app.get('/api/dashboard/stats', (req, res) => {
  try {
    const { clerkId } = req.query
    const user = mockUsers.find(u => u.clerkId === clerkId)
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Utente non trovato'
      })
    }
    
    const mockStats = user.userType === 'RIDER' ? {
      totalBookings: 25,
      activeBookings: 3,
      totalEarnings: 1250.50,
      averageRating: 4.7,
      totalReviews: 18
    } : {
      totalBookings: 12,
      activeBookings: 2,
      totalSpent: 680.00,
      completedBookings: 10
    }
    
    res.json({
      success: true,
      stats: mockStats
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Errore nel recupero delle statistiche'
    })
  }
})

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack)
  res.status(500).json({ 
    success: false, 
    message: 'Qualcosa è andato storto!',
    error: process.env.NODE_ENV === 'development' ? err.message : undefined
  })
})

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ 
    success: false, 
    message: 'Endpoint non trovato' 
  })
})

app.listen(PORT, () => {
  console.log(`🚀 Test Server running on port ${PORT}`)
  console.log(`📱 BeMyRider Test API ready at http://localhost:${PORT}`)
  console.log(`📊 Mock data: ${mockUsers.length} users, ${mockBookings.length} bookings`)
})
