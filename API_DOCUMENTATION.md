# BeMyRider API Documentation

## Base URL
```
http://localhost:5000/api
```

## Authentication
Tutte le route protette richiedono un token JWT nell'header Authorization:
```
Authorization: Bearer <token>
```

## Endpoints

### Authentication

#### POST /auth/register
Registra un nuovo utente.

**Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "name": "Nome Utente",
  "type": "ESERCENTE" | "RIDER"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Registrazione completata con successo",
  "token": "jwt_token",
  "user": {
    "id": "user_id",
    "email": "<EMAIL>",
    "name": "Nome Utente",
    "type": "ESERCENTE",
    "profileComplete": false
  }
}
```

#### POST /auth/login
Effettua il login.

**Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Login effettuato con successo",
  "token": "jwt_token",
  "user": {
    "id": "user_id",
    "email": "<EMAIL>",
    "name": "Nome Utente",
    "type": "ESERCENTE"
  }
}
```

#### GET /auth/me
Ottiene i dati dell'utente corrente.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "success": true,
  "user": {
    "id": "user_id",
    "email": "<EMAIL>",
    "name": "Nome Utente",
    "type": "RIDER",
    "hourlyRate": 12.0,
    "vehicleType": "MOTO"
  }
}
```

### Riders

#### GET /riders
Ottiene la lista dei rider disponibili.

**Headers:** `Authorization: Bearer <token>`

**Query Parameters:**
- `vehicleType`: Filtra per tipo di veicolo (BICI, E_BIKE, SCOOTER, MOTO, AUTO, FURGONE)
- `search`: Ricerca per nome, città o descrizione
- `city`: Filtra per città
- `minRate`: Tariffa minima
- `maxRate`: Tariffa massima

**Response:**
```json
{
  "success": true,
  "riders": [
    {
      "id": "rider_id",
      "name": "Paolo Cimino",
      "location": "Catania CT, Italia",
      "hourlyRate": 12.0,
      "vehicleType": "MOTO",
      "rating": 5.0,
      "isAvailable": true
    }
  ],
  "total": 1
}
```

#### GET /riders/:id
Ottiene i dettagli di un rider specifico.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "success": true,
  "rider": {
    "id": "rider_id",
    "name": "Paolo Cimino",
    "location": "Catania CT, Italia",
    "hourlyRate": 12.0,
    "vehicleType": "MOTO",
    "vehicleModel": "TRK 502X",
    "description": "Rider esperto...",
    "rating": 5.0,
    "availability": {
      "days": ["Lunedì", "Martedì", ...],
      "hours": "10:00 - 23:00"
    },
    "reviews": [...]
  }
}
```

### Bookings

#### POST /bookings
Crea una nuova prenotazione.

**Headers:** `Authorization: Bearer <token>`

**Body:**
```json
{
  "riderId": "rider_id",
  "date": "2025-06-15T14:00:00Z",
  "duration": 2,
  "address": "Via Roma 123, Milano",
  "instructions": "Note aggiuntive",
  "totalAmount": 27.60
}
```

**Response:**
```json
{
  "success": true,
  "message": "Prenotazione creata con successo",
  "booking": {
    "id": "booking_id",
    "riderId": "rider_id",
    "clientId": "client_id",
    "date": "2025-06-15T14:00:00Z",
    "duration": 2,
    "status": "PENDING",
    "totalAmount": 27.60
  }
}
```

#### GET /bookings
Ottiene le prenotazioni dell'utente.

**Headers:** `Authorization: Bearer <token>`

**Query Parameters:**
- `status`: Filtra per status (PENDING, CONFIRMED, IN_PROGRESS, COMPLETED, CANCELLED)
- `page`: Numero di pagina (default: 1)
- `limit`: Elementi per pagina (default: 10)

**Response:**
```json
{
  "success": true,
  "bookings": [...],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "pages": 3
  }
}
```

#### GET /bookings/:id
Ottiene i dettagli di una prenotazione.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "success": true,
  "booking": {
    "id": "booking_id",
    "date": "2025-06-15T14:00:00Z",
    "duration": 2,
    "status": "CONFIRMED",
    "rider": {...},
    "client": {...},
    "payment": {...}
  }
}
```

### Payments

#### POST /payments/create-payment-intent
Crea un payment intent per una prenotazione.

**Headers:** `Authorization: Bearer <token>`

**Body:**
```json
{
  "bookingId": "booking_id"
}
```

**Response:**
```json
{
  "success": true,
  "clientSecret": "pi_xxx_secret_xxx",
  "paymentIntentId": "pi_xxx"
}
```

#### POST /payments/confirm-payment
Conferma un pagamento completato.

**Headers:** `Authorization: Bearer <token>`

**Body:**
```json
{
  "paymentIntentId": "pi_xxx"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Pagamento confermato con successo",
  "payment": {...}
}
```

### Dashboard

#### GET /dashboard/stats
Ottiene le statistiche della dashboard.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "success": true,
  "stats": {
    "totalBookings": 12,
    "activeBookings": 3,
    "totalEarnings": 450.0,
    "averageRating": 4.8
  }
}
```

#### GET /dashboard/recent-bookings
Ottiene le prenotazioni recenti per la dashboard.

**Headers:** `Authorization: Bearer <token>`

**Query Parameters:**
- `limit`: Numero di prenotazioni da restituire (default: 5)

**Response:**
```json
{
  "success": true,
  "bookings": [
    {
      "id": "booking_id",
      "riderName": "Paolo Cimino",
      "date": "2025-06-15T14:00:00Z",
      "duration": 2,
      "status": "CONFIRMED",
      "amount": 27.60
    }
  ]
}
```

## Error Responses

Tutti gli endpoint possono restituire i seguenti errori:

### 400 Bad Request
```json
{
  "success": false,
  "message": "Messaggio di errore specifico"
}
```

### 401 Unauthorized
```json
{
  "success": false,
  "message": "Token di accesso richiesto"
}
```

### 403 Forbidden
```json
{
  "success": false,
  "message": "Accesso non autorizzato"
}
```

### 404 Not Found
```json
{
  "success": false,
  "message": "Risorsa non trovata"
}
```

### 500 Internal Server Error
```json
{
  "success": false,
  "message": "Errore interno del server"
}
```

## Status Codes

### Booking Status
- `PENDING`: Prenotazione in attesa di conferma
- `CONFIRMED`: Prenotazione confermata e pagata
- `IN_PROGRESS`: Servizio in corso
- `COMPLETED`: Servizio completato
- `CANCELLED`: Prenotazione cancellata

### Payment Status
- `PENDING`: Pagamento in attesa
- `PROCESSING`: Pagamento in elaborazione
- `COMPLETED`: Pagamento completato
- `FAILED`: Pagamento fallito
- `REFUNDED`: Pagamento rimborsato

### Vehicle Types
- `BICI`: Bicicletta
- `E_BIKE`: Bicicletta elettrica
- `SCOOTER`: Scooter
- `MOTO`: Motocicletta
- `AUTO`: Automobile
- `FURGONE`: Furgone
