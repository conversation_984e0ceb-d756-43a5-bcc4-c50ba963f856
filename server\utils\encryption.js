/**
 * Data Encryption Utilities
 * Enterprise-grade encryption for sensitive data
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import crypto from 'crypto'
import bcrypt from 'bcrypt'
import { getEnv } from './env-validator.js'

// ============================================================================
// CONFIGURATION
// ============================================================================

const ENCRYPTION_CONFIG = {
  algorithm: 'aes-256-gcm',
  keyLength: 32,
  ivLength: 16,
  tagLength: 16,
  saltRounds: 12,
  keyDerivationIterations: 100000
}

// Get encryption key from environment
const getEncryptionKey = () => {
  const key = getEnv('ENCRYPTION_KEY')
  if (!key) {
    throw new Error('ENCRYPTION_KEY environment variable is required')
  }
  
  // Derive a proper key from the environment variable
  return crypto.pbkdf2Sync(key, 'bemyrider-salt', ENCRYPTION_CONFIG.keyDerivationIterations, ENCRYPTION_CONFIG.keyLength, 'sha256')
}

// ============================================================================
// SYMMETRIC ENCRYPTION (for data at rest)
// ============================================================================

/**
 * Encrypt sensitive data
 * @param {string} plaintext - Data to encrypt
 * @returns {string} Encrypted data with IV and tag
 */
export const encrypt = (plaintext) => {
  try {
    if (!plaintext || typeof plaintext !== 'string') {
      throw new Error('Invalid plaintext for encryption')
    }

    const key = getEncryptionKey()
    const iv = crypto.randomBytes(ENCRYPTION_CONFIG.ivLength)
    const cipher = crypto.createCipher(ENCRYPTION_CONFIG.algorithm, key)
    
    cipher.setAAD(Buffer.from('bemyrider-aad'))
    
    let encrypted = cipher.update(plaintext, 'utf8', 'hex')
    encrypted += cipher.final('hex')
    
    const tag = cipher.getAuthTag()
    
    // Combine IV, tag, and encrypted data
    const result = iv.toString('hex') + ':' + tag.toString('hex') + ':' + encrypted
    
    return result
  } catch (error) {
    console.error('Encryption error:', error)
    throw new Error('Failed to encrypt data')
  }
}

/**
 * Decrypt sensitive data
 * @param {string} encryptedData - Encrypted data with IV and tag
 * @returns {string} Decrypted plaintext
 */
export const decrypt = (encryptedData) => {
  try {
    if (!encryptedData || typeof encryptedData !== 'string') {
      throw new Error('Invalid encrypted data for decryption')
    }

    const parts = encryptedData.split(':')
    if (parts.length !== 3) {
      throw new Error('Invalid encrypted data format')
    }

    const key = getEncryptionKey()
    const iv = Buffer.from(parts[0], 'hex')
    const tag = Buffer.from(parts[1], 'hex')
    const encrypted = parts[2]
    
    const decipher = crypto.createDecipher(ENCRYPTION_CONFIG.algorithm, key)
    decipher.setAuthTag(tag)
    decipher.setAAD(Buffer.from('bemyrider-aad'))
    
    let decrypted = decipher.update(encrypted, 'hex', 'utf8')
    decrypted += decipher.final('utf8')
    
    return decrypted
  } catch (error) {
    console.error('Decryption error:', error)
    throw new Error('Failed to decrypt data')
  }
}

// ============================================================================
// PASSWORD HASHING
// ============================================================================

/**
 * Hash password with bcrypt
 * @param {string} password - Plain password
 * @returns {Promise<string>} Hashed password
 */
export const hashPassword = async (password) => {
  try {
    if (!password || typeof password !== 'string') {
      throw new Error('Invalid password for hashing')
    }

    if (password.length < 8 || password.length > 128) {
      throw new Error('Password must be between 8 and 128 characters')
    }

    return await bcrypt.hash(password, ENCRYPTION_CONFIG.saltRounds)
  } catch (error) {
    console.error('Password hashing error:', error)
    throw new Error('Failed to hash password')
  }
}

/**
 * Verify password against hash
 * @param {string} password - Plain password
 * @param {string} hash - Hashed password
 * @returns {Promise<boolean>} True if password matches
 */
export const verifyPassword = async (password, hash) => {
  try {
    if (!password || !hash) {
      return false
    }

    return await bcrypt.compare(password, hash)
  } catch (error) {
    console.error('Password verification error:', error)
    return false
  }
}

// ============================================================================
// TOKEN GENERATION
// ============================================================================

/**
 * Generate secure random token
 * @param {number} length - Token length in bytes
 * @returns {string} Hex encoded token
 */
export const generateSecureToken = (length = 32) => {
  return crypto.randomBytes(length).toString('hex')
}

/**
 * Generate API key
 * @param {string} prefix - Key prefix (e.g., 'bmr_')
 * @returns {string} API key
 */
export const generateAPIKey = (prefix = 'bmr_') => {
  const timestamp = Date.now().toString(36)
  const random = crypto.randomBytes(16).toString('hex')
  return `${prefix}${timestamp}_${random}`
}

/**
 * Generate session token
 * @returns {string} Session token
 */
export const generateSessionToken = () => {
  return generateSecureToken(48) // 96 character hex string
}

// ============================================================================
// DATA MASKING
// ============================================================================

/**
 * Mask email address
 * @param {string} email - Email to mask
 * @returns {string} Masked email
 */
export const maskEmail = (email) => {
  if (!email || !email.includes('@')) {
    return '***'
  }

  const [local, domain] = email.split('@')
  const maskedLocal = local.length > 2 
    ? local[0] + '*'.repeat(local.length - 2) + local[local.length - 1]
    : '*'.repeat(local.length)
  
  return `${maskedLocal}@${domain}`
}

/**
 * Mask phone number
 * @param {string} phone - Phone to mask
 * @returns {string} Masked phone
 */
export const maskPhone = (phone) => {
  if (!phone) {
    return '***'
  }

  const cleaned = phone.replace(/\D/g, '')
  if (cleaned.length < 4) {
    return '*'.repeat(cleaned.length)
  }

  return '*'.repeat(cleaned.length - 4) + cleaned.slice(-4)
}

/**
 * Mask credit card number
 * @param {string} cardNumber - Card number to mask
 * @returns {string} Masked card number
 */
export const maskCardNumber = (cardNumber) => {
  if (!cardNumber) {
    return '****'
  }

  const cleaned = cardNumber.replace(/\D/g, '')
  if (cleaned.length < 4) {
    return '*'.repeat(cleaned.length)
  }

  return '*'.repeat(cleaned.length - 4) + cleaned.slice(-4)
}

// ============================================================================
// PII ENCRYPTION HELPERS
// ============================================================================

/**
 * Encrypt personally identifiable information
 * @param {Object} data - Data object containing PII
 * @param {Array} piiFields - Fields to encrypt
 * @returns {Object} Data with encrypted PII fields
 */
export const encryptPII = (data, piiFields = []) => {
  const encrypted = { ...data }
  
  for (const field of piiFields) {
    if (encrypted[field] && typeof encrypted[field] === 'string') {
      encrypted[field] = encrypt(encrypted[field])
    }
  }
  
  return encrypted
}

/**
 * Decrypt personally identifiable information
 * @param {Object} data - Data object with encrypted PII
 * @param {Array} piiFields - Fields to decrypt
 * @returns {Object} Data with decrypted PII fields
 */
export const decryptPII = (data, piiFields = []) => {
  const decrypted = { ...data }
  
  for (const field of piiFields) {
    if (decrypted[field] && typeof decrypted[field] === 'string') {
      try {
        decrypted[field] = decrypt(decrypted[field])
      } catch (error) {
        console.error(`Failed to decrypt field ${field}:`, error)
        // Keep encrypted value if decryption fails
      }
    }
  }
  
  return decrypted
}

// ============================================================================
// SECURE COMPARISON
// ============================================================================

/**
 * Constant-time string comparison to prevent timing attacks
 * @param {string} a - First string
 * @param {string} b - Second string
 * @returns {boolean} True if strings are equal
 */
export const secureCompare = (a, b) => {
  if (typeof a !== 'string' || typeof b !== 'string') {
    return false
  }

  if (a.length !== b.length) {
    return false
  }

  return crypto.timingSafeEqual(Buffer.from(a), Buffer.from(b))
}

// ============================================================================
// HASH FUNCTIONS
// ============================================================================

/**
 * Generate SHA-256 hash
 * @param {string} data - Data to hash
 * @returns {string} Hex encoded hash
 */
export const sha256 = (data) => {
  return crypto.createHash('sha256').update(data).digest('hex')
}

/**
 * Generate HMAC signature
 * @param {string} data - Data to sign
 * @param {string} secret - Secret key
 * @returns {string} HMAC signature
 */
export const generateHMAC = (data, secret) => {
  return crypto.createHmac('sha256', secret).update(data).digest('hex')
}

/**
 * Verify HMAC signature
 * @param {string} data - Original data
 * @param {string} signature - HMAC signature to verify
 * @param {string} secret - Secret key
 * @returns {boolean} True if signature is valid
 */
export const verifyHMAC = (data, signature, secret) => {
  const expectedSignature = generateHMAC(data, secret)
  return secureCompare(signature, expectedSignature)
}

// ============================================================================
// EXPORTS
// ============================================================================

export default {
  encrypt,
  decrypt,
  hashPassword,
  verifyPassword,
  generateSecureToken,
  generateAPIKey,
  generateSessionToken,
  maskEmail,
  maskPhone,
  maskCardNumber,
  encryptPII,
  decryptPII,
  secureCompare,
  sha256,
  generateHMAC,
  verifyHMAC
}
