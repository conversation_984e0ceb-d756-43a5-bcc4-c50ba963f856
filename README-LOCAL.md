# 🚀 BeMyRider - Guida Avvio Locale

## 📋 Prerequisiti

### Software Richiesto
- **Node.js** v18+ ([Download](https://nodejs.org/))
- **npm** v9+ (incluso con Node.js)
- **Git** ([Download](https://git-scm.com/))

### Account <PERSON><PERSON><PERSON>
- **Clerk.com** - Autenticazione ([Registrati](https://clerk.com/))
- **Supabase** - Database ([Registrati](https://supabase.com/))
- **Stripe** - Pagamenti ([Registrati](https://stripe.com/))

---

## ⚡ Avvio Rapido (5 minuti)

### 1. 📥 Clona e Installa
```bash
# Clona il repository
git clone <repository-url>
cd bemyrider

# Installa dipendenze root
npm install

# Installa dipendenze frontend
cd frontend && npm install && cd ..
```

### 2. 🔧 Configura Ambiente
```bash
# Copia il file di esempio
cp .env.example .env

# Modifica .env con i tuoi valori
# (vedi sezione "Configurazione Chiavi" sotto)
```

### 3. 🚀 Avvia Tutto
```bash
# Avvia frontend + backend insieme
npm start

# OPPURE avvia separatamente:
npm run start:backend    # Solo backend (porta 3001)
npm run start:frontend   # Solo frontend (porta 3000)
```

### 4. 🌐 Apri Browser
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:3001/api/health

---

## 🔑 Configurazione Chiavi

### Clerk Authentication
1. Vai su [Clerk Dashboard](https://dashboard.clerk.com/)
2. Crea un nuovo progetto
3. Copia le chiavi in `.env`:
```env
VITE_CLERK_PUBLISHABLE_KEY=pk_test_...
CLERK_SECRET_KEY=sk_test_...
```

### Supabase Database
1. Vai su [Supabase Dashboard](https://supabase.com/dashboard/)
2. Crea un nuovo progetto
3. Vai in Settings > API
4. Copia le chiavi in `.env`:
```env
VITE_SUPABASE_URL=https://xxx.supabase.co
VITE_SUPABASE_ANON_KEY=eyJ...
```

### Stripe Payments
1. Vai su [Stripe Dashboard](https://dashboard.stripe.com/)
2. Attiva modalità test
3. Vai in Developers > API Keys
4. Copia le chiavi in `.env`:
```env
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
```

---

## 🧪 Test dell'Applicazione

### 1. 🔐 Test Autenticazione
- Vai su http://localhost:3000
- Clicca "Sign Up" o "Sign In"
- Usa Google/Apple login (configurato in Clerk)

### 2. 👤 Test Onboarding
- Dopo login, scegli "Sono un Rider" o "Sono un Esercente"
- Completa il flow di onboarding

### 3. 📊 Test Dashboard
- **Rider**: Gestisci disponibilità, visualizza guadagni
- **Merchant**: Trova rider, gestisci flotta

### 4. 📱 Test Mobile
- Apri DevTools (F12)
- Attiva modalità mobile
- Testa navigazione bottom nav

---

## 🐛 Risoluzione Problemi

### ❌ Errore "Module not found"
```bash
# Reinstalla dipendenze
rm -rf node_modules frontend/node_modules
npm install
cd frontend && npm install
```

### ❌ Errore "Port already in use"
```bash
# Cambia porta in .env
PORT=3002  # Per backend
# Frontend usa automaticamente porta libera
```

### ❌ Errore Clerk "Invalid publishable key"
- Verifica che la chiave inizi con `pk_test_`
- Controlla che non ci siano spazi extra
- Riavvia il server dopo modifiche a `.env`

### ❌ Errore Supabase "Invalid URL"
- Verifica formato: `https://xxx.supabase.co`
- Controlla che il progetto sia attivo
- Verifica le chiavi API

### ❌ Errore CORS
- Verifica che `FRONTEND_URL=http://localhost:3000` in `.env`
- Riavvia il backend dopo modifiche

---

## 📁 Struttura Progetto

```
bemyrider/
├── frontend/           # React + Vite frontend
│   ├── src/
│   │   ├── components/ # Componenti riutilizzabili
│   │   ├── pages/      # Pagine dell'app
│   │   └── ...
├── server/             # Node.js backend
│   ├── routes/         # API endpoints
│   ├── services/       # Business logic
│   └── ...
├── .env               # Variabili ambiente (da creare)
├── .env.example       # Template variabili
└── start-local.js     # Script avvio locale
```

---

## 🔧 Comandi Utili

```bash
# Sviluppo
npm start                 # Avvia tutto
npm run start:frontend   # Solo frontend
npm run start:backend    # Solo backend

# Testing
npm test                 # Esegui test
npm run test:watch       # Test in modalità watch

# Build
npm run build            # Build per produzione
npm run preview          # Preview build locale

# Database
npm run db:setup         # Setup database
npm run db:seed          # Popola dati test
npm run db:reset         # Reset database
```

---

## 📞 Supporto

### 🆘 Problemi Comuni
- Controlla sempre la console browser (F12)
- Verifica i log del terminal
- Assicurati che tutte le chiavi siano configurate

### 📚 Documentazione
- [Clerk Docs](https://clerk.com/docs)
- [Supabase Docs](https://supabase.com/docs)
- [Stripe Docs](https://stripe.com/docs)
- [React Docs](https://react.dev/)

### 🎯 Prossimi Passi
1. ✅ Testa tutte le funzionalità in locale
2. 🔧 Personalizza configurazioni
3. 🚀 Prepara per deployment
4. 📈 Aggiungi funzionalità custom

---

**🎉 Buon sviluppo con BeMyRider!**
