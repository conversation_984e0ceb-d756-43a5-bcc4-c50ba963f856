/**
 * Rider Onboarding Integration Tests
 * Complete onboarding workflow testing
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest'
import request from 'supertest'
import { createTestApp } from '../helpers/test-app.js'

describe('Rider Onboarding Integration', () => {
  let app
  let riderAuthHeaders
  let onboardingSession

  beforeAll(async () => {
    app = await createTestApp()
    
    // Setup auth headers for rider
    riderAuthHeaders = global.createAuthHeaders('test_rider_onboarding')

    // Setup test rider user
    global.testDb.addUser({
      id: 'test_rider_onboarding',
      clerk_id: 'test_rider_onboarding_clerk',
      email: '<EMAIL>',
      type: 'RIDER',
      first_name: '<PERSON>',
      last_name: '<PERSON>',
      profile_complete: false
    })
  })

  afterAll(async () => {
    if (global.testDb) {
      global.testDb.clear()
    }
  })

  beforeEach(() => {
    // Clear onboarding sessions before each test
    if (global.testDb) {
      global.testDb.onboardingSessions?.clear()
    }
  })

  describe('🚀 Complete Onboarding Workflow', () => {
    it('should complete full rider onboarding process', async () => {
      // Step 1: Start onboarding
      const startData = {
        firstName: '<PERSON>',
        lastName: 'Rossi',
        email: '<EMAIL>',
        phone: '+39 333 1234567'
      }

      const startResponse = await request(app)
        .post('/api/rider-onboarding/start')
        .set(riderAuthHeaders)
        .send(startData)
        .expect(201)

      expect(startResponse.body.success).toBe(true)
      expect(startResponse.body.session).toHaveProperty('id')
      expect(startResponse.body.session.current_step).toBe('personal_info')

      onboardingSession = startResponse.body.session

      // Step 2: Complete personal information
      const personalData = {
        sessionId: onboardingSession.id,
        firstName: 'Mario',
        lastName: 'Rossi',
        email: '<EMAIL>',
        phone: '+39 333 1234567',
        dateOfBirth: '1990-01-15',
        fiscalCode: '****************',
        address: 'Via Roma 123, Milano, 20100'
      }

      const personalResponse = await request(app)
        .post('/api/rider-onboarding/personal-info')
        .set(riderAuthHeaders)
        .send(personalData)
        .expect(200)

      expect(personalResponse.body.success).toBe(true)

      // Step 3: Upload documents (mock file upload)
      const documentsResponse = await request(app)
        .post('/api/rider-onboarding/documents')
        .set(riderAuthHeaders)
        .field('sessionId', onboardingSession.id)
        .attach('identity_card', Buffer.from('mock-id-card'), 'id-card.jpg')
        .attach('driving_license', Buffer.from('mock-license'), 'license.jpg')
        .attach('vehicle_registration', Buffer.from('mock-registration'), 'registration.pdf')
        .attach('insurance_certificate', Buffer.from('mock-insurance'), 'insurance.pdf')
        .expect(200)

      expect(documentsResponse.body.success).toBe(true)
      expect(documentsResponse.body.results).toHaveProperty('IDENTITY_CARD')
      expect(documentsResponse.body.results).toHaveProperty('DRIVING_LICENSE')

      // Step 4: Complete vehicle information
      const vehicleData = {
        sessionId: onboardingSession.id,
        vehicleType: 'MOTO',
        make: 'Honda',
        model: 'SH 150',
        year: '2020',
        licensePlate: 'AB123CD',
        hourlyRate: 12.00
      }

      const vehicleResponse = await request(app)
        .post('/api/rider-onboarding/vehicle-info')
        .set(riderAuthHeaders)
        .send(vehicleData)
        .expect(200)

      expect(vehicleResponse.body.success).toBe(true)

      // Step 5: Setup availability
      const availabilityData = {
        sessionId: onboardingSession.id,
        weeklySchedule: {
          monday: { available: true, hours: [{ start: '09:00', end: '18:00' }] },
          tuesday: { available: true, hours: [{ start: '09:00', end: '18:00' }] },
          wednesday: { available: true, hours: [{ start: '09:00', end: '18:00' }] },
          thursday: { available: true, hours: [{ start: '09:00', end: '18:00' }] },
          friday: { available: true, hours: [{ start: '09:00', end: '18:00' }] },
          saturday: { available: false, hours: [] },
          sunday: { available: false, hours: [] }
        },
        maxShiftsPerDay: 4,
        preferredAreas: ['Milano Centro', 'Porta Garibaldi']
      }

      const availabilityResponse = await request(app)
        .post('/api/rider-onboarding/availability')
        .set(riderAuthHeaders)
        .send(availabilityData)
        .expect(200)

      expect(availabilityResponse.body.success).toBe(true)

      // Step 6: Setup Stripe Connect
      const stripeResponse = await request(app)
        .post('/api/rider-onboarding/stripe-connect')
        .set(riderAuthHeaders)
        .send({ sessionId: onboardingSession.id })
        .expect(200)

      expect(stripeResponse.body.success).toBe(true)
      expect(stripeResponse.body.stripe).toHaveProperty('accountId')
      expect(stripeResponse.body.stripe).toHaveProperty('onboardingUrl')

      // Step 7: Complete Stripe Connect (simulate completion)
      const stripeCompleteResponse = await request(app)
        .post('/api/rider-onboarding/stripe-connect/complete')
        .set(riderAuthHeaders)
        .send({ 
          sessionId: onboardingSession.id,
          accountId: stripeResponse.body.stripe.accountId
        })
        .expect(200)

      expect(stripeCompleteResponse.body.success).toBe(true)

      // Step 8: Check final status
      const statusResponse = await request(app)
        .get(`/api/rider-onboarding/status/${onboardingSession.id}`)
        .set(riderAuthHeaders)
        .expect(200)

      expect(statusResponse.body.success).toBe(true)
      expect(statusResponse.body.status.overallProgress).toBeGreaterThan(80)
    })
  })

  describe('📋 Individual Onboarding Steps', () => {
    beforeEach(async () => {
      // Create onboarding session for each test
      const startResponse = await request(app)
        .post('/api/rider-onboarding/start')
        .set(riderAuthHeaders)
        .send({
          firstName: 'Mario',
          lastName: 'Rossi',
          email: '<EMAIL>',
          phone: '+39 333 1234567'
        })

      onboardingSession = startResponse.body.session
    })

    it('should validate personal information correctly', async () => {
      // Test with missing required fields
      const incompleteData = {
        sessionId: onboardingSession.id,
        firstName: 'Mario'
        // Missing other required fields
      }

      await request(app)
        .post('/api/rider-onboarding/personal-info')
        .set(riderAuthHeaders)
        .send(incompleteData)
        .expect(400)

      // Test with complete data
      const completeData = {
        sessionId: onboardingSession.id,
        firstName: 'Mario',
        lastName: 'Rossi',
        email: '<EMAIL>',
        phone: '+39 333 1234567',
        dateOfBirth: '1990-01-15',
        fiscalCode: '****************',
        address: 'Via Roma 123, Milano, 20100'
      }

      const response = await request(app)
        .post('/api/rider-onboarding/personal-info')
        .set(riderAuthHeaders)
        .send(completeData)
        .expect(200)

      expect(response.body.success).toBe(true)
    })

    it('should validate vehicle information correctly', async () => {
      // Test with invalid hourly rate
      const invalidRateData = {
        sessionId: onboardingSession.id,
        vehicleType: 'MOTO',
        make: 'Honda',
        model: 'SH 150',
        year: '2020',
        licensePlate: 'AB123CD',
        hourlyRate: 15.00 // Above maximum €12.50
      }

      await request(app)
        .post('/api/rider-onboarding/vehicle-info')
        .set(riderAuthHeaders)
        .send(invalidRateData)
        .expect(400)

      // Test with valid data
      const validData = {
        sessionId: onboardingSession.id,
        vehicleType: 'MOTO',
        make: 'Honda',
        model: 'SH 150',
        year: '2020',
        licensePlate: 'AB123CD',
        hourlyRate: 12.00
      }

      const response = await request(app)
        .post('/api/rider-onboarding/vehicle-info')
        .set(riderAuthHeaders)
        .send(validData)
        .expect(200)

      expect(response.body.success).toBe(true)
    })

    it('should handle document uploads correctly', async () => {
      // Test with no files
      await request(app)
        .post('/api/rider-onboarding/documents')
        .set(riderAuthHeaders)
        .field('sessionId', onboardingSession.id)
        .expect(400)

      // Test with valid files
      const response = await request(app)
        .post('/api/rider-onboarding/documents')
        .set(riderAuthHeaders)
        .field('sessionId', onboardingSession.id)
        .attach('identity_card', Buffer.from('mock-id-card'), 'id-card.jpg')
        .attach('driving_license', Buffer.from('mock-license'), 'license.jpg')
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.results).toHaveProperty('IDENTITY_CARD')
      expect(response.body.results).toHaveProperty('DRIVING_LICENSE')
    })
  })

  describe('📄 Document Management', () => {
    it('should upload single document correctly', async () => {
      const response = await request(app)
        .post('/api/rider-onboarding/documents/identity_card')
        .set(riderAuthHeaders)
        .attach('document', Buffer.from('mock-id-card'), 'id-card.jpg')
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.document).toHaveProperty('documentId')
      expect(response.body.document).toHaveProperty('status')
    })

    it('should get user documents', async () => {
      // First upload a document
      await request(app)
        .post('/api/rider-onboarding/documents/identity_card')
        .set(riderAuthHeaders)
        .attach('document', Buffer.from('mock-id-card'), 'id-card.jpg')

      // Then get documents
      const response = await request(app)
        .get('/api/rider-onboarding/documents')
        .set(riderAuthHeaders)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(Array.isArray(response.body.documents)).toBe(true)
    })

    it('should filter documents by type and status', async () => {
      const response = await request(app)
        .get('/api/rider-onboarding/documents')
        .set(riderAuthHeaders)
        .query({
          documentType: 'IDENTITY_CARD',
          status: 'pending_review'
        })
        .expect(200)

      expect(response.body.success).toBe(true)
    })
  })

  describe('📊 Onboarding Status', () => {
    it('should track onboarding progress correctly', async () => {
      // Start onboarding
      const startResponse = await request(app)
        .post('/api/rider-onboarding/start')
        .set(riderAuthHeaders)
        .send({
          firstName: 'Mario',
          lastName: 'Rossi',
          email: '<EMAIL>',
          phone: '+39 333 1234567'
        })

      const sessionId = startResponse.body.session.id

      // Check initial status
      const initialStatus = await request(app)
        .get(`/api/rider-onboarding/status/${sessionId}`)
        .set(riderAuthHeaders)
        .expect(200)

      expect(initialStatus.body.status.overallProgress).toBe(0)
      expect(initialStatus.body.status.currentStep).toBe('personal_info')
      expect(initialStatus.body.status.nextAction).toBe('complete_personal_info')

      // Complete personal info
      await request(app)
        .post('/api/rider-onboarding/personal-info')
        .set(riderAuthHeaders)
        .send({
          sessionId,
          firstName: 'Mario',
          lastName: 'Rossi',
          email: '<EMAIL>',
          phone: '+39 333 1234567',
          dateOfBirth: '1990-01-15',
          fiscalCode: '****************',
          address: 'Via Roma 123, Milano, 20100'
        })

      // Check updated status
      const updatedStatus = await request(app)
        .get(`/api/rider-onboarding/status/${sessionId}`)
        .set(riderAuthHeaders)
        .expect(200)

      expect(updatedStatus.body.status.overallProgress).toBeGreaterThan(0)
      expect(updatedStatus.body.status.nextAction).toBe('upload_documents')
    })

    it('should handle invalid session ID', async () => {
      await request(app)
        .get('/api/rider-onboarding/status/invalid_session')
        .set(riderAuthHeaders)
        .expect(404)
    })
  })

  describe('🔒 Security & Authorization', () => {
    it('should require authentication for all endpoints', async () => {
      // Test without auth headers
      await request(app)
        .post('/api/rider-onboarding/start')
        .send({
          firstName: 'Mario',
          lastName: 'Rossi',
          email: '<EMAIL>',
          phone: '+39 333 1234567'
        })
        .expect(401)

      await request(app)
        .get('/api/rider-onboarding/documents')
        .expect(401)
    })

    it('should validate file uploads properly', async () => {
      // Test with oversized file (mock)
      const largeBuffer = Buffer.alloc(15 * 1024 * 1024) // 15MB

      await request(app)
        .post('/api/rider-onboarding/documents/identity_card')
        .set(riderAuthHeaders)
        .attach('document', largeBuffer, 'large-file.jpg')
        .expect(400)
    })

    it('should sanitize input data', async () => {
      const maliciousData = {
        firstName: '<script>alert("xss")</script>',
        lastName: 'Rossi',
        email: '<EMAIL>',
        phone: '+39 333 1234567'
      }

      const response = await request(app)
        .post('/api/rider-onboarding/start')
        .set(riderAuthHeaders)
        .send(maliciousData)
        .expect(201)

      // Input should be sanitized
      expect(response.body.session).toBeDefined()
    })
  })

  describe('⚡ Performance & Rate Limiting', () => {
    it('should handle concurrent onboarding sessions', async () => {
      const promises = []

      // Create multiple concurrent onboarding sessions
      for (let i = 0; i < 5; i++) {
        promises.push(
          request(app)
            .post('/api/rider-onboarding/start')
            .set(riderAuthHeaders)
            .send({
              firstName: `Mario${i}`,
              lastName: 'Rossi',
              email: `mario${i}.<EMAIL>`,
              phone: `+39 333 123456${i}`
            })
        )
      }

      const responses = await Promise.all(promises)

      // All should succeed
      responses.forEach(response => {
        expect(response.status).toBe(201)
        expect(response.body.success).toBe(true)
      })
    })

    it('should respect rate limits', async () => {
      // This would test rate limiting in a real scenario
      // For now, just verify the endpoint responds correctly
      const response = await request(app)
        .post('/api/rider-onboarding/start')
        .set(riderAuthHeaders)
        .send({
          firstName: 'Mario',
          lastName: 'Rossi',
          email: '<EMAIL>',
          phone: '+39 333 1234567'
        })

      expect(response.status).toBe(201)
    })
  })
})
