import React from 'react'
import { Power, Wifi, WifiOff } from 'lucide-react'

interface AvailabilityToggleProps {
  isOnline: boolean
  loading: boolean
  onToggle: () => void
  lastSeen?: string | null
}

const AvailabilityToggle: React.FC<AvailabilityToggleProps> = ({
  isOnline,
  loading,
  onToggle,
  lastSeen
}) => {
  const formatLastSeen = (timestamp: string | null) => {
    if (!timestamp) return 'Mai'
    
    const date = new Date(timestamp)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / (1000 * 60))
    
    if (diffMins < 1) return 'Ora'
    if (diffMins < 60) return `${diffMins} min fa`
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)} ore fa`
    return date.toLocaleDateString('it-IT')
  }

  return (
    <div className="bg-white rounded-lg p-6 border border-gray-200">
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">
            Stato Disponibilità
          </h3>
          <p className="text-sm text-gray-600">
            Gestisci la tua disponibilità per ricevere prenotazioni
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          {isOnline ? (
            <Wifi className="w-6 h-6 text-green-500" />
          ) : (
            <WifiOff className="w-6 h-6 text-gray-400" />
          )}
        </div>
      </div>

      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className={`w-3 h-3 rounded-full ${
            isOnline ? 'bg-green-500' : 'bg-gray-400'
          }`} />
          <span className={`font-medium ${
            isOnline ? 'text-green-700' : 'text-gray-600'
          }`}>
            {isOnline ? 'Online - Disponibile' : 'Offline - Non disponibile'}
          </span>
        </div>

        <button
          onClick={onToggle}
          disabled={loading}
          className={`
            flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200
            ${isOnline 
              ? 'bg-red-100 hover:bg-red-200 text-red-700 border border-red-200' 
              : 'bg-green-100 hover:bg-green-200 text-green-700 border border-green-200'
            }
            ${loading ? 'opacity-50 cursor-not-allowed' : 'hover:shadow-sm'}
          `}
        >
          {loading ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current" />
          ) : (
            <Power className="w-4 h-4" />
          )}
          <span>
            {loading 
              ? 'Aggiornamento...' 
              : isOnline 
                ? 'Vai Offline' 
                : 'Vai Online'
            }
          </span>
        </button>
      </div>

      <div className="text-xs text-gray-500 border-t pt-3">
        <div className="flex justify-between">
          <span>Ultima attività:</span>
          <span>{formatLastSeen(lastSeen)}</span>
        </div>
      </div>

      {isOnline && (
        <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
            <span className="text-sm text-green-700 font-medium">
              Sei visibile agli esercenti e puoi ricevere prenotazioni
            </span>
          </div>
        </div>
      )}

      {!isOnline && (
        <div className="mt-4 p-3 bg-gray-50 border border-gray-200 rounded-lg">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-gray-400 rounded-full" />
            <span className="text-sm text-gray-600">
              Non sei visibile agli esercenti. Vai online per ricevere prenotazioni.
            </span>
          </div>
        </div>
      )}
    </div>
  )
}

export default AvailabilityToggle
