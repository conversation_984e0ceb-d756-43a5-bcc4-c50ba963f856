/**
 * Sidebar Navigation Component
 * Main navigation sidebar for BeMyRider dashboard
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { useState } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { clsx } from 'clsx'
import { motion, AnimatePresence } from 'framer-motion'
import {
  HomeIcon,
  CalendarIcon,
  UserGroupIcon,
  ChartBarIcon,
  CogIcon,
  DocumentTextIcon,
  TruckIcon,
  MapPinIcon,
  BellIcon,
  QuestionMarkCircleIcon,
  ChevronLeftIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline'

// Navigation items for different user types
const navigationItems = {
  rider: [
    { name: 'Dashboard', href: '/rider/dashboard', icon: HomeIcon },
    { name: 'Disponibilità', href: '/rider/availability', icon: CalendarIcon },
    { name: 'Prenotazioni', href: '/rider/bookings', icon: DocumentTextIcon },
    { name: '<PERSON><PERSON><PERSON><PERSON>', href: '/rider/earnings', icon: ChartBarIcon },
    { name: 'Profilo', href: '/rider/profile', icon: CogIcon },
  ],
  merchant: [
    { name: 'Dashboard', href: '/merchant/dashboard', icon: HomeIcon },
    { name: 'Trova Rider', href: '/merchant/find-riders', icon: MapPinIcon },
    { name: 'Prenotazioni', href: '/merchant/bookings', icon: DocumentTextIcon },
    { name: 'Flotta', href: '/merchant/fleet', icon: TruckIcon },
    { name: 'Analytics', href: '/merchant/analytics', icon: ChartBarIcon },
    { name: 'Impostazioni', href: '/merchant/settings', icon: CogIcon },
  ]
}

const Sidebar = ({ userType = 'rider', collapsed = false, onToggleCollapse }) => {
  const location = useLocation()
  const [hoveredItem, setHoveredItem] = useState(null)
  
  const navigation = navigationItems[userType] || navigationItems.rider

  return (
    <div className={clsx(
      'flex flex-col h-full bg-white border-r border-gray-200 transition-all duration-300',
      collapsed ? 'w-16' : 'w-64'
    )}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <AnimatePresence mode="wait">
          {!collapsed && (
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.2 }}
              className="flex items-center space-x-3"
            >
              <div className="w-8 h-8 bg-gradient-to-br from-bemyrider-600 to-primary-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">BR</span>
              </div>
              <div>
                <h1 className="bemyrider-logo text-lg">bemyrider</h1>
                <p className="text-xs text-gray-500 capitalize">{userType}</p>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
        
        <button
          onClick={onToggleCollapse}
          className="p-1.5 rounded-md hover:bg-gray-100 transition-colors"
        >
          {collapsed ? (
            <ChevronRightIcon className="w-4 h-4 text-gray-500" />
          ) : (
            <ChevronLeftIcon className="w-4 h-4 text-gray-500" />
          )}
        </button>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2">
        {navigation.map((item) => {
          const isActive = location.pathname === item.href
          const Icon = item.icon

          return (
            <Link
              key={item.name}
              to={item.href}
              onMouseEnter={() => setHoveredItem(item.name)}
              onMouseLeave={() => setHoveredItem(null)}
              className={clsx(
                'flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 relative',
                isActive
                  ? 'bg-primary-50 text-primary-700 border border-primary-200'
                  : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
              )}
            >
              <Icon className={clsx(
                'flex-shrink-0 w-5 h-5',
                collapsed ? 'mx-auto' : 'mr-3'
              )} />
              
              <AnimatePresence mode="wait">
                {!collapsed && (
                  <motion.span
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -10 }}
                    transition={{ duration: 0.2 }}
                  >
                    {item.name}
                  </motion.span>
                )}
              </AnimatePresence>

              {/* Active indicator */}
              {isActive && (
                <motion.div
                  layoutId="activeTab"
                  className="absolute right-2 w-2 h-2 bg-primary-600 rounded-full"
                  transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                />
              )}

              {/* Tooltip for collapsed state */}
              {collapsed && hoveredItem === item.name && (
                <motion.div
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -10 }}
                  className="absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-xs rounded-md whitespace-nowrap z-50"
                >
                  {item.name}
                  <div className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-1 w-2 h-2 bg-gray-900 rotate-45" />
                </motion.div>
              )}
            </Link>
          )
        })}
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200 space-y-2">
        <Link
          to="/notifications"
          className={clsx(
            'flex items-center px-3 py-2 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors',
            collapsed && 'justify-center'
          )}
        >
          <BellIcon className={clsx('w-5 h-5', !collapsed && 'mr-3')} />
          {!collapsed && <span>Notifiche</span>}
        </Link>
        
        <Link
          to="/help"
          className={clsx(
            'flex items-center px-3 py-2 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors',
            collapsed && 'justify-center'
          )}
        >
          <QuestionMarkCircleIcon className={clsx('w-5 h-5', !collapsed && 'mr-3')} />
          {!collapsed && <span>Aiuto</span>}
        </Link>
      </div>
    </div>
  )
}

export default Sidebar
