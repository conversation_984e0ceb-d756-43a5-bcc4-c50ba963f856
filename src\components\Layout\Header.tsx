import React from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { ArrowLeft, Heart, Menu } from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'

const Header: React.FC = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const { user } = useAuth()

  const getTitle = () => {
    const path = location.pathname
    if (path === '/app') return 'Dashboard'
    if (path === '/app/riders') return 'Trova Rider'
    if (path.includes('/app/riders/')) return 'Prenotazione a Tariffa Oraria'
    if (path.includes('/app/booking/')) return 'Prenotazione a Tariffa Oraria'
    if (path === '/app/profile') return 'Profilo'
    return 'bemyrider'
  }

  const showBackButton = () => {
    return location.pathname !== '/app' && location.pathname !== '/app/riders'
  }

  const showFavoriteButton = () => {
    return location.pathname.includes('/app/riders/') && !location.pathname.includes('/booking/')
  }

  return (
    <header className="bg-purple-600 text-white px-4 py-4 flex items-center justify-between">
      <div className="flex items-center">
        {showBackButton() && (
          <button
            onClick={() => navigate(-1)}
            className="mr-3 p-1 hover:bg-purple-700 rounded"
          >
            <ArrowLeft size={24} />
          </button>
        )}
        <h1 className={`text-lg font-semibold ${getTitle() === 'bemyrider' ? 'bemyrider-logo text-white' : ''}`}>
          {getTitle()}
        </h1>
      </div>

      <div className="flex items-center space-x-3">
        {showFavoriteButton() && (
          <button className="p-2 hover:bg-purple-700 rounded">
            <Heart size={20} />
            <span className="text-xs block">Preferito</span>
          </button>
        )}
        
        <button className="p-2 hover:bg-purple-700 rounded">
          <Menu size={20} />
        </button>
      </div>
    </header>
  )
}

export default Header
