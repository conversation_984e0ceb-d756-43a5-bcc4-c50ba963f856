/**
 * BaseRepository Unit Tests
 * Tests the core repository functionality
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { BaseRepository } from '../../../server/repositories/BaseRepository.js'

describe('BaseRepository', () => {
  let repository
  let mockSupabase

  beforeEach(() => {
    // Setup mock Supabase client
    mockSupabase = {
      from: vi.fn().mockReturnThis(),
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      single: vi.fn(),
      insert: vi.fn().mockReturnThis(),
      update: vi.fn().mockReturnThis(),
      delete: vi.fn().mockReturnThis(),
      limit: vi.fn().mockReturnThis(),
      range: vi.fn().mockReturnThis(),
      order: vi.fn().mockReturnThis(),
      in: vi.fn().mockReturnThis()
    }

    repository = new BaseRepository('test_table')
    repository.db = mockSupabase
  })

  describe('findById', () => {
    it('should find a record by ID successfully', async () => {
      const mockData = { id: '123', name: 'Test Record' }
      mockSupabase.single.mockResolvedValue({ data: mockData, error: null })

      const result = await repository.findById('123')

      expect(mockSupabase.from).toHaveBeenCalledWith('test_table')
      expect(mockSupabase.select).toHaveBeenCalledWith('*')
      expect(mockSupabase.eq).toHaveBeenCalledWith('id', '123')
      expect(result).toEqual(mockData)
    })

    it('should return null when record not found', async () => {
      mockSupabase.single.mockResolvedValue({ 
        data: null, 
        error: { code: 'PGRST116' } 
      })

      const result = await repository.findById('nonexistent')

      expect(result).toBeNull()
    })

    it('should throw error for database errors', async () => {
      const dbError = new Error('Database connection failed')
      mockSupabase.single.mockResolvedValue({ 
        data: null, 
        error: dbError 
      })

      await expect(repository.findById('123')).rejects.toThrow('Database connection failed')
    })

    it('should allow custom column selection', async () => {
      const mockData = { id: '123', name: 'Test Record' }
      mockSupabase.single.mockResolvedValue({ data: mockData, error: null })

      await repository.findById('123', 'id, name')

      expect(mockSupabase.select).toHaveBeenCalledWith('id, name')
    })
  })

  describe('findMany', () => {
    it('should find multiple records with filters', async () => {
      const mockData = [
        { id: '1', status: 'active' },
        { id: '2', status: 'active' }
      ]
      mockSupabase.select.mockResolvedValue({ data: mockData, error: null })

      const result = await repository.findMany({ status: 'active' })

      expect(mockSupabase.from).toHaveBeenCalledWith('test_table')
      expect(mockSupabase.eq).toHaveBeenCalledWith('status', 'active')
      expect(result).toEqual(mockData)
    })

    it('should handle array filters with IN operator', async () => {
      const mockData = [{ id: '1' }, { id: '2' }]
      mockSupabase.select.mockResolvedValue({ data: mockData, error: null })

      await repository.findMany({ id: ['1', '2'] })

      expect(mockSupabase.in).toHaveBeenCalledWith('id', ['1', '2'])
    })

    it('should handle complex filters with operators', async () => {
      const mockData = [{ id: '1', price: 100 }]
      mockSupabase.select.mockResolvedValue({ data: mockData, error: null })
      mockSupabase.gte = vi.fn().mockReturnThis()

      await repository.findMany({ 
        price: { operator: 'gte', value: 50 } 
      })

      expect(mockSupabase.gte).toHaveBeenCalledWith('price', 50)
    })

    it('should apply ordering when specified', async () => {
      const mockData = [{ id: '1' }]
      mockSupabase.select.mockResolvedValue({ data: mockData, error: null })

      await repository.findMany({}, {
        orderBy: { column: 'created_at', ascending: false }
      })

      expect(mockSupabase.order).toHaveBeenCalledWith('created_at', { ascending: false })
    })

    it('should apply pagination when specified', async () => {
      const mockData = [{ id: '1' }]
      mockSupabase.select.mockResolvedValue({ data: mockData, error: null })

      await repository.findMany({}, {
        limit: 10,
        offset: 20
      })

      expect(mockSupabase.range).toHaveBeenCalledWith(20, 29)
    })

    it('should return empty array when no data found', async () => {
      mockSupabase.select.mockResolvedValue({ data: null, error: null })

      const result = await repository.findMany()

      expect(result).toEqual([])
    })
  })

  describe('findOne', () => {
    it('should find a single record with filters', async () => {
      const mockData = { id: '123', email: '<EMAIL>' }
      mockSupabase.single.mockResolvedValue({ data: mockData, error: null })

      const result = await repository.findOne({ email: '<EMAIL>' })

      expect(mockSupabase.eq).toHaveBeenCalledWith('email', '<EMAIL>')
      expect(result).toEqual(mockData)
    })

    it('should return null when no record found', async () => {
      mockSupabase.single.mockResolvedValue({ 
        data: null, 
        error: { code: 'PGRST116' } 
      })

      const result = await repository.findOne({ email: '<EMAIL>' })

      expect(result).toBeNull()
    })
  })

  describe('create', () => {
    it('should create a new record successfully', async () => {
      const inputData = { name: 'New Record', status: 'active' }
      const mockResult = { id: '123', ...inputData }
      mockSupabase.single.mockResolvedValue({ data: mockResult, error: null })

      const result = await repository.create(inputData)

      expect(mockSupabase.insert).toHaveBeenCalledWith(inputData)
      expect(mockSupabase.select).toHaveBeenCalledWith('*')
      expect(result).toEqual(mockResult)
    })

    it('should handle creation errors', async () => {
      const inputData = { name: 'Invalid Record' }
      const dbError = new Error('Validation failed')
      mockSupabase.single.mockResolvedValue({ data: null, error: dbError })

      await expect(repository.create(inputData)).rejects.toThrow('Validation failed')
    })

    it('should allow custom column selection for return data', async () => {
      const inputData = { name: 'New Record' }
      const mockResult = { id: '123', name: 'New Record' }
      mockSupabase.single.mockResolvedValue({ data: mockResult, error: null })

      await repository.create(inputData, 'id, name')

      expect(mockSupabase.select).toHaveBeenCalledWith('id, name')
    })
  })

  describe('createMany', () => {
    it('should create multiple records successfully', async () => {
      const inputData = [
        { name: 'Record 1' },
        { name: 'Record 2' }
      ]
      const mockResult = [
        { id: '1', name: 'Record 1' },
        { id: '2', name: 'Record 2' }
      ]
      mockSupabase.select.mockResolvedValue({ data: mockResult, error: null })

      const result = await repository.createMany(inputData)

      expect(mockSupabase.insert).toHaveBeenCalledWith(inputData)
      expect(result).toEqual(mockResult)
    })

    it('should return empty array when no data provided', async () => {
      mockSupabase.select.mockResolvedValue({ data: null, error: null })

      const result = await repository.createMany([])

      expect(result).toEqual([])
    })
  })

  describe('updateById', () => {
    it('should update a record by ID successfully', async () => {
      const updateData = { name: 'Updated Record' }
      const mockResult = { id: '123', name: 'Updated Record' }
      mockSupabase.single.mockResolvedValue({ data: mockResult, error: null })

      const result = await repository.updateById('123', updateData)

      expect(mockSupabase.update).toHaveBeenCalledWith(updateData)
      expect(mockSupabase.eq).toHaveBeenCalledWith('id', '123')
      expect(result).toEqual(mockResult)
    })

    it('should handle update errors', async () => {
      const updateData = { name: 'Invalid Update' }
      const dbError = new Error('Update failed')
      mockSupabase.single.mockResolvedValue({ data: null, error: dbError })

      await expect(repository.updateById('123', updateData)).rejects.toThrow('Update failed')
    })
  })

  describe('updateMany', () => {
    it('should update multiple records with filters', async () => {
      const filters = { status: 'pending' }
      const updateData = { status: 'active' }
      const mockResult = [
        { id: '1', status: 'active' },
        { id: '2', status: 'active' }
      ]
      mockSupabase.select.mockResolvedValue({ data: mockResult, error: null })

      const result = await repository.updateMany(filters, updateData)

      expect(mockSupabase.update).toHaveBeenCalledWith(updateData)
      expect(mockSupabase.eq).toHaveBeenCalledWith('status', 'pending')
      expect(result).toEqual(mockResult)
    })
  })

  describe('softDeleteById', () => {
    it('should soft delete a record by setting deleted_at', async () => {
      const mockResult = { id: '123', deleted_at: '2024-01-01T00:00:00Z' }
      mockSupabase.single.mockResolvedValue({ data: mockResult, error: null })

      const result = await repository.softDeleteById('123')

      expect(mockSupabase.update).toHaveBeenCalledWith(
        expect.objectContaining({ deleted_at: expect.any(String) })
      )
      expect(mockSupabase.eq).toHaveBeenCalledWith('id', '123')
      expect(result).toEqual(mockResult)
    })
  })

  describe('deleteById', () => {
    it('should hard delete a record by ID', async () => {
      mockSupabase.delete.mockResolvedValue({ error: null })

      const result = await repository.deleteById('123')

      expect(mockSupabase.delete).toHaveBeenCalled()
      expect(mockSupabase.eq).toHaveBeenCalledWith('id', '123')
      expect(result).toBe(true)
    })

    it('should handle deletion errors', async () => {
      const dbError = new Error('Deletion failed')
      mockSupabase.delete.mockResolvedValue({ error: dbError })

      await expect(repository.deleteById('123')).rejects.toThrow('Deletion failed')
    })
  })

  describe('count', () => {
    it('should count records with filters', async () => {
      mockSupabase.select.mockResolvedValue({ count: 5, error: null })

      const result = await repository.count({ status: 'active' })

      expect(mockSupabase.select).toHaveBeenCalledWith('*', { count: 'exact', head: true })
      expect(mockSupabase.eq).toHaveBeenCalledWith('status', 'active')
      expect(result).toBe(5)
    })

    it('should return 0 when count is null', async () => {
      mockSupabase.select.mockResolvedValue({ count: null, error: null })

      const result = await repository.count()

      expect(result).toBe(0)
    })
  })

  describe('exists', () => {
    it('should return true when records exist', async () => {
      mockSupabase.select.mockResolvedValue({ count: 1, error: null })

      const result = await repository.exists({ email: '<EMAIL>' })

      expect(result).toBe(true)
    })

    it('should return false when no records exist', async () => {
      mockSupabase.select.mockResolvedValue({ count: 0, error: null })

      const result = await repository.exists({ email: '<EMAIL>' })

      expect(result).toBe(false)
    })
  })

  describe('paginate', () => {
    it('should return paginated results with metadata', async () => {
      const mockData = [{ id: '1' }, { id: '2' }]
      mockSupabase.select.mockResolvedValueOnce({ data: mockData, error: null })
      mockSupabase.select.mockResolvedValueOnce({ count: 10, error: null })

      const result = await repository.paginate({}, { page: 1, limit: 2 })

      expect(result).toEqual({
        data: mockData,
        pagination: {
          page: 1,
          limit: 2,
          total: 10,
          pages: 5,
          hasNext: true,
          hasPrev: false
        }
      })
    })
  })
})
