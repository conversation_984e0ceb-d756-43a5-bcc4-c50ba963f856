import React from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import { useDashboard } from '../hooks/useDashboard'
import { useRiderAvailability } from '../hooks/useRiderAvailability'
import { Calendar, Clock, Star, TrendingUp, Users, MapPin, RefreshCw } from 'lucide-react'

// Rider components
import AvailabilityToggle from '../components/Rider/AvailabilityToggle'
import WorkSchedule from '../components/Rider/WorkSchedule'
import IncomingBookings from '../components/Rider/IncomingBookings'

const DashboardPage: React.FC = () => {
  const { user, getUserType } = useAuth()
  const navigate = useNavigate()
  const userType = getUserType()
  const {
    stats,
    recentBookings,
    earnings,
    activity,
    loading,
    error,
    refreshData,
    userType: dashboardUserType
  } = useDashboard()

  // Rider-specific hooks
  const riderAvailability = useRiderAvailability()

  // Debug info
  console.log('🔍 Dashboard Debug:', {
    user: user?.id,
    userType,
    dashboardUserType,
    loading,
    error
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return 'text-yellow-600 bg-yellow-100'
      case 'CONFIRMED': return 'text-blue-600 bg-blue-100'
      case 'COMPLETED': return 'text-green-600 bg-green-100'
      case 'CANCELLED': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'PENDING': return 'In attesa'
      case 'CONFIRMED': return 'Confermata'
      case 'COMPLETED': return 'Completata'
      case 'CANCELLED': return 'Annullata'
      default: return status
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Caricamento dashboard...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="text-red-500 mb-4">
            <TrendingUp className="w-12 h-12 mx-auto mb-2" />
            <p className="text-lg font-medium">Errore nel caricamento</p>
            <p className="text-sm text-gray-600 mt-1">{error}</p>
          </div>
          <button
            onClick={refreshData}
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center mx-auto"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Riprova
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-gray-50 min-h-screen">
      {/* Welcome Section */}
      <div className="bg-white px-4 py-6 border-b">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Ciao, {user?.fullName || user?.firstName || 'Utente'}!
            </h1>
            <p className="text-gray-600 mt-1">
              {userType === 'ESERCENTE'
                ? 'Gestisci le tue prenotazioni e trova rider'
                : 'Gestisci la tua disponibilità e guadagni'
              }
            </p>
          </div>
          <button
            onClick={refreshData}
            className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
            title="Aggiorna dati"
          >
            <RefreshCw className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="px-4 py-6">
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className="bg-white rounded-lg p-4 border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Prenotazioni Totali</p>
                <p className="text-2xl font-bold text-gray-900">{stats?.totalBookings || 0}</p>
              </div>
              <Calendar className="w-8 h-8 text-purple-600" />
            </div>
          </div>

          <div className="bg-white rounded-lg p-4 border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Prenotazioni Attive</p>
                <p className="text-2xl font-bold text-gray-900">{stats?.activeBookings || 0}</p>
              </div>
              <Clock className="w-8 h-8 text-blue-600" />
            </div>
          </div>

          <div className="bg-white rounded-lg p-4 border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">
                  {userType === 'ESERCENTE' ? 'Speso Totale' : 'Guadagni Totali'}
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  €{userType === 'ESERCENTE'
                    ? (stats?.totalSpent || 0).toFixed(2)
                    : (stats?.totalEarnings || 0).toFixed(2)
                  }
                </p>
              </div>
              <TrendingUp className="w-8 h-8 text-green-600" />
            </div>
          </div>

          <div className="bg-white rounded-lg p-4 border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">
                  {userType === 'ESERCENTE' ? 'Prenotazioni Completate' : 'Valutazione Media'}
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {userType === 'ESERCENTE'
                    ? (stats?.completedBookings || 0)
                    : (stats?.averageRating || 0).toFixed(1)
                  }
                </p>
              </div>
              {userType === 'ESERCENTE' ? (
                <Calendar className="w-8 h-8 text-orange-600" />
              ) : (
                <Star className="w-8 h-8 text-orange-600" />
              )}
            </div>
          </div>
        </div>

        {/* Rider-specific components */}
        {userType === 'RIDER' && riderAvailability.isRider && (
          <div className="space-y-6 mb-6">
            {/* Debug info for rider */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
              <h4 className="font-medium text-blue-900">🔍 Debug Rider:</h4>
              <p className="text-sm text-blue-700">
                Tipo: {userType} | Rider: {riderAvailability.isRider ? 'Sì' : 'No'} |
                Online: {riderAvailability.isOnline ? 'Sì' : 'No'} |
                Loading: {riderAvailability.loading ? 'Sì' : 'No'}
              </p>
            </div>

            {/* Availability Toggle */}
            <AvailabilityToggle
              isOnline={riderAvailability.isOnline}
              loading={riderAvailability.loading}
              onToggle={riderAvailability.toggleAvailability}
              lastSeen={riderAvailability.lastSeen}
            />

            {/* Work Schedule */}
            <WorkSchedule
              schedule={riderAvailability.workSchedule}
              onSave={riderAvailability.updateSchedule}
              loading={riderAvailability.loading}
            />

            {/* Incoming Bookings */}
            <IncomingBookings
              bookings={recentBookings?.filter(b => b.status === 'PENDING') || []}
              loading={loading}
              onAccept={(bookingId) => {
                console.log('Accept booking:', bookingId)
                // TODO: Implement accept booking API
              }}
              onReject={(bookingId) => {
                console.log('Reject booking:', bookingId)
                // TODO: Implement reject booking API
              }}
            />
          </div>
        )}

        {/* Show message if user is RIDER but components not loading */}
        {userType === 'RIDER' && !riderAvailability.isRider && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
            <h4 className="font-medium text-yellow-900">⚠️ Componenti Rider non caricati</h4>
            <p className="text-sm text-yellow-700 mt-1">
              Verifica che l'utente sia correttamente configurato come RIDER nel database.
            </p>
          </div>
        )}

        {/* Esercente Quick Actions */}
        {userType === 'ESERCENTE' && (
          <div className="mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Azioni Rapide</h2>
            <div className="grid grid-cols-2 gap-4">
              <button
                onClick={() => navigate('/app/riders')}
                className="bg-purple-600 hover:bg-purple-700 text-white p-4 rounded-lg text-center transition-colors"
              >
                <Users className="w-6 h-6 mx-auto mb-2" />
                <span className="text-sm font-medium">Trova Rider</span>
              </button>
              <button
                onClick={() => navigate('/app/bookings')}
                className="bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-lg text-center transition-colors"
              >
                <Calendar className="w-6 h-6 mx-auto mb-2" />
                <span className="text-sm font-medium">Le Mie Prenotazioni</span>
              </button>
            </div>
          </div>
        )}

        {/* Recent Bookings */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">Prenotazioni Recenti</h2>
            <button
              onClick={() => navigate('/app/bookings')}
              className="text-purple-600 hover:text-purple-700 text-sm font-medium"
            >
              Vedi tutte
            </button>
          </div>

          <div className="space-y-3">
            {recentBookings && recentBookings.length > 0 ? (
              recentBookings.map((booking) => (
                <div key={booking.id} className="bg-white rounded-lg p-4 border">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900">
                        {userType === 'ESERCENTE'
                          ? booking.rider?.name || booking.rider?.first_name || 'Rider'
                          : booking.client?.name || booking.client?.first_name || 'Cliente'
                        }
                      </h3>
                      <div className="flex items-center space-x-4 mt-1 text-sm text-gray-600">
                        <span>{new Date(booking.date || booking.created_at).toLocaleDateString('it-IT')}</span>
                        {booking.duration && (
                          <span>{booking.duration} ora{booking.duration > 1 ? 'e' : ''}</span>
                        )}
                        {booking.address && (
                          <span className="flex items-center">
                            <MapPin className="w-3 h-3 mr-1" />
                            {booking.address.substring(0, 30)}...
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-semibold text-gray-900">
                        €{(booking.totalAmount || booking.total_amount || 0).toFixed(2)}
                      </div>
                      <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(booking.status)}`}>
                        {getStatusText(booking.status)}
                      </span>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="bg-white rounded-lg p-8 border text-center">
                <Calendar className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">Nessuna prenotazione ancora</p>
                {userType === 'ESERCENTE' && (
                  <button
                    onClick={() => navigate('/app/riders')}
                    className="mt-4 text-purple-600 hover:text-purple-700 font-medium"
                  >
                    Trova il tuo primo rider
                  </button>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default DashboardPage
