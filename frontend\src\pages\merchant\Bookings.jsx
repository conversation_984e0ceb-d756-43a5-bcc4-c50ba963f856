/**
 * Merchant Bookings Page
 * Manage merchant bookings and shifts
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { useState } from 'react'
import { motion } from 'framer-motion'
import { DocumentTextIcon, ClockIcon, UserIcon, CheckCircleIcon } from '@heroicons/react/24/outline'

import Card, { CardHeader, CardTitle, CardContent } from '@components/ui/Card'
import Button from '@components/ui/Button'
import Header from '@components/layout/Header'

const mockBookings = [
  {
    id: 1,
    riderName: '<PERSON>',
    startTime: '2024-12-22T14:00:00Z',
    endTime: '2024-12-22T16:00:00Z',
    status: 'confirmed',
    rate: 12.00,
    totalCost: 24.00
  },
  {
    id: 2,
    riderName: 'Luigi Verdi',
    startTime: '2024-12-22T19:00:00Z',
    endTime: '2024-12-22T21:00:00Z',
    status: 'pending',
    rate: 11.50,
    totalCost: 23.00
  }
]

const MerchantBookings = () => {
  const [filter, setFilter] = useState('all')

  return (
    <div className="min-h-screen bg-gray-50">
      <Header 
        title="Gestione Prenotazioni"
        subtitle="Visualizza e gestisci tutte le prenotazioni"
      />

      <div className="p-6 space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <DocumentTextIcon className="w-5 h-5 text-blue-600" />
              <span>Le Tue Prenotazioni</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockBookings.map((booking, index) => (
                <motion.div
                  key={booking.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="p-4 border border-gray-200 rounded-lg"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <UserIcon className="w-8 h-8 text-blue-600" />
                      <div>
                        <h3 className="font-medium text-gray-900">{booking.riderName}</h3>
                        <p className="text-sm text-gray-600">
                          {new Date(booking.startTime).toLocaleString('it-IT')} - 
                          {new Date(booking.endTime).toLocaleString('it-IT')}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-gray-900">€{booking.totalCost.toFixed(2)}</p>
                      <span className={`status-badge ${
                        booking.status === 'confirmed' ? 'status-success' : 'status-warning'
                      }`}>
                        {booking.status === 'confirmed' ? 'Confermato' : 'In attesa'}
                      </span>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default MerchantBookings
