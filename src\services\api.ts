import axios from 'axios'

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api'

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor for logging
api.interceptors.request.use(
  (config) => {
    console.log(`🔄 API Request: ${config.method?.toUpperCase()} ${config.url}`)
    return config
  },
  (error) => {
    console.error('❌ API Request Error:', error)
    return Promise.reject(error)
  }
)

// Response interceptor for logging and error handling
api.interceptors.response.use(
  (response) => {
    console.log(`✅ API Response: ${response.status} ${response.config.url}`)
    return response
  },
  (error) => {
    console.error('❌ API Response Error:', error.response?.data || error.message)
    return Promise.reject(error)
  }
)

// Types
export interface User {
  id: string
  clerk_id: string
  email: string
  first_name?: string
  last_name?: string
  name?: string
  type: 'ESERCENTE' | 'RIDER'
  profile_complete: boolean
  phone?: string
  city?: string
  hourly_rate?: number
  vehicle_type?: string
  vehicle_model?: string
  description?: string
  is_available?: boolean
  created_at: string
  updated_at: string
}

export interface Rider extends User {
  averageRating: number
  totalReviews: number
  totalBookings: number
}

export interface DashboardStats {
  totalBookings: number
  activeBookings: number
  totalEarnings?: number
  averageRating?: number
  totalReviews?: number
  totalSpent?: number
  completedBookings?: number
}

export interface ApiResponse<T> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

// API Functions
export const apiService = {
  // Health check
  async healthCheck(): Promise<{ status: string; message: string }> {
    const response = await api.get('/health')
    return response.data
  },

  // Users
  async syncUser(userData: {
    clerkId: string
    email: string
    firstName?: string
    lastName?: string
    name?: string
    userType: 'ESERCENTE' | 'RIDER'
    profileComplete?: boolean
  }): Promise<ApiResponse<User>> {
    const response = await api.post('/users/sync', userData)
    return response.data
  },

  async getUserProfile(clerkId: string): Promise<ApiResponse<User>> {
    const response = await api.get(`/users/profile/${clerkId}`)
    return response.data
  },

  async updateUserProfile(clerkId: string, updates: Partial<User>): Promise<ApiResponse<User>> {
    const response = await api.put(`/users/profile/${clerkId}`, updates)
    return response.data
  },

  async getRiders(filters?: {
    vehicleType?: string
    minRate?: number
    maxRate?: number
    city?: string
    available?: boolean
    page?: number
    limit?: number
  }): Promise<ApiResponse<{ riders: Rider[]; pagination: any }>> {
    const params = new URLSearchParams()
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined) {
          params.append(key, value.toString())
        }
      })
    }
    
    const response = await api.get(`/users/riders?${params.toString()}`)
    return response.data
  },

  // Dashboard
  async getDashboardStats(clerkId: string): Promise<ApiResponse<DashboardStats>> {
    const response = await api.get(`/dashboard/stats?clerkId=${clerkId}`)
    return response.data
  },

  async getRecentBookings(clerkId: string, limit = 5): Promise<ApiResponse<any[]>> {
    const response = await api.get(`/dashboard/recent-bookings?clerkId=${clerkId}&limit=${limit}`)
    return response.data
  },

  async getEarnings(clerkId: string, period = 'month'): Promise<ApiResponse<any>> {
    const response = await api.get(`/dashboard/earnings?clerkId=${clerkId}&period=${period}`)
    return response.data
  },

  async getActivity(clerkId: string, limit = 10): Promise<ApiResponse<any[]>> {
    const response = await api.get(`/dashboard/activity?clerkId=${clerkId}&limit=${limit}`)
    return response.data
  },

  // Rider Availability
  async updateAvailability(clerkId: string, isOnline: boolean): Promise<ApiResponse<any>> {
    const response = await api.post('/dashboard/availability', { clerkId, isOnline })
    return response.data
  },

  async updateWorkSchedule(clerkId: string, schedule: any): Promise<ApiResponse<any>> {
    const response = await api.put('/dashboard/schedule', { clerkId, schedule })
    return response.data
  },

  async getRiderData(clerkId: string): Promise<ApiResponse<any>> {
    const response = await api.get(`/dashboard/rider-data?clerkId=${clerkId}`)
    return response.data
  },

  // Bookings
  async createBooking(bookingData: {
    riderId: string
    clientClerkId: string
    date: string
    duration: number
    address: string
    instructions?: string
    hourlyRate: number
  }): Promise<ApiResponse<any>> {
    const response = await api.post('/bookings', bookingData)
    return response.data
  },

  async getUserBookings(clerkId: string, type = 'all'): Promise<ApiResponse<{ bookings: any[]; pagination: any }>> {
    const response = await api.get(`/bookings?clerkId=${clerkId}&type=${type}`)
    return response.data
  },

  async getBookingById(bookingId: string): Promise<ApiResponse<any>> {
    const response = await api.get(`/bookings/${bookingId}`)
    return response.data
  },

  async updateBookingStatus(bookingId: string, status: string, clerkId: string): Promise<ApiResponse<any>> {
    const response = await api.patch(`/bookings/${bookingId}/status`, { status, clerkId })
    return response.data
  },

  async cancelBooking(bookingId: string, clerkId: string): Promise<ApiResponse<any>> {
    const response = await api.delete(`/bookings/${bookingId}`, { data: { clerkId } })
    return response.data
  }
}

export default apiService
