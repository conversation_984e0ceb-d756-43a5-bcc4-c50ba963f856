/**
 * UserRepository Unit Tests
 * Tests user-specific repository functionality
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { UserRepository } from '../../../server/repositories/UserRepository.js'

describe('UserRepository', () => {
  let userRepository
  let mockSupabase

  beforeEach(() => {
    mockSupabase = {
      from: vi.fn().mockReturnThis(),
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      or: vi.fn().mockReturnThis(),
      single: vi.fn(),
      insert: vi.fn().mockReturnThis(),
      update: vi.fn().mockReturnThis(),
      order: vi.fn().mockReturnThis(),
      limit: vi.fn().mockReturnThis(),
      rpc: vi.fn()
    }

    userRepository = new UserRepository()
    userRepository.db = mockSupabase
  })

  describe('findByClerkId', () => {
    it('should find user by Clerk ID', async () => {
      const mockUser = global.createTestUser()
      mockSupabase.single.mockResolvedValue({ data: mockUser, error: null })

      const result = await userRepository.findByClerkId('test_clerk_id')

      expect(mockSupabase.from).toHaveBeenCalledWith('users')
      expect(mockSupabase.eq).toHaveBeenCalledWith('clerk_id', 'test_clerk_id')
      expect(result).toEqual(mockUser)
    })

    it('should return null when user not found', async () => {
      mockSupabase.single.mockResolvedValue({ 
        data: null, 
        error: { code: 'PGRST116' } 
      })

      const result = await userRepository.findByClerkId('nonexistent')

      expect(result).toBeNull()
    })
  })

  describe('findByEmail', () => {
    it('should find user by email', async () => {
      const mockUser = global.createTestUser({ email: '<EMAIL>' })
      mockSupabase.single.mockResolvedValue({ data: mockUser, error: null })

      const result = await userRepository.findByEmail('<EMAIL>')

      expect(mockSupabase.eq).toHaveBeenCalledWith('email', '<EMAIL>')
      expect(result).toEqual(mockUser)
    })
  })

  describe('getRiders', () => {
    it('should get all riders with default filters', async () => {
      const mockRiders = [
        global.createTestUser({ type: 'RIDER', city: 'Milano' }),
        global.createTestUser({ type: 'RIDER', city: 'Roma' })
      ]
      mockSupabase.select.mockResolvedValue({ data: mockRiders, error: null })

      const result = await userRepository.getRiders()

      expect(mockSupabase.from).toHaveBeenCalledWith('users')
      expect(mockSupabase.eq).toHaveBeenCalledWith('type', 'RIDER')
      expect(mockSupabase.order).toHaveBeenCalledWith('average_rating', { ascending: false })
      expect(result).toEqual(mockRiders)
    })

    it('should filter riders by city', async () => {
      const mockRiders = [
        global.createTestUser({ type: 'RIDER', city: 'Milano' })
      ]
      mockSupabase.select.mockResolvedValue({ data: mockRiders, error: null })

      await userRepository.getRiders({ city: 'Milano' })

      expect(mockSupabase.eq).toHaveBeenCalledWith('city', 'Milano')
    })

    it('should filter riders by vehicle type', async () => {
      const mockRiders = [
        global.createTestUser({ type: 'RIDER', vehicle_type: 'MOTO' })
      ]
      mockSupabase.select.mockResolvedValue({ data: mockRiders, error: null })

      await userRepository.getRiders({ vehicleType: 'MOTO' })

      expect(mockSupabase.eq).toHaveBeenCalledWith('vehicle_type', 'MOTO')
    })

    it('should filter riders by availability', async () => {
      const mockRiders = [
        global.createTestUser({ type: 'RIDER', is_available: true })
      ]
      mockSupabase.select.mockResolvedValue({ data: mockRiders, error: null })

      await userRepository.getRiders({ available: true })

      expect(mockSupabase.eq).toHaveBeenCalledWith('is_available', true)
    })

    it('should filter riders by online status', async () => {
      const mockRiders = [
        global.createTestUser({ type: 'RIDER', is_online: true })
      ]
      mockSupabase.select.mockResolvedValue({ data: mockRiders, error: null })

      await userRepository.getRiders({ online: true })

      expect(mockSupabase.eq).toHaveBeenCalledWith('is_online', true)
    })

    it('should filter riders by verification status', async () => {
      const mockRiders = [
        global.createTestUser({ type: 'RIDER', document_verified: true })
      ]
      mockSupabase.select.mockResolvedValue({ data: mockRiders, error: null })

      await userRepository.getRiders({ verified: true })

      expect(mockSupabase.eq).toHaveBeenCalledWith('document_verified', true)
    })
  })

  describe('getAvailableRidersNearLocation', () => {
    it('should find available riders near location using PostGIS', async () => {
      const location = { latitude: 45.4642, longitude: 9.1900 }
      const mockRiders = [
        { rider_id: 'rider1', distance_km: 2.5, hourly_rate: 25.00 },
        { rider_id: 'rider2', distance_km: 5.0, hourly_rate: 30.00 }
      ]
      mockSupabase.rpc.mockResolvedValue({ data: mockRiders, error: null })

      const result = await userRepository.getAvailableRidersNearLocation(location, 10)

      expect(mockSupabase.rpc).toHaveBeenCalledWith('find_available_riders', {
        p_location: 'POINT(9.1900 45.4642)',
        p_date: expect.any(String),
        p_duration: 60,
        p_vehicle_type: null,
        p_max_distance: 10,
        p_limit: 10
      })
      expect(result).toEqual(mockRiders)
    })

    it('should fallback to basic query when PostGIS fails', async () => {
      const location = { latitude: 45.4642, longitude: 9.1900 }
      mockSupabase.rpc.mockRejectedValue(new Error('PostGIS not available'))
      mockSupabase.select.mockResolvedValue({ data: [], error: null })

      const result = await userRepository.getAvailableRidersNearLocation(location)

      expect(result).toEqual([])
    })

    it('should pass custom filters to PostGIS function', async () => {
      const location = { latitude: 45.4642, longitude: 9.1900 }
      const filters = {
        date: '2024-01-01T10:00:00Z',
        duration: 120,
        vehicleType: 'MOTO',
        limit: 5
      }
      mockSupabase.rpc.mockResolvedValue({ data: [], error: null })

      await userRepository.getAvailableRidersNearLocation(location, 15, filters)

      expect(mockSupabase.rpc).toHaveBeenCalledWith('find_available_riders', {
        p_location: 'POINT(9.1900 45.4642)',
        p_date: '2024-01-01T10:00:00Z',
        p_duration: 120,
        p_vehicle_type: 'MOTO',
        p_max_distance: 15,
        p_limit: 5
      })
    })
  })

  describe('updateStatistics', () => {
    it('should call database function to update user statistics', async () => {
      mockSupabase.rpc.mockResolvedValue({ error: null })

      await userRepository.updateStatistics('user123')

      expect(mockSupabase.rpc).toHaveBeenCalledWith('update_user_statistics', {
        p_user_id: 'user123'
      })
    })

    it('should handle statistics update errors', async () => {
      const dbError = new Error('Statistics update failed')
      mockSupabase.rpc.mockResolvedValue({ error: dbError })

      await expect(userRepository.updateStatistics('user123')).rejects.toThrow('Statistics update failed')
    })
  })

  describe('updateAvailability', () => {
    it('should update rider availability status', async () => {
      const mockResult = {
        id: 'user123',
        is_online: true,
        is_available: true,
        last_seen: '2024-01-01T10:00:00Z'
      }
      mockSupabase.single.mockResolvedValue({ data: mockResult, error: null })

      const result = await userRepository.updateAvailability('user123', true, true)

      expect(mockSupabase.update).toHaveBeenCalledWith({
        is_online: true,
        is_available: true,
        last_seen: expect.any(String)
      })
      expect(mockSupabase.eq).toHaveBeenCalledWith('id', 'user123')
      expect(result).toEqual(mockResult)
    })

    it('should update last_seen timestamp', async () => {
      const mockResult = { id: 'user123', last_seen: expect.any(String) }
      mockSupabase.single.mockResolvedValue({ data: mockResult, error: null })

      await userRepository.updateAvailability('user123', false, false)

      const updateCall = mockSupabase.update.mock.calls[0][0]
      expect(updateCall.last_seen).toBeDefined()
      expect(new Date(updateCall.last_seen)).toBeInstanceOf(Date)
    })
  })

  describe('updateLocation', () => {
    it('should update user location with PostGIS point', async () => {
      const location = { latitude: 45.4642, longitude: 9.1900 }
      const mockResult = {
        id: 'user123',
        location: 'POINT(9.1900 45.4642)',
        last_seen: '2024-01-01T10:00:00Z'
      }
      mockSupabase.single.mockResolvedValue({ data: mockResult, error: null })

      const result = await userRepository.updateLocation('user123', location)

      expect(mockSupabase.update).toHaveBeenCalledWith({
        location: 'POINT(9.1900 45.4642)',
        last_seen: expect.any(String)
      })
      expect(result).toEqual(mockResult)
    })
  })

  describe('getDashboardStats', () => {
    it('should get dashboard statistics for a user', async () => {
      const mockUser = global.createTestUser({
        average_rating: 4.5,
        total_reviews: 25,
        total_earnings: 1500.00
      })
      const mockBookings = [
        { status: 'COMPLETED', total_amount: '50.00' },
        { status: 'PENDING', total_amount: '30.00' },
        { status: 'COMPLETED', total_amount: '75.00' }
      ]

      mockSupabase.single.mockResolvedValue({ data: mockUser, error: null })
      mockSupabase.select.mockResolvedValue({ data: mockBookings, error: null })

      const result = await userRepository.getDashboardStats('user123')

      expect(result).toEqual({
        totalBookings: 3,
        activeBookings: 1,
        completedBookings: 2,
        averageRating: 4.5,
        totalReviews: 25,
        totalEarnings: 1500.00
      })
    })

    it('should calculate total spent for clients', async () => {
      const mockUser = global.createTestUser({ type: 'ESERCENTE' })
      const mockBookings = [
        { status: 'COMPLETED', total_amount: '50.00' },
        { status: 'COMPLETED', total_amount: '75.00' }
      ]

      mockSupabase.single.mockResolvedValue({ data: mockUser, error: null })
      mockSupabase.select.mockResolvedValue({ data: mockBookings, error: null })

      const result = await userRepository.getDashboardStats('user123')

      expect(result.totalSpent).toBe(125.00)
      expect(result.totalEarnings).toBeUndefined()
    })

    it('should handle missing user', async () => {
      mockSupabase.single.mockResolvedValue({ data: null, error: null })

      await expect(userRepository.getDashboardStats('nonexistent')).rejects.toThrow('User not found')
    })
  })

  describe('searchUsers', () => {
    it('should search users with text query', async () => {
      const mockUsers = [
        global.createTestUser({ first_name: 'Marco', last_name: 'Rossi' }),
        global.createTestUser({ first_name: 'Maria', last_name: 'Bianchi' })
      ]
      mockSupabase.select.mockResolvedValue({ data: mockUsers, error: null })

      const result = await userRepository.searchUsers('Mar')

      expect(mockSupabase.or).toHaveBeenCalledWith(expect.stringContaining('Mar'))
      expect(mockSupabase.order).toHaveBeenCalledWith('average_rating', { ascending: false })
      expect(result).toEqual(mockUsers)
    })

    it('should apply additional filters', async () => {
      const mockUsers = [global.createTestUser({ type: 'RIDER' })]
      mockSupabase.select.mockResolvedValue({ data: mockUsers, error: null })

      await userRepository.searchUsers('test', { type: 'RIDER' })

      expect(mockSupabase.eq).toHaveBeenCalledWith('type', 'RIDER')
    })

    it('should limit results', async () => {
      mockSupabase.select.mockResolvedValue({ data: [], error: null })

      await userRepository.searchUsers('test', { limit: 5 })

      expect(mockSupabase.limit).toHaveBeenCalledWith(5)
    })
  })

  describe('getVerificationStatus', () => {
    it('should get user verification status', async () => {
      const mockUser = {
        id: 'user123',
        email_verified: true,
        phone_verified: true,
        document_verified: false,
        background_check: false,
        profile_complete: true,
        account_status: 'ACTIVE'
      }
      mockSupabase.single.mockResolvedValue({ data: mockUser, error: null })

      const result = await userRepository.getVerificationStatus('user123')

      expect(result).toEqual({
        emailVerified: true,
        phoneVerified: true,
        documentVerified: false,
        backgroundCheck: false,
        profileComplete: true,
        accountStatus: 'ACTIVE',
        overallVerified: false // Not fully verified
      })
    })

    it('should calculate overall verification status', async () => {
      const mockUser = {
        email_verified: true,
        phone_verified: true,
        document_verified: true,
        profile_complete: true
      }
      mockSupabase.single.mockResolvedValue({ data: mockUser, error: null })

      const result = await userRepository.getVerificationStatus('user123')

      expect(result.overallVerified).toBe(true)
    })
  })

  describe('updateVerificationStatus', () => {
    it('should update allowed verification fields', async () => {
      const verificationData = {
        email_verified: true,
        document_verified: true,
        invalid_field: 'should be ignored'
      }
      const mockResult = { id: 'user123', email_verified: true }
      mockSupabase.single.mockResolvedValue({ data: mockResult, error: null })

      await userRepository.updateVerificationStatus('user123', verificationData)

      expect(mockSupabase.update).toHaveBeenCalledWith({
        email_verified: true,
        document_verified: true
        // invalid_field should not be included
      })
    })
  })

  describe('getUsersByType', () => {
    it('should get users by type with pagination', async () => {
      const mockUsers = [
        global.createTestUser({ type: 'RIDER' }),
        global.createTestUser({ type: 'RIDER' })
      ]
      mockSupabase.select.mockResolvedValueOnce({ data: mockUsers, error: null })
      mockSupabase.select.mockResolvedValueOnce({ count: 10, error: null })

      const result = await userRepository.getUsersByType('RIDER', { page: 1, limit: 2 })

      expect(result).toEqual({
        users: mockUsers,
        pagination: {
          page: 1,
          limit: 2,
          total: 10,
          pages: 5
        }
      })
    })
  })
})
