import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

dotenv.config()

const supabaseUrl = process.env.SUPABASE_URL
const supabaseKey = process.env.SUPABASE_ANON_KEY

const supabase = createClient(supabaseUrl, supabaseKey)

async function createTables() {
  console.log('🏗️ Creating Supabase tables...')
  
  try {
    // Create users table
    const { error: usersError } = await supabase.rpc('exec', {
      sql: `
        -- Create enum types
        DO $$ BEGIN
          CREATE TYPE user_type AS ENUM ('ESERCENTE', 'RIDER');
        EXCEPTION
          WHEN duplicate_object THEN null;
        END $$;
        
        DO $$ BEGIN
          CREATE TYPE vehicle_type AS ENUM ('BICI', 'E_BIKE', 'SCOOTER', 'MOTO', 'AUTO', 'FURGONE');
        EXCEPTION
          WHEN duplicate_object THEN null;
        END $$;
        
        DO $$ BEGIN
          CREATE TYPE booking_status AS ENUM ('PENDING', 'CONFIRMED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED');
        EXCEPTION
          WHEN duplicate_object THEN null;
        END $$;
        
        DO $$ BEGIN
          CREATE TYPE payment_status AS ENUM ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'REFUNDED');
        EXCEPTION
          WHEN duplicate_object THEN null;
        END $$;
        
        -- Create users table
        CREATE TABLE IF NOT EXISTS users (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          clerk_id TEXT UNIQUE NOT NULL,
          email TEXT UNIQUE NOT NULL,
          first_name TEXT,
          last_name TEXT,
          name TEXT,
          type user_type NOT NULL,
          profile_complete BOOLEAN DEFAULT FALSE,
          phone TEXT,
          address TEXT,
          city TEXT,
          province TEXT,
          postal_code TEXT,
          hourly_rate DECIMAL(10,2),
          vehicle_type vehicle_type,
          vehicle_model TEXT,
          vehicle_plate TEXT,
          description TEXT,
          is_available BOOLEAN DEFAULT TRUE,
          stripe_account_id TEXT UNIQUE,
          stripe_customer_id TEXT UNIQUE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Create bookings table
        CREATE TABLE IF NOT EXISTS bookings (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          rider_id UUID REFERENCES users(id) ON DELETE CASCADE,
          client_id UUID REFERENCES users(id) ON DELETE CASCADE,
          date TIMESTAMP WITH TIME ZONE NOT NULL,
          duration INTEGER NOT NULL,
          address TEXT NOT NULL,
          instructions TEXT,
          hourly_rate DECIMAL(10,2) NOT NULL,
          subtotal DECIMAL(10,2) NOT NULL,
          platform_fee DECIMAL(10,2) NOT NULL,
          total_amount DECIMAL(10,2) NOT NULL,
          status booking_status DEFAULT 'PENDING',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Create payments table
        CREATE TABLE IF NOT EXISTS payments (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          booking_id UUID REFERENCES bookings(id) ON DELETE CASCADE,
          user_id UUID REFERENCES users(id) ON DELETE CASCADE,
          stripe_payment_intent_id TEXT UNIQUE NOT NULL,
          amount DECIMAL(10,2) NOT NULL,
          platform_fee DECIMAL(10,2) NOT NULL,
          rider_amount DECIMAL(10,2) NOT NULL,
          currency TEXT DEFAULT 'eur',
          status payment_status DEFAULT 'PENDING',
          metadata JSONB,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Create reviews table
        CREATE TABLE IF NOT EXISTS reviews (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          booking_id UUID REFERENCES bookings(id) ON DELETE CASCADE,
          reviewer_id UUID REFERENCES users(id) ON DELETE CASCADE,
          reviewee_id UUID REFERENCES users(id) ON DELETE CASCADE,
          rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
          comment TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Create availability table
        CREATE TABLE IF NOT EXISTS availability (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          rider_id UUID REFERENCES users(id) ON DELETE CASCADE,
          day_of_week INTEGER NOT NULL CHECK (day_of_week >= 0 AND day_of_week <= 6),
          start_time TIME NOT NULL,
          end_time TIME NOT NULL,
          is_active BOOLEAN DEFAULT TRUE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          UNIQUE(rider_id, day_of_week, start_time, end_time)
        );
        
        -- Create indexes for better performance
        CREATE INDEX IF NOT EXISTS idx_users_clerk_id ON users(clerk_id);
        CREATE INDEX IF NOT EXISTS idx_users_type ON users(type);
        CREATE INDEX IF NOT EXISTS idx_users_available ON users(is_available);
        CREATE INDEX IF NOT EXISTS idx_bookings_rider ON bookings(rider_id);
        CREATE INDEX IF NOT EXISTS idx_bookings_client ON bookings(client_id);
        CREATE INDEX IF NOT EXISTS idx_bookings_status ON bookings(status);
        CREATE INDEX IF NOT EXISTS idx_bookings_date ON bookings(date);
        CREATE INDEX IF NOT EXISTS idx_reviews_reviewee ON reviews(reviewee_id);
        CREATE INDEX IF NOT EXISTS idx_availability_rider ON availability(rider_id);
        
        -- Enable Row Level Security
        ALTER TABLE users ENABLE ROW LEVEL SECURITY;
        ALTER TABLE bookings ENABLE ROW LEVEL SECURITY;
        ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
        ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;
        ALTER TABLE availability ENABLE ROW LEVEL SECURITY;
      `
    })
    
    if (usersError) {
      console.error('❌ Error creating tables:', usersError)
      return false
    }
    
    console.log('✅ Tables created successfully!')
    return true
    
  } catch (error) {
    console.error('❌ Unexpected error:', error)
    return false
  }
}

// Run the creation
createTables()
  .then(success => {
    if (success) {
      console.log('🎉 Database setup completed!')
    } else {
      console.log('❌ Database setup failed!')
    }
  })
  .catch(error => {
    console.error('❌ Setup failed:', error)
  })
