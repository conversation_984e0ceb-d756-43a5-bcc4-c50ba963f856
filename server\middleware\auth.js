import { createClerkClient } from '@clerk/clerk-sdk-node'

// Initialize Clerk client
const clerkClient = createClerkClient({
  secretKey: process.env.CLERK_SECRET_KEY,
})

/**
 * Middleware to authenticate requests using Clerk JWT tokens
 */
export const authenticateClerk = async (req, res, next) => {
  try {
    // Get token from Authorization header
    const authHeader = req.headers.authorization
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: 'Token di autenticazione mancante'
      })
    }

    const token = authHeader.substring(7) // Remove 'Bearer ' prefix

    // Verify token with Clerk
    try {
      const payload = await clerkClient.verifyToken(token)
      
      // Add user info to request object
      req.auth = {
        userId: payload.sub,
        sessionId: payload.sid,
        claims: payload
      }
      
      next()
    } catch (clerkError) {
      console.error('Clerk token verification failed:', clerkError.message)
      return res.status(401).json({
        success: false,
        message: 'Token di autenticazione non valido'
      })
    }

  } catch (error) {
    console.error('Authentication middleware error:', error)
    return res.status(500).json({
      success: false,
      message: 'Errore interno del server'
    })
  }
}

/**
 * Middleware to require specific user types
 */
export const requireUserType = (allowedTypes) => {
  return async (req, res, next) => {
    try {
      if (!req.auth || !req.auth.userId) {
        return res.status(401).json({
          success: false,
          message: 'Autenticazione richiesta'
        })
      }

      // Get user from Clerk
      const user = await clerkClient.users.getUser(req.auth.userId)
      const userType = user.unsafeMetadata?.userType

      if (!userType || !allowedTypes.includes(userType)) {
        return res.status(403).json({
          success: false,
          message: 'Accesso non autorizzato per questo tipo di utente'
        })
      }

      // Add user type to request
      req.userType = userType
      req.user = user
      
      next()
    } catch (error) {
      console.error('User type validation error:', error)
      return res.status(500).json({
        success: false,
        message: 'Errore nella validazione del tipo utente'
      })
    }
  }
}

/**
 * Middleware for optional authentication (doesn't fail if no token)
 */
export const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      // No token provided, continue without auth
      req.auth = null
      return next()
    }

    const token = authHeader.substring(7)

    try {
      const payload = await clerkClient.verifyToken(token)
      req.auth = {
        userId: payload.sub,
        sessionId: payload.sid,
        claims: payload
      }
    } catch (clerkError) {
      // Invalid token, but don't fail the request
      console.warn('Optional auth failed:', clerkError.message)
      req.auth = null
    }

    next()
  } catch (error) {
    console.error('Optional auth middleware error:', error)
    req.auth = null
    next()
  }
}

/**
 * Rate limiting middleware (basic implementation)
 */
const requestCounts = new Map()

export const rateLimit = (maxRequests = 100, windowMs = 15 * 60 * 1000) => {
  return (req, res, next) => {
    const clientId = req.ip || req.connection.remoteAddress
    const now = Date.now()
    const windowStart = now - windowMs

    // Clean old entries
    for (const [key, data] of requestCounts.entries()) {
      if (data.timestamp < windowStart) {
        requestCounts.delete(key)
      }
    }

    // Check current client
    const clientData = requestCounts.get(clientId) || { count: 0, timestamp: now }
    
    if (clientData.timestamp < windowStart) {
      // Reset count for new window
      clientData.count = 1
      clientData.timestamp = now
    } else {
      clientData.count++
    }

    requestCounts.set(clientId, clientData)

    if (clientData.count > maxRequests) {
      return res.status(429).json({
        success: false,
        message: 'Troppi tentativi. Riprova più tardi.',
        retryAfter: Math.ceil(windowMs / 1000)
      })
    }

    // Add rate limit headers
    res.set({
      'X-RateLimit-Limit': maxRequests,
      'X-RateLimit-Remaining': Math.max(0, maxRequests - clientData.count),
      'X-RateLimit-Reset': new Date(clientData.timestamp + windowMs).toISOString()
    })

    next()
  }
}

/**
 * Input validation middleware
 */
export const validateInput = (schema) => {
  return (req, res, next) => {
    try {
      // Basic validation - in a real app you'd use a library like Joi or Yup
      const errors = []

      if (schema.required) {
        for (const field of schema.required) {
          if (!req.body[field]) {
            errors.push(`Campo '${field}' è obbligatorio`)
          }
        }
      }

      if (schema.email) {
        for (const field of schema.email) {
          if (req.body[field] && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(req.body[field])) {
            errors.push(`Campo '${field}' deve essere un email valido`)
          }
        }
      }

      if (errors.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'Errori di validazione',
          errors
        })
      }

      next()
    } catch (error) {
      console.error('Validation middleware error:', error)
      return res.status(500).json({
        success: false,
        message: 'Errore nella validazione dei dati'
      })
    }
  }
}

/**
 * Require admin role middleware
 */
export const requireAdmin = (req, res, next) => {
  try {
    const user = req.auth?.user

    if (!user) {
      return res.status(401).json({
        error: 'Authentication required',
        message: 'You must be logged in to access this resource'
      })
    }

    const userRole = user.publicMetadata?.role || user.publicMetadata?.userType

    if (userRole !== 'admin' && userRole !== 'ADMIN') {
      return res.status(403).json({
        error: 'Insufficient permissions',
        message: 'Admin access required'
      })
    }

    next()
  } catch (error) {
    console.error('Admin check error:', error)
    res.status(500).json({
      error: 'Authorization error',
      message: 'Failed to verify admin permissions'
    })
  }
}
