
import { createClient } from '@supabase/supabase-js'

// Validate required environment variables
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl) {
  throw new Error('VITE_SUPABASE_URL environment variable is required')
}

if (!supabaseAnonKey) {
  throw new Error('VITE_SUPABASE_ANON_KEY environment variable is required')
}

// Validate URL format
if (!supabaseUrl.startsWith('https://') || !supabaseUrl.includes('.supabase.co')) {
  throw new Error('Invalid VITE_SUPABASE_URL format. Expected: https://your-project.supabase.co')
}

// Validate key format (should be a JWT)
if (!supabaseAnonKey.startsWith('eyJ')) {
  throw new Error('Invalid VITE_SUPABASE_ANON_KEY format. Expected a valid JWT token')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Test connection function
export const testSupabaseConnection = async () => {
  try {
    console.log('🧪 Testing Supabase connection...')
    console.log('📍 URL:', supabaseUrl)
    console.log('🔑 Key:', supabaseAnonKey ? 'Present' : 'Missing')

    // Test basic connection with a simple query
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1)

    if (error) {
      console.log('❌ Connection test failed:', error.message)
      return { success: false, error: error.message }
    } else {
      console.log('✅ Supabase connection successful!')
      console.log('📊 Query result:', data)
      return { success: true, data }
    }
  } catch (error: any) {
    console.error('❌ Unexpected error:', error)
    return { success: false, error: error.message || 'Unknown error' }
  }
}
