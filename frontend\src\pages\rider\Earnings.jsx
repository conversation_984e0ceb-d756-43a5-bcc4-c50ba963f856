/**
 * Rider Earnings Page
 * View earnings, statistics and payment history
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { useState } from 'react'
import { motion } from 'framer-motion'
import {
  CurrencyEuroIcon,
  ChartBarIcon,
  CalendarIcon,
  ArrowTrendingUpIcon,
  BanknotesIcon,
  ClockIcon
} from '@heroicons/react/24/outline'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts'

import Card, { CardHeader, CardTitle, CardContent } from '@components/ui/Card'
import Button from '@components/ui/Button'
import Header from '@components/layout/Header'

const mockEarningsData = {
  summary: {
    totalEarnings: 1872.50,
    thisMonth: 456.75,
    thisWeek: 127.50,
    today: 25.00,
    pendingPayments: 89.25,
    averageHourlyRate: 11.85
  },
  chartData: [
    { month: 'Gen', earnings: 234.50, hours: 20 },
    { month: 'Feb', earnings: 345.75, hours: 28 },
    { month: 'Mar', earnings: 456.25, hours: 35 },
    { month: 'Apr', earnings: 389.50, hours: 32 },
    { month: 'Mag', earnings: 567.80, hours: 45 },
    { month: 'Giu', earnings: 456.75, hours: 38 }
  ],
  recentPayments: [
    {
      id: 1,
      date: '2024-12-20',
      amount: 127.50,
      description: 'Pagamento settimanale',
      status: 'completed',
      shifts: 8
    },
    {
      id: 2,
      date: '2024-12-13',
      amount: 156.75,
      description: 'Pagamento settimanale',
      status: 'completed',
      shifts: 10
    },
    {
      id: 3,
      date: '2024-12-06',
      amount: 89.25,
      description: 'Pagamento settimanale',
      status: 'pending',
      shifts: 6
    }
  ]
}

const RiderEarnings = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('month')

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('it-IT', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('it-IT', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    })
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header 
        title="I Miei Guadagni"
        subtitle="Monitora i tuoi ricavi e pagamenti"
        actions={
          <div className="flex space-x-2">
            <Button variant="outline" size="sm">
              Esporta Report
            </Button>
            <Button variant="rider" size="sm">
              Richiedi Pagamento
            </Button>
          </div>
        }
      />

      <div className="p-6 space-y-6">
        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
          >
            <Card className="metric-card card-hover">
              <div className="flex items-center justify-between">
                <div>
                  <p className="metric-label">Guadagni Totali</p>
                  <p className="metric-value">{formatCurrency(mockEarningsData.summary.totalEarnings)}</p>
                  <p className="metric-change metric-change-positive">
                    +12.5% dal mese scorso
                  </p>
                </div>
                <div className="p-3 bg-green-100 rounded-full">
                  <CurrencyEuroIcon className="w-6 h-6 text-green-600" />
                </div>
              </div>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Card className="metric-card card-hover">
              <div className="flex items-center justify-between">
                <div>
                  <p className="metric-label">Questo Mese</p>
                  <p className="metric-value">{formatCurrency(mockEarningsData.summary.thisMonth)}</p>
                  <p className="metric-change metric-change-positive">
                    +8.3% dalla media
                  </p>
                </div>
                <div className="p-3 bg-blue-100 rounded-full">
                  <CalendarIcon className="w-6 h-6 text-blue-600" />
                </div>
              </div>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card className="metric-card card-hover">
              <div className="flex items-center justify-between">
                <div>
                  <p className="metric-label">Tariffa Media</p>
                  <p className="metric-value">{formatCurrency(mockEarningsData.summary.averageHourlyRate)}/h</p>
                  <p className="metric-change metric-change-positive">
                    Sopra la media
                  </p>
                </div>
                <div className="p-3 bg-purple-100 rounded-full">
                  <ArrowTrendingUpIcon className="w-6 h-6 text-purple-600" />
                </div>
              </div>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <Card className="metric-card card-hover">
              <div className="flex items-center justify-between">
                <div>
                  <p className="metric-label">In Attesa</p>
                  <p className="metric-value">{formatCurrency(mockEarningsData.summary.pendingPayments)}</p>
                  <p className="metric-change">
                    Pagamento previsto venerdì
                  </p>
                </div>
                <div className="p-3 bg-yellow-100 rounded-full">
                  <ClockIcon className="w-6 h-6 text-yellow-600" />
                </div>
              </div>
            </Card>
          </motion.div>
        </div>

        {/* Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Earnings Chart */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.5 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <ChartBarIcon className="w-5 h-5 text-blue-600" />
                  <span>Andamento Guadagni</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={mockEarningsData.chartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip 
                      formatter={(value) => [formatCurrency(value), 'Guadagni']}
                      labelStyle={{ color: '#374151' }}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="earnings" 
                      stroke="#3B82F6" 
                      strokeWidth={3}
                      dot={{ fill: '#3B82F6', strokeWidth: 2, r: 4 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </motion.div>

          {/* Hours Chart */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.6 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <ClockIcon className="w-5 h-5 text-green-600" />
                  <span>Ore Lavorate</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={mockEarningsData.chartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip 
                      formatter={(value) => [value + 'h', 'Ore']}
                      labelStyle={{ color: '#374151' }}
                    />
                    <Bar 
                      dataKey="hours" 
                      fill="#10B981"
                      radius={[4, 4, 0, 0]}
                    />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Recent Payments */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
        >
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center space-x-2">
                  <BanknotesIcon className="w-5 h-5 text-green-600" />
                  <span>Pagamenti Recenti</span>
                </CardTitle>
                <Button variant="outline" size="sm">
                  Vedi Tutti
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockEarningsData.recentPayments.map((payment, index) => (
                  <motion.div
                    key={payment.id}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.8 + index * 0.1 }}
                    className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                  >
                    <div className="flex items-center space-x-4">
                      <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                        payment.status === 'completed' ? 'bg-green-100' : 'bg-yellow-100'
                      }`}>
                        <BanknotesIcon className={`w-5 h-5 ${
                          payment.status === 'completed' ? 'text-green-600' : 'text-yellow-600'
                        }`} />
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">{payment.description}</h4>
                        <div className="flex items-center space-x-4 text-sm text-gray-600">
                          <span>{formatDate(payment.date)}</span>
                          <span>{payment.shifts} turni</span>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-gray-900">{formatCurrency(payment.amount)}</p>
                      <span className={`status-badge ${
                        payment.status === 'completed' ? 'status-success' : 'status-warning'
                      }`}>
                        {payment.status === 'completed' ? 'Pagato' : 'In attesa'}
                      </span>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}

export default RiderEarnings
