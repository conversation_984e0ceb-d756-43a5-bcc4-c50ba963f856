/**
 * Global Test Setup
 * Configures the testing environment for all tests
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { beforeAll, afterAll, beforeEach, afterEach } from 'vitest'
import dotenv from 'dotenv'

// Load test environment variables
dotenv.config({ path: '.env.test' })

// Global test configuration
global.TEST_CONFIG = {
  API_BASE_URL: 'http://localhost:5001/api',
  TEST_TIMEOUT: 30000,
  DB_CLEANUP_ENABLED: true,
  MOCK_EXTERNAL_SERVICES: true
}

// Global setup - runs once before all tests
beforeAll(async () => {
  console.log('🧪 Starting global test setup...')
  
  // Set test environment
  process.env.NODE_ENV = 'test'
  
  // Initialize test database
  await initializeTestDatabase()
  
  // Setup mock services
  await setupMockServices()
  
  console.log('✅ Global test setup completed')
})

// Global teardown - runs once after all tests
afterAll(async () => {
  console.log('🧹 Starting global test teardown...')
  
  // Cleanup test database
  await cleanupTestDatabase()
  
  // Cleanup mock services
  await cleanupMockServices()
  
  console.log('✅ Global test teardown completed')
})

// Setup before each test
beforeEach(async () => {
  // Reset mocks
  if (global.mockReset) {
    global.mockReset()
  }
  
  // Clear any cached data
  if (global.clearCache) {
    global.clearCache()
  }
})

// Cleanup after each test
afterEach(async () => {
  // Cleanup any test-specific data
  if (global.TEST_CONFIG.DB_CLEANUP_ENABLED) {
    await cleanupTestData()
  }
})

/**
 * Initialize test database
 */
async function initializeTestDatabase() {
  try {
    console.log('📊 Initializing test database...')
    
    // Note: In a real scenario, you might want to:
    // 1. Create a separate test database
    // 2. Run migrations
    // 3. Seed with test data
    
    // For now, we'll use the existing database with test data
    console.log('✅ Test database initialized')
  } catch (error) {
    console.error('❌ Failed to initialize test database:', error)
    throw error
  }
}

/**
 * Setup mock services
 */
async function setupMockServices() {
  try {
    console.log('🎭 Setting up mock services...')
    
    // Mock Clerk authentication
    global.mockClerk = {
      verifyToken: async (token) => {
        if (token === 'valid_test_token') {
          return {
            sub: 'test_user_id',
            sid: 'test_session_id',
            email: '<EMAIL>'
          }
        }
        throw new Error('Invalid token')
      },
      
      users: {
        getUser: async (userId) => {
          if (userId === 'test_user_id') {
            return {
              id: 'test_user_id',
              emailAddresses: [{ emailAddress: '<EMAIL>' }],
              unsafeMetadata: { userType: 'RIDER' }
            }
          }
          throw new Error('User not found')
        }
      }
    }
    
    // Mock Stripe
    global.mockStripe = {
      paymentIntents: {
        create: async (params) => ({
          id: 'pi_test_' + Date.now(),
          client_secret: 'pi_test_client_secret',
          status: 'requires_payment_method',
          amount: params.amount
        }),
        
        confirm: async (id) => ({
          id,
          status: 'succeeded'
        })
      }
    }
    
    // Mock Supabase
    global.mockSupabase = {
      from: (table) => ({
        select: () => ({
          eq: () => ({
            single: async () => ({ data: null, error: null })
          })
        }),
        insert: () => ({
          select: () => ({
            single: async () => ({ data: { id: 'test_id' }, error: null })
          })
        }),
        update: () => ({
          eq: () => ({
            select: () => ({
              single: async () => ({ data: { id: 'test_id' }, error: null })
            })
          })
        })
      })
    }
    
    console.log('✅ Mock services setup completed')
  } catch (error) {
    console.error('❌ Failed to setup mock services:', error)
    throw error
  }
}

/**
 * Cleanup test database
 */
async function cleanupTestDatabase() {
  try {
    console.log('🧹 Cleaning up test database...')
    
    // In a real scenario, you might want to:
    // 1. Drop test database
    // 2. Or clean up test data
    
    console.log('✅ Test database cleanup completed')
  } catch (error) {
    console.error('❌ Failed to cleanup test database:', error)
    // Don't throw here to avoid masking test failures
  }
}

/**
 * Cleanup mock services
 */
async function cleanupMockServices() {
  try {
    console.log('🧹 Cleaning up mock services...')
    
    // Reset global mocks
    delete global.mockClerk
    delete global.mockStripe
    delete global.mockSupabase
    
    console.log('✅ Mock services cleanup completed')
  } catch (error) {
    console.error('❌ Failed to cleanup mock services:', error)
    // Don't throw here to avoid masking test failures
  }
}

/**
 * Cleanup test data after each test
 */
async function cleanupTestData() {
  try {
    // Clean up any test-specific data
    // This could include:
    // - Removing test users
    // - Cleaning up test bookings
    // - Resetting test state
    
    // For now, this is a placeholder
  } catch (error) {
    console.error('❌ Failed to cleanup test data:', error)
    // Don't throw here to avoid masking test failures
  }
}

/**
 * Utility function to create test user
 */
global.createTestUser = (overrides = {}) => ({
  id: 'test_user_id',
  clerk_id: 'test_clerk_id',
  email: '<EMAIL>',
  type: 'RIDER',
  first_name: 'Test',
  last_name: 'User',
  display_name: 'Test User',
  profile_complete: true,
  email_verified: true,
  account_status: 'ACTIVE',
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  ...overrides
})

/**
 * Utility function to create test booking
 */
global.createTestBooking = (overrides = {}) => ({
  id: 'test_booking_id',
  booking_number: 'BMR24TEST001',
  client_id: 'test_client_id',
  rider_id: 'test_rider_id',
  scheduled_date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
  duration: 120,
  pickup_address: 'Test Pickup Address',
  delivery_address: 'Test Delivery Address',
  base_rate: 25.00,
  subtotal: 50.00,
  platform_fee: 7.50,
  total_amount: 57.50,
  status: 'PENDING',
  payment_status: 'PENDING',
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  ...overrides
})

/**
 * Utility function to create test auth headers
 */
global.createAuthHeaders = (token = 'valid_test_token') => ({
  'Authorization': `Bearer ${token}`,
  'Content-Type': 'application/json'
})

/**
 * Utility function to wait for async operations
 */
global.waitFor = (ms) => new Promise(resolve => setTimeout(resolve, ms))

/**
 * Utility function to generate random test data
 */
global.generateTestData = {
  email: () => `test${Date.now()}@example.com`,
  phone: () => `+39 33${Math.floor(Math.random() * 100000000).toString().padStart(8, '0')}`,
  uuid: () => 'test_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
  bookingNumber: () => 'BMR24' + Math.random().toString(36).substr(2, 8).toUpperCase()
}
