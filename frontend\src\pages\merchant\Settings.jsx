/**
 * Merchant Settings Page
 * Manage merchant settings and preferences
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { useState } from 'react'
import { motion } from 'framer-motion'
import { CogIcon, BuildingStorefrontIcon, BellIcon, CreditCardIcon } from '@heroicons/react/24/outline'

import Card, { CardHeader, CardTitle, CardContent } from '@components/ui/Card'
import Button from '@components/ui/Button'
import Input from '@components/ui/Input'
import Header from '@components/layout/Header'

const MerchantSettings = () => {
  const [settings, setSettings] = useState({
    businessName: 'Pizzeria Da Mario',
    businessType: 'restaurant',
    address: 'Via Roma 123, Milano',
    phone: '+39 02 1234567',
    email: '<EMAIL>',
    notifications: {
      email: true,
      sms: false,
      push: true
    }
  })

  return (
    <div className="min-h-screen bg-gray-50">
      <Header 
        title="Impostazioni"
        subtitle="Gestisci le impostazioni del tuo account"
      />

      <div className="p-6 space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Business Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <BuildingStorefrontIcon className="w-5 h-5 text-blue-600" />
                <span>Informazioni Attività</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Input
                  label="Nome Attività"
                  value={settings.businessName}
                  onChange={(e) => setSettings({...settings, businessName: e.target.value})}
                />
                <Input
                  label="Indirizzo"
                  value={settings.address}
                  onChange={(e) => setSettings({...settings, address: e.target.value})}
                />
                <Input
                  label="Telefono"
                  value={settings.phone}
                  onChange={(e) => setSettings({...settings, phone: e.target.value})}
                />
                <Input
                  label="Email"
                  type="email"
                  value={settings.email}
                  onChange={(e) => setSettings({...settings, email: e.target.value})}
                />
              </div>
            </CardContent>
          </Card>

          {/* Notifications */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <BellIcon className="w-5 h-5 text-green-600" />
                <span>Notifiche</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">Notifiche Email</span>
                  <input
                    type="checkbox"
                    checked={settings.notifications.email}
                    onChange={(e) => setSettings({
                      ...settings,
                      notifications: {...settings.notifications, email: e.target.checked}
                    })}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">Notifiche SMS</span>
                  <input
                    type="checkbox"
                    checked={settings.notifications.sms}
                    onChange={(e) => setSettings({
                      ...settings,
                      notifications: {...settings.notifications, sms: e.target.checked}
                    })}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">Notifiche Push</span>
                  <input
                    type="checkbox"
                    checked={settings.notifications.push}
                    onChange={(e) => setSettings({
                      ...settings,
                      notifications: {...settings.notifications, push: e.target.checked}
                    })}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="flex justify-end">
          <Button variant="merchant">
            Salva Impostazioni
          </Button>
        </div>
      </div>
    </div>
  )
}

export default MerchantSettings
