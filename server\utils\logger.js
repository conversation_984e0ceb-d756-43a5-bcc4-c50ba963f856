/**
 * Enterprise Logging System
 * Structured logging with multiple transports and security focus
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import winston from 'winston'
import DailyRotateFile from 'winston-daily-rotate-file'
import { getEnv } from './env-validator.js'

// ============================================================================
// LOG LEVELS AND CONFIGURATION
// ============================================================================

const LOG_LEVELS = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4
}

const LOG_COLORS = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'blue'
}

winston.addColors(LOG_COLORS)

// ============================================================================
// CUSTOM FORMATS
// ============================================================================

/**
 * Custom format for structured logging
 */
const structuredFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    const logEntry = {
      timestamp,
      level: level.toUpperCase(),
      message,
      service: 'bemyrider-api',
      environment: getEnv('NODE_ENV', 'development'),
      version: process.env.npm_package_version || '1.0.0',
      ...meta
    }

    // Add correlation ID if available
    if (meta.correlationId) {
      logEntry.correlationId = meta.correlationId
    }

    // Add user context if available
    if (meta.userId) {
      logEntry.userId = meta.userId
    }

    // Add request context if available
    if (meta.requestId) {
      logEntry.requestId = meta.requestId
    }

    return JSON.stringify(logEntry)
  })
)

/**
 * Console format for development
 */
const consoleFormat = winston.format.combine(
  winston.format.timestamp({ format: 'HH:mm:ss' }),
  winston.format.colorize({ all: true }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let logMessage = `${timestamp} [${level}]: ${message}`
    
    if (Object.keys(meta).length > 0) {
      logMessage += ` ${JSON.stringify(meta, null, 2)}`
    }
    
    return logMessage
  })
)

// ============================================================================
// TRANSPORTS CONFIGURATION
// ============================================================================

/**
 * Create file transport with rotation
 */
const createFileTransport = (filename, level = 'info') => {
  return new DailyRotateFile({
    filename: `logs/${filename}-%DATE%.log`,
    datePattern: 'YYYY-MM-DD',
    maxSize: '20m',
    maxFiles: '14d',
    level,
    format: structuredFormat,
    auditFile: `logs/.${filename}-audit.json`,
    zippedArchive: true
  })
}

/**
 * Configure transports based on environment
 */
const getTransports = () => {
  const transports = []

  // Console transport (always enabled in development)
  if (getEnv('NODE_ENV') === 'development') {
    transports.push(
      new winston.transports.Console({
        level: 'debug',
        format: consoleFormat
      })
    )
  } else {
    transports.push(
      new winston.transports.Console({
        level: 'info',
        format: structuredFormat
      })
    )
  }

  // File transports (production and staging)
  if (getEnv('NODE_ENV') !== 'test') {
    // General application logs
    transports.push(createFileTransport('app', 'info'))
    
    // Error logs
    transports.push(createFileTransport('error', 'error'))
    
    // Security logs
    transports.push(createFileTransport('security', 'warn'))
    
    // HTTP access logs
    transports.push(createFileTransport('access', 'http'))
  }

  return transports
}

// ============================================================================
// LOGGER INSTANCE
// ============================================================================

/**
 * Main logger instance
 */
const logger = winston.createLogger({
  levels: LOG_LEVELS,
  level: getEnv('LOG_LEVEL', 'info'),
  format: structuredFormat,
  defaultMeta: {
    service: 'bemyrider-api',
    environment: getEnv('NODE_ENV', 'development')
  },
  transports: getTransports(),
  exitOnError: false
})

// ============================================================================
// SPECIALIZED LOGGERS
// ============================================================================

/**
 * Security logger for security events
 */
const securityLogger = winston.createLogger({
  levels: LOG_LEVELS,
  level: 'warn',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json(),
    winston.format.printf(({ timestamp, level, message, ...meta }) => {
      return JSON.stringify({
        timestamp,
        level: level.toUpperCase(),
        type: 'SECURITY_EVENT',
        message,
        ...meta
      })
    })
  ),
  transports: [
    createFileTransport('security', 'warn'),
    new winston.transports.Console({
      level: 'warn',
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ]
})

/**
 * Audit logger for compliance and audit trails
 */
const auditLogger = winston.createLogger({
  levels: LOG_LEVELS,
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json(),
    winston.format.printf(({ timestamp, level, message, ...meta }) => {
      return JSON.stringify({
        timestamp,
        level: level.toUpperCase(),
        type: 'AUDIT_EVENT',
        message,
        ...meta
      })
    })
  ),
  transports: [
    createFileTransport('audit', 'info')
  ]
})

/**
 * Performance logger for monitoring
 */
const performanceLogger = winston.createLogger({
  levels: LOG_LEVELS,
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json(),
    winston.format.printf(({ timestamp, level, message, ...meta }) => {
      return JSON.stringify({
        timestamp,
        level: level.toUpperCase(),
        type: 'PERFORMANCE_EVENT',
        message,
        ...meta
      })
    })
  ),
  transports: [
    createFileTransport('performance', 'info')
  ]
})

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

/**
 * Log with correlation ID
 */
const logWithCorrelation = (level, message, meta = {}, correlationId) => {
  logger.log(level, message, {
    ...meta,
    correlationId: correlationId || generateCorrelationId()
  })
}

/**
 * Generate correlation ID
 */
const generateCorrelationId = () => {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

/**
 * Log API request
 */
const logAPIRequest = (req, res, responseTime) => {
  const logData = {
    method: req.method,
    url: req.originalUrl,
    statusCode: res.statusCode,
    responseTime: `${responseTime}ms`,
    userAgent: req.headers['user-agent'],
    ip: req.ip || req.connection.remoteAddress,
    userId: req.auth?.userId || null,
    correlationId: req.correlationId
  }

  if (res.statusCode >= 400) {
    logger.warn('API Request Failed', logData)
  } else {
    logger.http('API Request', logData)
  }
}

/**
 * Log security event
 */
const logSecurityEvent = (eventType, details, req = null) => {
  const logData = {
    eventType,
    details,
    timestamp: new Date().toISOString()
  }

  if (req) {
    logData.ip = req.ip || req.connection.remoteAddress
    logData.userAgent = req.headers['user-agent']
    logData.url = req.originalUrl
    logData.method = req.method
    logData.userId = req.auth?.userId || null
  }

  securityLogger.warn(`Security Event: ${eventType}`, logData)
}

/**
 * Log audit event
 */
const logAuditEvent = (action, resource, userId, details = {}) => {
  auditLogger.info(`Audit: ${action}`, {
    action,
    resource,
    userId,
    details,
    timestamp: new Date().toISOString()
  })
}

/**
 * Log performance metric
 */
const logPerformance = (metric, value, unit = 'ms', tags = {}) => {
  performanceLogger.info(`Performance: ${metric}`, {
    metric,
    value,
    unit,
    tags,
    timestamp: new Date().toISOString()
  })
}

/**
 * Log database query performance
 */
const logDatabaseQuery = (query, duration, success = true) => {
  const logData = {
    query: query.substring(0, 200), // Truncate long queries
    duration: `${duration}ms`,
    success
  }

  if (duration > 1000) {
    logger.warn('Slow Database Query', logData)
  } else {
    logger.debug('Database Query', logData)
  }
}

/**
 * Log error with context
 */
const logError = (error, context = {}) => {
  logger.error('Application Error', {
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack
    },
    context
  })
}

// ============================================================================
// MIDDLEWARE
// ============================================================================

/**
 * Request logging middleware
 */
const requestLoggingMiddleware = (req, res, next) => {
  const startTime = Date.now()
  
  // Generate correlation ID for request tracking
  req.correlationId = generateCorrelationId()
  
  // Add correlation ID to response headers
  res.setHeader('X-Correlation-ID', req.correlationId)
  
  // Log request start
  logger.http('Request Started', {
    method: req.method,
    url: req.originalUrl,
    correlationId: req.correlationId,
    ip: req.ip || req.connection.remoteAddress,
    userAgent: req.headers['user-agent']
  })
  
  // Override res.end to log response
  const originalEnd = res.end
  res.end = function(...args) {
    const responseTime = Date.now() - startTime
    logAPIRequest(req, res, responseTime)
    originalEnd.apply(this, args)
  }
  
  next()
}

// ============================================================================
// EXPORTS
// ============================================================================

export default logger

export {
  logger,
  securityLogger,
  auditLogger,
  performanceLogger,
  logWithCorrelation,
  generateCorrelationId,
  logAPIRequest,
  logSecurityEvent,
  logAuditEvent,
  logPerformance,
  logDatabaseQuery,
  logError,
  requestLoggingMiddleware
}
