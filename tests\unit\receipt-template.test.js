/**
 * Receipt Template Tests
 * Tests for BeMyRider receipt generation with real template format
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { describe, it, expect, beforeEach } from 'vitest'
import { ReceiptService } from '../../server/services/ReceiptService.js'

describe('BeMyRider Receipt Template', () => {
  let receiptService

  beforeEach(() => {
    receiptService = new ReceiptService()
  })

  describe('📄 Real Template Format', () => {
    it('should generate receipt matching exact template structure', async () => {
      const shiftData = {
        id: 'shift_123',
        rider_id: 'rider_456',
        client_id: 'client_789',
        scheduled_date: '2024-12-22T09:00:00Z',
        actual_start: '2024-12-22T09:00:00Z',
        actual_end: '2024-12-22T10:00:00Z',
        duration: 1,
        actual_duration: 1,
        hourly_rate: 5.00, // €5 for 1 hour = €5 total (well below threshold)
        vehicle_type: 'BICI',
        client_business_name: 'Golfood Italia Di Giorgio <PERSON>'
      }

      const receipt = await receiptService.generateReceipt(shiftData)

      // Verify receipt structure matches template
      expect(receipt.number).toMatch(/^BMR\d{4}/)
      expect(receipt.type).toBe('prestazione_occasionale')
      
      // Verify amounts calculation
      expect(receipt.amounts.gross_amount).toBe(5.00)
      expect(receipt.amounts.ritenuta_acconto).toBe(0) // Below threshold
      expect(receipt.amounts.below_threshold).toBe(true)
      expect(receipt.amounts.net_amount).toBe(5.00) // Full amount to rider
    })

    it('should format amounts exactly like template', () => {
      const shiftData = {
        hourly_rate: 5.00,
        duration: 1,
        actual_duration: 1
      }

      const amounts = receiptService.calculateAmounts(shiftData)

      // Template format verification
      expect(amounts.gross_amount_formatted).toBe('5')
      expect(amounts.net_amount_formatted).toBe('5')
      expect(amounts.ritenuta_formatted).toBe('0')
      expect(amounts.below_threshold).toBe(true)
      expect(amounts.fiscal_advantage).toBe('Esenzione ritenuta d\'acconto per importi sotto €25,82')
    })

    it('should generate PDF structure matching template layout', async () => {
      const shiftData = {
        id: 'shift_123',
        rider_id: 'rider_456',
        client_id: 'client_789',
        scheduled_date: '2024-12-22T09:00:00Z',
        actual_start: '2024-12-22T09:00:00Z',
        actual_end: '2024-12-22T10:00:00Z',
        duration: 1,
        hourly_rate: 5.00
      }

      const receipt = await receiptService.generateReceipt(shiftData)
      const pdfStructure = JSON.parse(receipt.pdf_buffer || '{}')

      // Verify header section matches template
      expect(pdfStructure.header).toHaveProperty('title', 'Ricevuta Prestazione Occasionale n°')
      expect(pdfStructure.header).toHaveProperty('receiptNumber')
      expect(pdfStructure.header).toHaveProperty('date')
      expect(pdfStructure.header).toHaveProperty('fiscalCode')

      // Verify DICHIARA section
      expect(pdfStructure.declaration).toHaveProperty('title', 'DICHIARA')
      expect(pdfStructure.declaration).toHaveProperty('receivingFrom', 'di ricevere dall\'azienda')

      // Verify service description
      expect(pdfStructure.serviceDescription.text).toContain('Rider su prenotazione a tariffa oraria sull\'app bemyrider')

      // Verify financial details format
      expect(pdfStructure.financialDetails).toHaveProperty('duration', 'ORE 1')
      expect(pdfStructure.financialDetails).toHaveProperty('grossAmount', '€ 5 EUR')
      expect(pdfStructure.financialDetails).toHaveProperty('withholdingAmount', '€ 0 EUR')
      expect(pdfStructure.financialDetails).toHaveProperty('netAmount', '€ 5 EUR')

      // Verify legal declaration
      expect(pdfStructure.legalDeclaration.statements).toHaveLength(4)
      expect(pdfStructure.legalDeclaration.statements[0]).toContain('carattere del tutto occasionale')

      // Verify signature section
      expect(pdfStructure.signature).toHaveProperty('text', 'Firma')
      expect(pdfStructure.signature).toHaveProperty('signatureLine')
    })
  })

  describe('💰 Fiscal Compliance in Template', () => {
    it('should show correct ritenuta d\'acconto text for amounts below threshold', () => {
      const shiftData = {
        hourly_rate: 12.00,
        duration: 2, // €24 total - below €25.82 threshold
        actual_duration: 2
      }

      const amounts = receiptService.calculateAmounts(shiftData)

      expect(amounts.below_threshold).toBe(true)
      expect(amounts.ritenuta_acconto).toBe(0)
      expect(amounts.gross_amount).toBe(24.00)
      expect(amounts.net_amount).toBe(24.00) // Rider keeps full amount
    })

    it('should handle maximum BeMyRider amount (€25.00)', () => {
      const shiftData = {
        hourly_rate: 12.50,
        duration: 2, // €25.00 total - still below €25.82 threshold
        actual_duration: 2
      }

      const amounts = receiptService.calculateAmounts(shiftData)

      expect(amounts.below_threshold).toBe(true)
      expect(amounts.ritenuta_acconto).toBe(0)
      expect(amounts.gross_amount).toBe(25.00)
      expect(amounts.net_amount).toBe(25.00)
      expect(amounts.fiscal_advantage).toBe('Esenzione ritenuta d\'acconto per importi sotto €25,82')
    })

    it('should format legal notes correctly for template', async () => {
      const shiftData = {
        id: 'shift_123',
        rider_id: 'rider_456',
        client_id: 'client_789',
        actual_start: '2024-12-22T09:00:00Z',
        actual_end: '2024-12-22T11:00:00Z',
        duration: 2,
        hourly_rate: 12.00 // €24 total
      }

      const receipt = await receiptService.generateReceipt(shiftData)

      expect(receipt.legal_notes).toContain(
        'Ritenuta d\'acconto non applicabile - Importo €24 inferiore alla soglia di €25.82'
      )
      expect(receipt.legal_notes).toContain(
        'Vantaggio fiscale: Prestatore incassa 100% della tariffa, committente senza oneri ritenuta'
      )
    })
  })

  describe('🏢 Company Information Template', () => {
    it('should use default company information matching template', async () => {
      const shiftData = {
        id: 'shift_123',
        rider_id: 'rider_456',
        client_id: 'client_789',
        actual_start: '2024-12-22T09:00:00Z',
        actual_end: '2024-12-22T10:00:00Z',
        duration: 1,
        hourly_rate: 5.00
      }

      const receipt = await receiptService.generateReceipt(shiftData)
      const pdfStructure = JSON.parse(receipt.pdf_buffer || '{}')

      // Should match template default company
      expect(pdfStructure.declaration.companyName).toBe('Golfood Italia Di Giorgio Di Martino')
      expect(pdfStructure.declaration.vatNumber).toBe('***********')
      expect(pdfStructure.declaration.companyAddress).toContain('Palermo P.A. Italia')
    })

    it('should use actual company data when provided', async () => {
      // Mock client data
      const originalGetClientData = receiptService.getClientData
      receiptService.getClientData = async () => ({
        business_name: 'Test Restaurant SRL',
        vat_number: '*************',
        city: 'Milano',
        address: 'Via Test 123'
      })

      const shiftData = {
        id: 'shift_123',
        rider_id: 'rider_456',
        client_id: 'client_789',
        actual_start: '2024-12-22T09:00:00Z',
        actual_end: '2024-12-22T10:00:00Z',
        duration: 1,
        hourly_rate: 5.00
      }

      const receipt = await receiptService.generateReceipt(shiftData)

      expect(receipt.committente.business_name).toBe('Test Restaurant SRL')
      expect(receipt.committente.vat_number).toBe('*************')

      // Restore original method
      receiptService.getClientData = originalGetClientData
    })
  })

  describe('📅 Date and Number Formatting', () => {
    it('should format dates in Italian format', async () => {
      const shiftData = {
        id: 'shift_123',
        rider_id: 'rider_456',
        client_id: 'client_789',
        scheduled_date: '2024-12-22T09:00:00Z',
        actual_start: '2024-12-22T09:00:00Z',
        actual_end: '2024-12-22T10:00:00Z',
        duration: 1,
        hourly_rate: 5.00
      }

      const receipt = await receiptService.generateReceipt(shiftData)
      const pdfStructure = JSON.parse(receipt.pdf_buffer || '{}')

      // Italian date format: DD/MM/YYYY
      expect(pdfStructure.header.date).toMatch(/^\d{1,2}\/\d{1,2}\/\d{4}$/)
      expect(pdfStructure.financialDetails.date).toMatch(/^\d{1,2}\/\d{1,2}\/\d{4}$/)
    })

    it('should generate receipt numbers in correct format', async () => {
      const receiptNumber = await receiptService.generateReceiptNumber()

      // Format: BMR + YEAR + 6-digit timestamp + 3-char random
      expect(receiptNumber).toMatch(/^BMR\d{4}\d{6}[A-Z]{3}$/)
      expect(receiptNumber).toHaveLength(16) // BMR(3) + YEAR(4) + TIMESTAMP(6) + RANDOM(3)
    })
  })

  describe('🎯 Template Compliance Verification', () => {
    it('should include all required template sections', async () => {
      const shiftData = {
        id: 'shift_123',
        rider_id: 'rider_456',
        client_id: 'client_789',
        actual_start: '2024-12-22T09:00:00Z',
        actual_end: '2024-12-22T10:00:00Z',
        duration: 1,
        hourly_rate: 5.00
      }

      const receipt = await receiptService.generateReceipt(shiftData)
      const pdfStructure = JSON.parse(receipt.pdf_buffer || '{}')

      // All required sections from template
      const requiredSections = [
        'header',
        'declaration',
        'serviceDescription',
        'financialDetails',
        'legalDeclaration',
        'signature',
        'metadata'
      ]

      requiredSections.forEach(section => {
        expect(pdfStructure).toHaveProperty(section)
      })
    })

    it('should maintain template text exactly', async () => {
      const shiftData = {
        id: 'shift_123',
        rider_id: 'rider_456',
        client_id: 'client_789',
        actual_start: '2024-12-22T09:00:00Z',
        actual_end: '2024-12-22T10:00:00Z',
        duration: 1,
        hourly_rate: 5.00
      }

      const receipt = await receiptService.generateReceipt(shiftData)
      const pdfStructure = JSON.parse(receipt.pdf_buffer || '{}')

      // Exact text from template
      expect(pdfStructure.serviceDescription.text).toBe(
        'quale compenso per l\'attività "Rider su prenotazione a tariffa oraria sull\'app bemyrider"'
      )

      expect(pdfStructure.legalDeclaration.statements[0]).toBe(
        '- ha carattere del tutto occasionale, non svolgendo il sottoscritto prestazioni di lavoro autonomo con carattere di abitualità;'
      )
    })
  })
})
