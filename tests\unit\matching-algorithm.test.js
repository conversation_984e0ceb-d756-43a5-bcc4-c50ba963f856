/**
 * Smart Matching Algorithm Tests
 * Tests for BeMyRider intelligent matching system
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { describe, it, expect, beforeEach } from 'vitest'
import { MatchingService } from '../../server/services/MatchingService.js'

describe('Smart Matching Algorithm', () => {
  let matchingService

  beforeEach(() => {
    matchingService = new MatchingService()
  })

  describe('🎯 Core Matching Logic', () => {
    it('should calculate accurate match scores', () => {
      const rider = {
        id: 'rider_1',
        name: '<PERSON>',
        rating: 4.8,
        hourlyRate: 12.00,
        distance: 2.5,
        vehicleType: 'MOTO',
        available: true
      }

      const bookingRequest = {
        id: 'booking_1',
        clientId: 'client_1',
        startTime: '2024-12-22T10:00:00Z',
        endTime: '2024-12-22T11:00:00Z',
        location: { lat: 45.4642, lng: 9.1900 },
        vehicleType: 'MOTO',
        budget: 12.00
      }

      const scoring = matchingService.calculateMatchScore(rider, bookingRequest)

      expect(scoring.totalScore).toBeGreaterThan(80)
      expect(scoring.breakdown).toHaveProperty('distance')
      expect(scoring.breakdown).toHaveProperty('rating')
      expect(scoring.breakdown).toHaveProperty('price')
      expect(scoring.breakdown).toHaveProperty('availability')
      expect(scoring.breakdown).toHaveProperty('preference')
    })

    it('should prioritize distance in scoring', () => {
      const closeRider = {
        id: 'rider_close',
        rating: 4.0,
        hourlyRate: 12.00,
        distance: 1.0, // Very close
        available: true
      }

      const farRider = {
        id: 'rider_far',
        rating: 4.0,
        hourlyRate: 12.00,
        distance: 10.0, // Far away
        available: true
      }

      const bookingRequest = {
        clientId: 'client_1',
        budget: 12.00
      }

      const closeScore = matchingService.calculateMatchScore(closeRider, bookingRequest)
      const farScore = matchingService.calculateMatchScore(farRider, bookingRequest)

      expect(closeScore.totalScore).toBeGreaterThan(farScore.totalScore)
      expect(closeScore.breakdown.distance).toBeGreaterThan(farScore.breakdown.distance)
    })

    it('should reward high ratings appropriately', () => {
      const excellentRider = {
        id: 'rider_excellent',
        rating: 4.9,
        hourlyRate: 12.00,
        distance: 5.0,
        available: true
      }

      const averageRider = {
        id: 'rider_average',
        rating: 3.5,
        hourlyRate: 12.00,
        distance: 5.0,
        available: true
      }

      const bookingRequest = {
        clientId: 'client_1',
        budget: 12.00
      }

      const excellentScore = matchingService.calculateMatchScore(excellentRider, bookingRequest)
      const averageScore = matchingService.calculateMatchScore(averageRider, bookingRequest)

      expect(excellentScore.breakdown.rating).toBe(100)
      expect(averageScore.breakdown.rating).toBeLessThan(excellentScore.breakdown.rating)
    })

    it('should consider price competitiveness', () => {
      const budgetRider = {
        id: 'rider_budget',
        rating: 4.0,
        hourlyRate: 10.00, // Below market rate
        distance: 5.0,
        available: true
      }

      const expensiveRider = {
        id: 'rider_expensive',
        rating: 4.0,
        hourlyRate: 12.50, // At maximum rate
        distance: 5.0,
        available: true
      }

      const bookingRequest = {
        clientId: 'client_1',
        budget: 11.00
      }

      const budgetScore = matchingService.calculateMatchScore(budgetRider, bookingRequest)
      const expensiveScore = matchingService.calculateMatchScore(expensiveRider, bookingRequest)

      expect(budgetScore.breakdown.price).toBeGreaterThan(expensiveScore.breakdown.price)
    })
  })

  describe('🔄 Matching Strategies', () => {
    const mockRiders = [
      {
        id: 'rider_1',
        name: 'Mario Rossi',
        rating: 4.8,
        hourlyRate: 12.00,
        distance: 2.5,
        vehicleType: 'MOTO',
        available: true
      },
      {
        id: 'rider_2',
        name: 'Luigi Verdi',
        rating: 4.2,
        hourlyRate: 10.50,
        distance: 1.5,
        vehicleType: 'BICI',
        available: true
      },
      {
        id: 'rider_3',
        name: 'Giuseppe Bianchi',
        rating: 4.9,
        hourlyRate: 11.00,
        distance: 4.0,
        vehicleType: 'MOTO',
        available: true
      }
    ]

    const bookingRequest = {
      id: 'booking_1',
      clientId: 'client_1',
      startTime: '2024-12-22T10:00:00Z',
      endTime: '2024-12-22T11:00:00Z',
      location: { lat: 45.4642, lng: 9.1900 },
      vehicleType: 'MOTO'
    }

    it('should apply distance-first strategy correctly', async () => {
      const rankedRiders = await matchingService.applyMatchingStrategy(
        mockRiders,
        bookingRequest,
        'distance_first'
      )

      // Should be sorted by distance (ascending)
      expect(rankedRiders[0].distance).toBeLessThanOrEqual(rankedRiders[1].distance)
      expect(rankedRiders[1].distance).toBeLessThanOrEqual(rankedRiders[2].distance)
    })

    it('should apply rating-first strategy correctly', async () => {
      const rankedRiders = await matchingService.applyMatchingStrategy(
        mockRiders,
        bookingRequest,
        'rating_first'
      )

      // Should be sorted by rating (descending)
      expect(rankedRiders[0].rating).toBeGreaterThanOrEqual(rankedRiders[1].rating)
      expect(rankedRiders[1].rating).toBeGreaterThanOrEqual(rankedRiders[2].rating)
    })

    it('should apply price-first strategy correctly', async () => {
      const rankedRiders = await matchingService.applyMatchingStrategy(
        mockRiders,
        bookingRequest,
        'price_first'
      )

      // Should be sorted by price (ascending)
      expect(rankedRiders[0].hourlyRate).toBeLessThanOrEqual(rankedRiders[1].hourlyRate)
      expect(rankedRiders[1].hourlyRate).toBeLessThanOrEqual(rankedRiders[2].hourlyRate)
    })

    it('should apply balanced strategy correctly', async () => {
      const rankedRiders = await matchingService.applyMatchingStrategy(
        mockRiders,
        bookingRequest,
        'balanced'
      )

      // Should be sorted by match score (descending)
      expect(rankedRiders[0].matchScore).toBeGreaterThanOrEqual(rankedRiders[1].matchScore)
      expect(rankedRiders[1].matchScore).toBeGreaterThanOrEqual(rankedRiders[2].matchScore)
    })
  })

  describe('🎛️ Preference System', () => {
    it('should get default client preferences', async () => {
      const preferences = await matchingService.getClientPreferences('client_1')

      expect(preferences).toHaveProperty('preferredVehicleType')
      expect(preferences).toHaveProperty('preferredRiders')
      expect(preferences).toHaveProperty('blockedRiders')
      expect(preferences).toHaveProperty('minPrice')
      expect(preferences).toHaveProperty('maxPrice')
      expect(preferences).toHaveProperty('maxDistance')
      expect(preferences).toHaveProperty('minRating')
    })

    it('should calculate compatibility scores', async () => {
      const compatibilityScore = await matchingService.calculateCompatibilityScore('client_1', 'rider_1')

      expect(compatibilityScore).toBeGreaterThanOrEqual(0)
      expect(compatibilityScore).toBeLessThanOrEqual(100)
    })

    it('should update preferences based on booking history', async () => {
      const bookingData = {
        id: 'booking_1',
        riderId: 'rider_1',
        rating: 4.8,
        vehicleType: 'MOTO',
        hourlyRate: 12.00
      }

      const updatedPreferences = await matchingService.updateClientPreferences('client_1', bookingData)

      expect(updatedPreferences.preferredRiders).toContain('rider_1')
    })

    it('should block riders with poor ratings', async () => {
      const bookingData = {
        id: 'booking_2',
        riderId: 'rider_bad',
        rating: 1.5, // Very poor rating
        vehicleType: 'MOTO',
        hourlyRate: 12.00
      }

      const updatedPreferences = await matchingService.updateClientPreferences('client_1', bookingData)

      expect(updatedPreferences.blockedRiders).toContain('rider_bad')
    })
  })

  describe('🔍 Scoring Components', () => {
    it('should calculate distance scores correctly', () => {
      expect(matchingService.calculateDistanceScore(1.0)).toBe(100) // Optimal distance
      expect(matchingService.calculateDistanceScore(5.0)).toBe(100) // Within optimal range
      expect(matchingService.calculateDistanceScore(10.0)).toBeGreaterThan(0) // Acceptable distance
      expect(matchingService.calculateDistanceScore(15.0)).toBe(0) // At maximum range
      expect(matchingService.calculateDistanceScore(20.0)).toBe(0) // Beyond maximum
    })

    it('should calculate rating scores correctly', () => {
      expect(matchingService.calculateRatingScore(4.8)).toBe(100) // Excellent
      expect(matchingService.calculateRatingScore(4.2)).toBe(80) // Good
      expect(matchingService.calculateRatingScore(3.5)).toBe(60) // Acceptable
      expect(matchingService.calculateRatingScore(2.5)).toBe(0) // Below minimum
    })

    it('should calculate price scores correctly', () => {
      expect(matchingService.calculatePriceScore(10.00, 12.00)).toBe(100) // Below budget
      expect(matchingService.calculatePriceScore(12.00, 12.00)).toBe(100) // At budget
      expect(matchingService.calculatePriceScore(13.00, 12.00)).toBeLessThan(100) // Above budget
    })

    it('should calculate availability scores correctly', () => {
      const availableRider = { available: true }
      const unavailableRider = { available: false }

      expect(matchingService.calculateAvailabilityScore(availableRider, {})).toBe(100)
      expect(matchingService.calculateAvailabilityScore(unavailableRider, {})).toBe(0)
    })

    it('should calculate preference scores correctly', () => {
      const rider = {
        vehicleType: 'MOTO',
        previousInteractions: 3
      }

      const bookingRequest = {
        preferredVehicleType: 'MOTO'
      }

      const score = matchingService.calculatePreferenceScore(rider, bookingRequest)
      expect(score).toBeGreaterThan(50) // Should get bonus for matching vehicle type
    })
  })

  describe('🚫 Fallback Strategies', () => {
    it('should handle no matches scenario', async () => {
      const bookingRequest = {
        id: 'booking_impossible',
        clientId: 'client_1',
        startTime: '2024-12-22T10:00:00Z',
        endTime: '2024-12-22T11:00:00Z',
        location: { lat: 45.4642, lng: 9.1900 },
        vehicleType: 'HELICOPTER' // Impossible vehicle type
      }

      // Mock the availability service to return no riders
      matchingService.availabilityService.findAvailableRiders = async () => []

      const result = await matchingService.findBestMatches(bookingRequest)

      expect(Array.isArray(result)).toBe(true)
      // Should return suggestions or empty array
    })

    it('should suggest alternative time slots', async () => {
      const bookingRequest = {
        startTime: '2024-12-22T10:00:00Z',
        endTime: '2024-12-22T11:00:00Z'
      }

      const alternatives = await matchingService.findAlternativeTimeSlots(bookingRequest)

      expect(Array.isArray(alternatives)).toBe(true)
      expect(alternatives.length).toBeGreaterThan(0)
      
      alternatives.forEach(alt => {
        expect(alt).toHaveProperty('startTime')
        expect(alt).toHaveProperty('endTime')
        expect(alt).toHaveProperty('reason')
      })
    })
  })

  describe('📊 Performance Metrics', () => {
    it('should generate match reasons', () => {
      const scoring = {
        breakdown: {
          distance: 95,
          rating: 100,
          price: 85,
          availability: 100,
          preference: 70
        }
      }

      const reason = matchingService.generateMatchReason(scoring, 'balanced')

      expect(typeof reason).toBe('string')
      expect(reason.length).toBeGreaterThan(0)
      expect(reason).toContain('close') // Should mention proximity
      expect(reason).toContain('rating') // Should mention excellent rating
    })

    it('should extract search criteria correctly', () => {
      const bookingRequest = {
        startTime: '2024-12-22T10:00:00Z',
        endTime: '2024-12-22T11:00:00Z',
        location: { lat: 45.4642, lng: 9.1900 },
        vehicleType: 'MOTO'
      }

      const criteria = matchingService.extractSearchCriteria(bookingRequest)

      expect(criteria).toHaveProperty('startTime')
      expect(criteria).toHaveProperty('endTime')
      expect(criteria).toHaveProperty('location')
      expect(criteria).toHaveProperty('vehicleType')
      expect(criteria).toHaveProperty('maxDistance')
      expect(criteria).toHaveProperty('minRating')
    })

    it('should apply filters correctly', () => {
      const riders = [
        { id: 'rider_1', distance: 5, rating: 4.5, hourlyRate: 10.00, vehicleType: 'MOTO' },
        { id: 'rider_2', distance: 20, rating: 4.0, hourlyRate: 12.00, vehicleType: 'BICI' }, // Too far
        { id: 'rider_3', distance: 3, rating: 2.5, hourlyRate: 11.00, vehicleType: 'MOTO' }, // Low rating
        { id: 'rider_4', distance: 4, rating: 4.2, hourlyRate: 15.00, vehicleType: 'MOTO' } // Too expensive
      ]

      const bookingRequest = {
        maxBudget: 12.50,
        requiredVehicleType: 'MOTO'
      }

      const options = {
        maxDistance: 15,
        minRating: 4.0
      }

      const filtered = matchingService.applyFilters(riders, bookingRequest, options)

      expect(filtered).toHaveLength(1) // Only rider_1 should pass all filters
      expect(filtered[0].id).toBe('rider_1')
    })
  })
})
