/**
 * Test Server with Mock Environment
 * Starts the server with mock environment variables for testing
 */

import dotenv from 'dotenv'
import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import { validateEnvironment, getEnv, getSafeConfig } from './server/utils/env-validator.js'

// Load test environment variables
dotenv.config({ path: '.env.test' })

async function testServerMock() {
  console.log('🧪 Testing server startup with mock environment...')
  
  try {
    // Validate environment first
    console.log('🔍 Validating environment...')
    validateEnvironment()
    
    const app = express()
    const PORT = parseInt(getEnv('PORT', '5000'), 10)
    
    console.log('🔧 Server starting with validated configuration:')
    const config = getSafeConfig()
    Object.entries(config).forEach(([key, value]) => {
      console.log(`   ${key}: ${value}`)
    })
    
    // Security middleware
    app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
          fontSrc: ["'self'", "https://fonts.gstatic.com"],
          imgSrc: ["'self'", "data:", "https:"],
          scriptSrc: ["'self'"],
          connectSrc: ["'self'", "https://api.stripe.com", "https://*.supabase.co", "https://*.clerk.accounts.dev"]
        }
      },
      crossOriginEmbedderPolicy: false
    }))
    
    // CORS configuration
    const corsOptions = {
      origin: function (origin, callback) {
        if (!origin) return callback(null, true)
        
        const allowedOrigins = [
          'http://localhost:3000',
          'http://localhost:5173',
          getEnv('FRONTEND_URL'),
        ].filter(Boolean)
        
        if (allowedOrigins.indexOf(origin) !== -1) {
          callback(null, true)
        } else {
          console.warn(`🚫 CORS blocked request from origin: ${origin}`)
          callback(new Error('Not allowed by CORS'))
        }
      },
      credentials: true,
      optionsSuccessStatus: 200
    }
    
    app.use(cors(corsOptions))
    app.use(express.json({ limit: '10mb' }))
    app.use(express.urlencoded({ extended: true, limit: '10mb' }))
    
    // Test routes
    app.get('/api/health', (req, res) => {
      res.json({ 
        status: 'OK', 
        message: 'BeMyRider API is running',
        environment: 'test',
        timestamp: new Date().toISOString()
      })
    })
    
    app.get('/api/config', (req, res) => {
      res.json({
        success: true,
        config: getSafeConfig()
      })
    })
    
    // Error handling middleware
    app.use((err, req, res, next) => {
      console.error(err.stack)
      res.status(500).json({ 
        success: false, 
        message: 'Qualcosa è andato storto!',
        error: process.env.NODE_ENV === 'development' ? err.message : undefined
      })
    })
    
    // 404 handler
    app.use('*', (req, res) => {
      res.status(404).json({ 
        success: false, 
        message: 'Endpoint non trovato' 
      })
    })
    
    // Start server
    const server = app.listen(PORT, () => {
      console.log(`🚀 Test server running on port ${PORT}`)
      console.log(`📱 BeMyRider Test API ready at http://localhost:${PORT}`)
      
      // Test the server
      setTimeout(async () => {
        console.log('\n🧪 Running server tests...')
        
        try {
          // Test health endpoint
          const response = await fetch(`http://localhost:${PORT}/api/health`)
          const data = await response.json()
          
          if (response.ok) {
            console.log('✅ Health endpoint working:', data.message)
          } else {
            console.log('❌ Health endpoint failed')
          }
          
          // Test config endpoint
          const configResponse = await fetch(`http://localhost:${PORT}/api/config`)
          const configData = await configResponse.json()
          
          if (configResponse.ok) {
            console.log('✅ Config endpoint working')
            console.log('   Config:', JSON.stringify(configData.config, null, 2))
          } else {
            console.log('❌ Config endpoint failed')
          }
          
          console.log('\n🎉 Server tests completed!')
          
        } catch (error) {
          console.error('❌ Server test error:', error.message)
        } finally {
          // Close server
          server.close(() => {
            console.log('🛑 Test server stopped')
            process.exit(0)
          })
        }
      }, 1000)
    })
    
  } catch (error) {
    console.error('❌ Server test failed:', error.message)
    if (error.errors) {
      error.errors.forEach(err => console.error(`   - ${err}`))
    }
    process.exit(1)
  }
}

testServerMock()
