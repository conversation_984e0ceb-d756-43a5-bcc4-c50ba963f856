import React, { createContext, useContext, ReactNode } from 'react'
import { useUser, useAuth as useClerkAuth } from '@clerk/clerk-react'
import toast from 'react-hot-toast'
import axios from 'axios'

interface AuthContextType {
  user: any // Clerk user object
  isSignedIn: boolean
  updateUserProfile: (data: { type: 'ESERCENTE' | 'RIDER' }) => Promise<boolean>
  logout: () => void
  loading: boolean
  getUserType: () => 'ESERCENTE' | 'RIDER' | null
  isProfileComplete: () => boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: ReactNode
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const { user: clerkUser, isSignedIn, isLoaded } = useUser()
  const { signOut } = useClerkAuth()

  // Funzione per ottenere il tipo di utente dai metadata di Clerk
  const getUserType = (): 'ESERCENTE' | 'RIDER' | null => {
    if (!clerkUser) return null
    return clerkUser.unsafeMetadata?.userType as 'ESERCENTE' | 'RIDER' || null
  }

  // Funzione per verificare se il profilo è completo
  const isProfileComplete = (): boolean => {
    if (!clerkUser) return false
    return !!clerkUser.unsafeMetadata?.profileComplete
  }

  // Sincronizza utente con il database
  const syncUserWithDatabase = async (userType: 'ESERCENTE' | 'RIDER') => {
    try {
      if (!clerkUser) return false

      const userData = {
        clerkId: clerkUser.id,
        email: clerkUser.emailAddresses[0]?.emailAddress,
        firstName: clerkUser.firstName,
        lastName: clerkUser.lastName,
        name: clerkUser.fullName,
        userType,
        profileComplete: true
      }

      const response = await axios.post('/api/users/sync', userData)
      return response.data.success
    } catch (error) {
      console.error('Error syncing user with database:', error)
      return false
    }
  }

  // Aggiorna il profilo utente usando i metadata di Clerk
  const updateUserProfile = async (data: { type: 'ESERCENTE' | 'RIDER' }): Promise<boolean> => {
    try {
      if (!clerkUser) return false

      // Verifica se l'utente ha già un tipo impostato e sta cercando di cambiarlo
      const currentType = getUserType()
      if (currentType && currentType !== data.type) {
        toast.error('Non puoi cambiare il tipo di profilo dopo la registrazione')
        return false
      }

      // Aggiorna i metadata di Clerk usando unsafeMetadata (accessibile dal frontend)
      await clerkUser.update({
        unsafeMetadata: {
          ...clerkUser.unsafeMetadata,
          userType: data.type,
          profileComplete: true
        }
      })

      // Sincronizza con il database
      const dbSyncSuccess = await syncUserWithDatabase(data.type)
      if (!dbSyncSuccess) {
        console.warn('Failed to sync user with database, but Clerk update succeeded')
      }

      toast.success('Profilo configurato con successo!')
      return true
    } catch (error: any) {
      console.error('Error updating profile:', error)
      toast.error('Errore durante la configurazione del profilo')
      return false
    }
  }

  const logout = async () => {
    await signOut()
    toast.success('Logout effettuato con successo!')
  }

  const value: AuthContextType = {
    user: clerkUser,
    isSignedIn: isSignedIn || false,
    updateUserProfile,
    logout,
    loading: !isLoaded,
    getUserType,
    isProfileComplete
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
