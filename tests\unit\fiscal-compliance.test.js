/**
 * Fiscal Compliance Tests
 * Tests for Italian tax law compliance (ritenuta d'acconto)
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { describe, it, expect, beforeEach } from 'vitest'
import { PricingService } from '../../server/services/PricingService.js'
import { ReceiptService } from '../../server/services/ReceiptService.js'

describe('Fiscal Compliance - Ritenuta d\'Acconto', () => {
  let pricingService
  let receiptService

  beforeEach(() => {
    pricingService = new PricingService()
    receiptService = new ReceiptService()
  })

  describe('🏛️ Italian Tax Law Compliance', () => {
    it('should stay below €25.82 ritenuta d\'acconto threshold', async () => {
      // Maximum allowed configuration
      const maxBooking = {
        vehicleType: 'MOTO',
        duration: 2, // 2 hours
        customRate: 12.50 // Maximum rate
      }

      const pricing = await pricingService.calculateBookingPrice(maxBooking)

      // €12.50 × 2h = €25.00 (below €25.82 threshold)
      expect(pricing.baseAmount).toBe(25.00)
      expect(pricing.baseAmount).toBeLessThan(25.82)
      expect(pricing.fiscalCompliance.belowThreshold).toBe(true)
      expect(pricing.fiscalCompliance.ritenutaAcconto).toBe(0)
    })

    it('should reject configurations that exceed fiscal threshold', async () => {
      // This would exceed the threshold if allowed
      const invalidBooking = {
        vehicleType: 'MOTO',
        duration: 2,
        customRate: 13.00 // Would result in €26.00 (above threshold)
      }

      await expect(pricingService.calculateBookingPrice(invalidBooking))
        .rejects.toThrow('exceeds maximum €25 (fiscal compliance limit)')
    })

    it('should enforce maximum hourly rate of €12.50', async () => {
      const invalidRate = {
        vehicleType: 'MOTO',
        duration: 1,
        customRate: 15.00 // Above maximum
      }

      await expect(pricingService.calculateBookingPrice(invalidRate))
        .rejects.toThrow('Hourly rate cannot exceed €12.5/hour')
    })
  })

  describe('💰 Fiscal Advantages', () => {
    it('should calculate rider net earnings without ritenuta d\'acconto', async () => {
      const booking = {
        vehicleType: 'MOTO',
        duration: 2,
        customRate: 12.00 // €24.00 total (below threshold)
      }

      const pricing = await pricingService.calculateBookingPrice(booking)

      // Rider receives 100% of their rate (no withholding tax)
      expect(pricing.fiscalCompliance.riderNetAmount).toBe(24.00)
      expect(pricing.fiscalCompliance.ritenutaAcconto).toBe(0)
      
      // Platform fee is separate
      expect(pricing.platformFee).toBe(3.60) // 15% of €24
    })

    it('should demonstrate merchant fiscal advantage', async () => {
      const shiftData = {
        id: 'shift_123',
        rider_id: 'rider_456',
        client_id: 'client_789',
        actual_start: '2024-01-15T09:00:00Z',
        actual_end: '2024-01-15T11:00:00Z',
        duration: 2,
        hourly_rate: 12.00 // €24.00 total
      }

      const receipt = await receiptService.generateReceipt(shiftData)

      // Merchant has no obligation to pay ritenuta d'acconto
      expect(receipt.amounts.ritenuta_acconto).toBe(0)
      expect(receipt.amounts.below_threshold).toBe(true)
      expect(receipt.amounts.fiscal_advantage).toBe('Esenzione ritenuta d\'acconto')
    })
  })

  describe('📄 Receipt Fiscal Information', () => {
    it('should include correct fiscal notes for amounts below threshold', async () => {
      const shiftData = {
        id: 'shift_123',
        rider_id: 'rider_456',
        client_id: 'client_789',
        actual_start: '2024-01-15T09:00:00Z',
        actual_end: '2024-01-15T11:00:00Z',
        duration: 2,
        hourly_rate: 12.00
      }

      const receipt = await receiptService.generateReceipt(shiftData)

      expect(receipt.legal_notes).toContain(
        'Ritenuta d\'acconto non applicabile - Importo €24 inferiore alla soglia di €25.82'
      )
      expect(receipt.legal_notes).toContain(
        'Vantaggio fiscale: Prestatore incassa 100% della tariffa, committente senza oneri ritenuta'
      )
      expect(receipt.legal_notes).toContain(
        'Riferimento normativo: Agenzia delle Entrate - Ritenute su redditi di lavoro autonomo'
      )
    })

    it('should calculate amounts correctly with fiscal compliance', async () => {
      const shiftData = {
        id: 'shift_123',
        rider_id: 'rider_456',
        client_id: 'client_789',
        actual_start: '2024-01-15T09:00:00Z',
        actual_end: '2024-01-15T11:00:00Z',
        duration: 2,
        hourly_rate: 12.50 // Maximum rate
      }

      const receipt = await receiptService.generateReceipt(shiftData)

      expect(receipt.amounts.gross_amount).toBe(25.00) // €12.50 × 2h
      expect(receipt.amounts.ritenuta_acconto).toBe(0) // No withholding tax
      expect(receipt.amounts.platform_fee).toBe(3.75) // 15% of €25
      expect(receipt.amounts.net_amount).toBe(21.25) // €25 - €3.75 (no ritenuta)
      expect(receipt.amounts.below_threshold).toBe(true)
    })
  })

  describe('🔍 Edge Cases', () => {
    it('should handle exact threshold amount correctly', async () => {
      // Test with amount exactly at threshold (should still be exempt)
      const booking = {
        vehicleType: 'MOTO',
        duration: 2,
        customRate: 12.41 // €24.82 (still below €25.82)
      }

      const pricing = await pricingService.calculateBookingPrice(booking)

      expect(pricing.baseAmount).toBe(24.82)
      expect(pricing.fiscalCompliance.belowThreshold).toBe(true)
      expect(pricing.fiscalCompliance.ritenutaAcconto).toBe(0)
    })

    it('should validate minimum rates', async () => {
      const booking = {
        vehicleType: 'MOTO',
        duration: 1,
        customRate: 0 // Invalid rate
      }

      await expect(pricingService.calculateBookingPrice(booking))
        .rejects.toThrow('Hourly rate must be greater than 0')
    })

    it('should handle fractional hours correctly', async () => {
      // Even though duration is limited to 1-2 hours, test fractional calculation
      const booking = {
        vehicleType: 'MOTO',
        duration: 1.5, // Should be rejected by duration validation
        customRate: 12.00
      }

      await expect(pricingService.calculateBookingPrice(booking))
        .rejects.toThrow('Duration must be between 1 and 2 hours')
    })
  })

  describe('📊 Business Model Validation', () => {
    it('should demonstrate complete fiscal optimization', async () => {
      // Scenario: Maximum earning shift with full fiscal optimization
      const optimalShift = {
        vehicleType: 'FURGONE',
        duration: 2,
        customRate: 12.50 // Maximum allowed rate
      }

      const pricing = await pricingService.calculateBookingPrice(optimalShift)

      // Financial breakdown
      expect(pricing.baseAmount).toBe(25.00) // €12.50 × 2h
      expect(pricing.platformFee).toBe(3.75) // 15% platform fee
      expect(pricing.total).toBe(28.75) // Total charged to merchant

      // Fiscal compliance
      expect(pricing.fiscalCompliance.ritenutaAcconto).toBe(0) // No withholding tax
      expect(pricing.fiscalCompliance.riderNetAmount).toBe(25.00) // Rider gets full amount
      expect(pricing.fiscalCompliance.belowThreshold).toBe(true)

      // Rider earnings (after platform fee)
      const riderEarnings = pricingService.calculateRiderEarnings({ baseAmount: 25.00 })
      expect(riderEarnings).toBe(21.25) // 85% of €25.00

      // This demonstrates the complete value proposition:
      // - Rider: Gets €21.25 net (no tax withholding)
      // - Merchant: Pays €28.75 total (no ritenuta d'acconto obligations)
      // - Platform: Gets €3.75 fee
      // - Tax Authority: No withholding tax required
    })

    it('should validate all vehicle types stay within fiscal limits', async () => {
      const vehicles = ['BICI', 'MOTO', 'AUTO', 'FURGONE']

      for (const vehicleType of vehicles) {
        const maxShift = {
          vehicleType,
          duration: 2,
          customRate: 12.50
        }

        const pricing = await pricingService.calculateBookingPrice(maxShift)
        
        expect(pricing.baseAmount).toBeLessThanOrEqual(25.00)
        expect(pricing.fiscalCompliance.belowThreshold).toBe(true)
        expect(pricing.fiscalCompliance.ritenutaAcconto).toBe(0)
      }
    })
  })

  describe('🎯 Competitive Advantage', () => {
    it('should demonstrate platform value proposition', () => {
      // BeMyRider's competitive advantage through fiscal optimization
      const traditionalModel = {
        grossAmount: 25.00,
        ritenutaAcconto: 25.00 * 0.20, // 20% withholding tax
        merchantObligations: true,
        riderNetAmount: 25.00 * 0.80 // 80% after withholding
      }

      const bemyriderModel = {
        grossAmount: 25.00,
        ritenutaAcconto: 0, // No withholding tax
        merchantObligations: false,
        riderNetAmount: 25.00 // 100% of rate
      }

      // Rider advantage: +25% net income
      const riderAdvantage = (bemyriderModel.riderNetAmount - traditionalModel.riderNetAmount) / traditionalModel.riderNetAmount
      expect(riderAdvantage).toBe(0.25) // 25% more net income

      // Merchant advantage: No administrative burden
      expect(bemyriderModel.merchantObligations).toBe(false)
      expect(traditionalModel.merchantObligations).toBe(true)
    })
  })
})
