/**
 * Start Test Server
 * Starts a test server with mock environment for testing
 */

import dotenv from 'dotenv'
import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import { validateEnvironment, getEnv, getSafeConfig } from './server/utils/env-validator.js'
import { authenticateClerk, optionalAuth, validateInput, rateLimit } from './server/middleware/auth.js'

// Load test environment variables
dotenv.config({ path: '.env.test' })

async function startTestServer() {
  console.log('🚀 Starting test server...')
  
  try {
    // Validate environment first
    validateEnvironment()
    
    const app = express()
    const PORT = 5000 // Use standard port for testing
    
    console.log('🔧 Server starting with validated configuration:')
    const config = getSafeConfig()
    Object.entries(config).forEach(([key, value]) => {
      console.log(`   ${key}: ${value}`)
    })
    
    // Security middleware
    app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
          fontSrc: ["'self'", "https://fonts.gstatic.com"],
          imgSrc: ["'self'", "data:", "https:"],
          scriptSrc: ["'self'"],
          connectSrc: ["'self'", "https://api.stripe.com", "https://*.supabase.co", "https://*.clerk.accounts.dev"]
        }
      },
      crossOriginEmbedderPolicy: false
    }))
    
    // CORS configuration
    const corsOptions = {
      origin: function (origin, callback) {
        if (!origin) return callback(null, true)
        
        const allowedOrigins = [
          'http://localhost:3000',
          'http://localhost:5173',
          getEnv('FRONTEND_URL'),
        ].filter(Boolean)
        
        if (allowedOrigins.indexOf(origin) !== -1) {
          callback(null, true)
        } else {
          console.warn(`🚫 CORS blocked request from origin: ${origin}`)
          callback(new Error('Not allowed by CORS'))
        }
      },
      credentials: true,
      optionsSuccessStatus: 200
    }
    
    app.use(cors(corsOptions))
    app.use(express.json({ limit: '10mb' }))
    app.use(express.urlencoded({ extended: true, limit: '10mb' }))
    
    // Test routes with different security levels
    
    // Public route
    app.get('/api/health', (req, res) => {
      res.json({ 
        status: 'OK', 
        message: 'BeMyRider API is running',
        environment: 'test',
        timestamp: new Date().toISOString()
      })
    })
    
    // Rate limited route
    app.get('/api/limited', rateLimit(5, 60000), (req, res) => {
      res.json({ 
        success: true,
        message: 'Rate limited endpoint accessed',
        timestamp: new Date().toISOString()
      })
    })
    
    // Protected route (requires authentication)
    app.get('/api/protected', authenticateClerk, (req, res) => {
      res.json({ 
        success: true,
        message: 'Protected endpoint accessed',
        userId: req.auth.userId
      })
    })
    
    // Route with input validation
    app.post('/api/validate', 
      validateInput({
        required: ['email', 'name'],
        email: ['email']
      }),
      (req, res) => {
        res.json({ 
          success: true,
          message: 'Input validation passed',
          data: req.body
        })
      }
    )
    
    // Optional auth route
    app.get('/api/optional-auth', optionalAuth, (req, res) => {
      res.json({ 
        success: true,
        message: 'Optional auth endpoint',
        authenticated: !!req.auth,
        userId: req.auth?.userId || null
      })
    })
    
    // Error handling middleware
    app.use((err, req, res, next) => {
      console.error('Error:', err.message)
      res.status(500).json({ 
        success: false, 
        message: 'Qualcosa è andato storto!',
        error: process.env.NODE_ENV === 'development' ? err.message : undefined
      })
    })
    
    // 404 handler
    app.use('*', (req, res) => {
      res.status(404).json({ 
        success: false, 
        message: 'Endpoint non trovato' 
      })
    })
    
    // Start server
    app.listen(PORT, () => {
      console.log(`🚀 Test server running on port ${PORT}`)
      console.log(`📱 BeMyRider Test API ready at http://localhost:${PORT}`)
      console.log('\n📋 Available test endpoints:')
      console.log('   GET  /api/health          - Public health check')
      console.log('   GET  /api/limited         - Rate limited endpoint')
      console.log('   GET  /api/protected       - Protected endpoint (requires auth)')
      console.log('   POST /api/validate        - Input validation test')
      console.log('   GET  /api/optional-auth   - Optional authentication')
      console.log('\n🧪 Server ready for testing!')
    })
    
  } catch (error) {
    console.error('❌ Server startup failed:', error.message)
    if (error.errors) {
      error.errors.forEach(err => console.error(`   - ${err}`))
    }
    process.exit(1)
  }
}

startTestServer()
