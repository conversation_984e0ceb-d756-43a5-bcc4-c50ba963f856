/**
 * Performance Tester Helper
 * Utilities for load testing and performance measurement
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import request from 'supertest'

export class PerformanceTester {
  constructor(app) {
    this.app = app
  }

  /**
   * Run a load test with specified parameters
   * @param {Object} options - Test configuration
   * @returns {Promise<Object>} Test results
   */
  async loadTest(options) {
    const {
      endpoint,
      method = 'GET',
      headers = {},
      body = null,
      concurrency = 1,
      requests = 10,
      maxResponseTime = 1000,
      retryOnFailure = false
    } = options

    const results = {
      totalRequests: requests,
      successfulRequests: 0,
      failedRequests: 0,
      responseTimes: [],
      errors: [],
      startTime: Date.now(),
      endTime: null
    }

    // Create batches for concurrent execution
    const batches = this.createBatches(requests, concurrency)
    
    for (const batch of batches) {
      const batchPromises = batch.map(() => this.makeRequest(endpoint, method, headers, body, retryOnFailure))
      const batchResults = await Promise.allSettled(batchPromises)
      
      batchResults.forEach(result => {
        if (result.status === 'fulfilled') {
          results.successfulRequests++
          results.responseTimes.push(result.value.responseTime)
          
          if (result.value.responseTime > maxResponseTime) {
            results.errors.push(`Response time ${result.value.responseTime}ms exceeded limit ${maxResponseTime}ms`)
          }
        } else {
          results.failedRequests++
          results.errors.push(result.reason.message)
        }
      })
    }

    results.endTime = Date.now()
    return this.calculateMetrics(results)
  }

  /**
   * Run a concurrency test with gradual load increase
   * @param {Object} options - Test configuration
   * @returns {Promise<Object>} Test results
   */
  async concurrencyTest(options) {
    const {
      endpoint,
      method = 'GET',
      headers = {},
      body = null,
      maxConcurrency = 10,
      duration = 10000,
      rampUpTime = 2000
    } = options

    const results = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      responseTimes: [],
      errors: [],
      startTime: Date.now(),
      endTime: null
    }

    const startTime = Date.now()
    const endTime = startTime + duration
    const rampUpEnd = startTime + rampUpTime

    while (Date.now() < endTime) {
      const currentTime = Date.now()
      let currentConcurrency

      if (currentTime < rampUpEnd) {
        // Ramp up phase
        const rampProgress = (currentTime - startTime) / rampUpTime
        currentConcurrency = Math.ceil(maxConcurrency * rampProgress)
      } else {
        // Full load phase
        currentConcurrency = maxConcurrency
      }

      const promises = Array(currentConcurrency).fill().map(() => 
        this.makeRequest(endpoint, method, headers, body)
      )

      const batchResults = await Promise.allSettled(promises)
      
      batchResults.forEach(result => {
        results.totalRequests++
        if (result.status === 'fulfilled') {
          results.successfulRequests++
          results.responseTimes.push(result.value.responseTime)
        } else {
          results.failedRequests++
          results.errors.push(result.reason.message)
        }
      })

      // Small delay to prevent overwhelming
      await this.sleep(100)
    }

    results.endTime = Date.now()
    return this.calculateMetrics(results)
  }

  /**
   * Run a stress test to find breaking point
   * @param {Object} options - Test configuration
   * @returns {Promise<Object>} Test results
   */
  async stressTest(options) {
    const {
      endpoint,
      method = 'GET',
      headers = {},
      body = null,
      initialLoad = 1,
      maxLoad = 50,
      stepSize = 5,
      stepDuration = 5000,
      acceptableErrorRate = 0.1
    } = options

    const results = {
      breakingPoint: null,
      maxThroughput: 0,
      steps: []
    }

    for (let load = initialLoad; load <= maxLoad; load += stepSize) {
      console.log(`Testing with load: ${load}`)
      
      const stepResult = await this.concurrencyTest({
        endpoint,
        method,
        headers,
        body,
        maxConcurrency: load,
        duration: stepDuration,
        rampUpTime: 1000
      })

      results.steps.push({
        load,
        ...stepResult
      })

      results.maxThroughput = Math.max(results.maxThroughput, stepResult.throughput)

      // Check if we've reached the breaking point
      if (stepResult.errorRate > acceptableErrorRate) {
        results.breakingPoint = load
        break
      }
    }

    return results
  }

  /**
   * Test rate limiting behavior
   * @param {Object} options - Test configuration
   * @returns {Promise<Object>} Test results
   */
  async rateLimitTest(options) {
    const {
      endpoint,
      method = 'GET',
      headers = {},
      body = null,
      requestsPerSecond = 10,
      duration = 5000
    } = options

    const results = {
      totalRequests: 0,
      successfulRequests: 0,
      rateLimitedRequests: 0,
      otherErrors: 0,
      responseTimes: []
    }

    const startTime = Date.now()
    const endTime = startTime + duration
    const interval = 1000 / requestsPerSecond

    while (Date.now() < endTime) {
      const requestStart = Date.now()
      
      try {
        const result = await this.makeRequest(endpoint, method, headers, body)
        results.totalRequests++
        results.successfulRequests++
        results.responseTimes.push(result.responseTime)
      } catch (error) {
        results.totalRequests++
        if (error.status === 429) {
          results.rateLimitedRequests++
        } else {
          results.otherErrors++
        }
      }

      // Wait for next interval
      const elapsed = Date.now() - requestStart
      const waitTime = Math.max(0, interval - elapsed)
      if (waitTime > 0) {
        await this.sleep(waitTime)
      }
    }

    return {
      ...results,
      successRate: results.successfulRequests / results.totalRequests,
      rateLimitRate: results.rateLimitedRequests / results.totalRequests
    }
  }

  /**
   * Make a single HTTP request and measure response time
   * @param {string} endpoint - API endpoint
   * @param {string} method - HTTP method
   * @param {Object} headers - Request headers
   * @param {Object} body - Request body
   * @param {boolean} retryOnFailure - Whether to retry on failure
   * @returns {Promise<Object>} Request result
   */
  async makeRequest(endpoint, method, headers, body, retryOnFailure = false) {
    const startTime = Date.now()
    
    try {
      let req = request(this.app)[method.toLowerCase()](endpoint)
      
      // Add headers
      Object.entries(headers).forEach(([key, value]) => {
        req = req.set(key, value)
      })
      
      // Add body for POST/PUT requests
      if (body && ['POST', 'PUT', 'PATCH'].includes(method.toUpperCase())) {
        req = req.send(body)
      }
      
      const response = await req
      const responseTime = Date.now() - startTime
      
      return {
        status: response.status,
        responseTime,
        body: response.body
      }
    } catch (error) {
      const responseTime = Date.now() - startTime
      
      if (retryOnFailure && error.status >= 500) {
        // Retry once for server errors
        await this.sleep(100)
        return this.makeRequest(endpoint, method, headers, body, false)
      }
      
      throw {
        status: error.status || 500,
        message: error.message,
        responseTime
      }
    }
  }

  /**
   * Create batches for concurrent execution
   * @param {number} totalRequests - Total number of requests
   * @param {number} concurrency - Concurrent requests per batch
   * @returns {Array} Array of batches
   */
  createBatches(totalRequests, concurrency) {
    const batches = []
    let remaining = totalRequests
    
    while (remaining > 0) {
      const batchSize = Math.min(concurrency, remaining)
      batches.push(Array(batchSize).fill(null))
      remaining -= batchSize
    }
    
    return batches
  }

  /**
   * Calculate performance metrics from results
   * @param {Object} results - Raw test results
   * @returns {Object} Calculated metrics
   */
  calculateMetrics(results) {
    const { responseTimes, totalRequests, successfulRequests, startTime, endTime } = results
    
    if (responseTimes.length === 0) {
      return {
        ...results,
        averageResponseTime: 0,
        minResponseTime: 0,
        maxResponseTime: 0,
        p50ResponseTime: 0,
        p95ResponseTime: 0,
        p99ResponseTime: 0,
        successRate: 0,
        errorRate: 1,
        throughput: 0,
        duration: endTime - startTime
      }
    }
    
    const sortedTimes = [...responseTimes].sort((a, b) => a - b)
    const duration = endTime - startTime
    
    return {
      ...results,
      averageResponseTime: responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length,
      minResponseTime: Math.min(...responseTimes),
      maxResponseTime: Math.max(...responseTimes),
      p50ResponseTime: this.percentile(sortedTimes, 50),
      p95ResponseTime: this.percentile(sortedTimes, 95),
      p99ResponseTime: this.percentile(sortedTimes, 99),
      successRate: successfulRequests / totalRequests,
      errorRate: (totalRequests - successfulRequests) / totalRequests,
      throughput: (successfulRequests / duration) * 1000, // requests per second
      duration
    }
  }

  /**
   * Calculate percentile from sorted array
   * @param {Array} sortedArray - Sorted array of values
   * @param {number} percentile - Percentile to calculate (0-100)
   * @returns {number} Percentile value
   */
  percentile(sortedArray, percentile) {
    if (sortedArray.length === 0) return 0
    
    const index = (percentile / 100) * (sortedArray.length - 1)
    const lower = Math.floor(index)
    const upper = Math.ceil(index)
    
    if (lower === upper) {
      return sortedArray[lower]
    }
    
    const weight = index - lower
    return sortedArray[lower] * (1 - weight) + sortedArray[upper] * weight
  }

  /**
   * Sleep for specified milliseconds
   * @param {number} ms - Milliseconds to sleep
   * @returns {Promise} Promise that resolves after delay
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}
