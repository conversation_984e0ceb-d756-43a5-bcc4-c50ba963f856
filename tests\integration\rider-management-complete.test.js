/**
 * Complete Rider Management Integration Tests
 * End-to-end testing for the entire rider management system
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest'
import request from 'supertest'
import { createTestApp } from '../helpers/test-app.js'

describe('Complete Rider Management System', () => {
  let app
  let riderAuthHeaders
  let merchantAuthHeaders
  let onboardingSession
  let riderId
  let merchantId

  beforeAll(async () => {
    app = await createTestApp()
    
    // Setup auth headers
    riderId = 'test_rider_complete'
    merchantId = 'test_merchant_complete'
    
    riderAuthHeaders = global.createAuthHeaders(riderId)
    merchantAuthHeaders = global.createAuthHeaders(merchantId)

    // Setup test users
    global.testDb.addUser({
      id: riderId,
      clerk_id: `${riderId}_clerk`,
      email: '<EMAIL>',
      type: 'RIDER',
      first_name: '<PERSON>',
      last_name: '<PERSON>',
      profile_complete: false
    })

    global.testDb.addUser({
      id: merchantId,
      clerk_id: `${merchantId}_clerk`,
      email: '<EMAIL>',
      type: 'ESERCENTE',
      first_name: '<PERSON>',
      last_name: '<PERSON>',
      profile_complete: true
    })
  })

  afterAll(async () => {
    if (global.testDb) {
      global.testDb.clear()
    }
  })

  describe('🔄 Complete Rider Lifecycle', () => {
    it('should complete full rider lifecycle from onboarding to fleet management', async () => {
      // ========================================================================
      // PHASE 1: RIDER ONBOARDING
      // ========================================================================
      
      console.log('🚀 Phase 1: Starting rider onboarding...')
      
      // Start onboarding
      const startResponse = await request(app)
        .post('/api/rider-onboarding/start')
        .set(riderAuthHeaders)
        .send({
          firstName: 'Mario',
          lastName: 'Rossi',
          email: '<EMAIL>',
          phone: '+39 333 1234567'
        })
        .expect(201)

      expect(startResponse.body.success).toBe(true)
      onboardingSession = startResponse.body.session

      // Complete personal information
      await request(app)
        .post('/api/rider-onboarding/personal-info')
        .set(riderAuthHeaders)
        .send({
          sessionId: onboardingSession.id,
          firstName: 'Mario',
          lastName: 'Rossi',
          email: '<EMAIL>',
          phone: '+39 333 1234567',
          dateOfBirth: '1990-01-15',
          fiscalCode: '****************',
          address: 'Via Roma 123, Milano, 20100'
        })
        .expect(200)

      // Upload documents
      await request(app)
        .post('/api/rider-onboarding/documents')
        .set(riderAuthHeaders)
        .field('sessionId', onboardingSession.id)
        .attach('identity_card', Buffer.from('mock-id-card'), 'id-card.jpg')
        .attach('driving_license', Buffer.from('mock-license'), 'license.jpg')
        .expect(200)

      // Complete vehicle information
      await request(app)
        .post('/api/rider-onboarding/vehicle-info')
        .set(riderAuthHeaders)
        .send({
          sessionId: onboardingSession.id,
          vehicleType: 'MOTO',
          make: 'Honda',
          model: 'SH 150',
          year: '2020',
          licensePlate: 'AB123CD',
          hourlyRate: 12.00
        })
        .expect(200)

      console.log('✅ Phase 1: Rider onboarding completed')

      // ========================================================================
      // PHASE 2: AVAILABILITY MANAGEMENT
      // ========================================================================
      
      console.log('🚀 Phase 2: Setting up availability...')

      // Set weekly availability
      const weeklyPattern = {
        monday: { available: true, hours: [{ start: '09:00', end: '18:00' }] },
        tuesday: { available: true, hours: [{ start: '09:00', end: '18:00' }] },
        wednesday: { available: true, hours: [{ start: '09:00', end: '18:00' }] },
        thursday: { available: true, hours: [{ start: '09:00', end: '18:00' }] },
        friday: { available: true, hours: [{ start: '09:00', end: '18:00' }] },
        saturday: { available: false, hours: [] },
        sunday: { available: false, hours: [] }
      }

      await request(app)
        .post('/api/availability/weekly')
        .set(riderAuthHeaders)
        .send({ weeklyPattern })
        .expect(200)

      // Add specific availability slots
      const tomorrow = new Date()
      tomorrow.setDate(tomorrow.getDate() + 1)
      const tomorrowStr = tomorrow.toISOString().split('T')[0]

      await request(app)
        .post('/api/availability/slots')
        .set(riderAuthHeaders)
        .send({
          slots: [
            {
              startTime: `${tomorrowStr}T10:00:00Z`,
              endTime: `${tomorrowStr}T12:00:00Z`,
              type: 'specific'
            }
          ]
        })
        .expect(201)

      // Get rider availability
      const availabilityResponse = await request(app)
        .get('/api/availability/my-availability')
        .set(riderAuthHeaders)
        .query({
          startDate: tomorrowStr,
          endDate: tomorrowStr
        })
        .expect(200)

      expect(availabilityResponse.body.success).toBe(true)
      expect(Array.isArray(availabilityResponse.body.availability)).toBe(true)

      console.log('✅ Phase 2: Availability management completed')

      // ========================================================================
      // PHASE 3: MATCHING SYSTEM
      // ========================================================================
      
      console.log('🚀 Phase 3: Testing matching system...')

      // Find matches for a booking request
      const matchingResponse = await request(app)
        .post('/api/matching/find-matches')
        .set(merchantAuthHeaders)
        .send({
          startTime: `${tomorrowStr}T10:00:00Z`,
          endTime: `${tomorrowStr}T11:00:00Z`,
          location: { lat: 45.4642, lng: 9.1900, address: 'Milano Centro' },
          vehicleType: 'MOTO',
          strategy: 'balanced'
        })
        .expect(200)

      expect(matchingResponse.body.success).toBe(true)
      expect(Array.isArray(matchingResponse.body.matches)).toBe(true)

      // Get client preferences
      await request(app)
        .get('/api/matching/preferences')
        .set(merchantAuthHeaders)
        .expect(200)

      // Update client preferences
      await request(app)
        .put('/api/matching/preferences')
        .set(merchantAuthHeaders)
        .send({
          preferredVehicleType: 'MOTO',
          maxPrice: 12.50,
          minRating: 4.0,
          maxDistance: 10
        })
        .expect(200)

      console.log('✅ Phase 3: Matching system completed')

      // ========================================================================
      // PHASE 4: FLEET MANAGEMENT
      // ========================================================================
      
      console.log('🚀 Phase 4: Testing fleet management...')

      // Get fleet overview
      const fleetOverviewResponse = await request(app)
        .get('/api/fleet-management/overview')
        .set(merchantAuthHeaders)
        .expect(200)

      expect(fleetOverviewResponse.body.success).toBe(true)
      expect(fleetOverviewResponse.body.overview).toHaveProperty('fleet')
      expect(fleetOverviewResponse.body.overview).toHaveProperty('operations')
      expect(fleetOverviewResponse.body.overview).toHaveProperty('performance')

      // Get fleet analytics
      const analyticsResponse = await request(app)
        .get('/api/fleet-management/analytics')
        .set(merchantAuthHeaders)
        .query({
          startDate: '2024-12-01',
          endDate: '2024-12-31'
        })
        .expect(200)

      expect(analyticsResponse.body.success).toBe(true)
      expect(analyticsResponse.body.analytics).toHaveProperty('overview')
      expect(analyticsResponse.body.analytics).toHaveProperty('performance')

      // Bulk schedule shifts
      const bulkScheduleResponse = await request(app)
        .post('/api/fleet-management/bulk-schedule')
        .set(merchantAuthHeaders)
        .send({
          scheduleRequests: [
            {
              riderId: riderId,
              timeSlot: {
                startTime: `${tomorrowStr}T14:00:00Z`,
                endTime: `${tomorrowStr}T16:00:00Z`
              },
              shiftType: 'regular'
            }
          ]
        })
        .expect(200)

      expect(bulkScheduleResponse.body.success).toBe(true)
      expect(bulkScheduleResponse.body.results).toHaveProperty('successful')
      expect(bulkScheduleResponse.body.results).toHaveProperty('failed')

      // Get compliance dashboard
      const complianceResponse = await request(app)
        .get('/api/fleet-management/compliance')
        .set(merchantAuthHeaders)
        .expect(200)

      expect(complianceResponse.body.success).toBe(true)
      expect(complianceResponse.body.compliance).toHaveProperty('overview')
      expect(complianceResponse.body.compliance).toHaveProperty('documents')

      console.log('✅ Phase 4: Fleet management completed')

      // ========================================================================
      // PHASE 5: INTEGRATION VERIFICATION
      // ========================================================================
      
      console.log('🚀 Phase 5: Verifying system integration...')

      // Verify rider can see their schedule
      const scheduleResponse = await request(app)
        .get('/api/availability/schedule')
        .set(riderAuthHeaders)
        .query({
          startDate: tomorrowStr,
          endDate: tomorrowStr
        })
        .expect(200)

      expect(scheduleResponse.body.success).toBe(true)
      expect(scheduleResponse.body.schedule).toHaveProperty('schedule')
      expect(scheduleResponse.body.schedule).toHaveProperty('summary')

      // Verify merchant can see fleet riders
      const ridersResponse = await request(app)
        .get('/api/fleet-management/riders')
        .set(merchantAuthHeaders)
        .expect(200)

      expect(ridersResponse.body.success).toBe(true)
      expect(Array.isArray(ridersResponse.body.riders)).toBe(true)

      // Verify matching analytics
      const matchingAnalyticsResponse = await request(app)
        .get('/api/matching/analytics')
        .set(merchantAuthHeaders)
        .query({
          startDate: '2024-12-01',
          endDate: '2024-12-31'
        })
        .expect(200)

      expect(matchingAnalyticsResponse.body.success).toBe(true)
      expect(matchingAnalyticsResponse.body.analytics).toHaveProperty('overview')

      console.log('✅ Phase 5: System integration verified')

      console.log('🎉 Complete rider lifecycle test passed!')
    })
  })

  describe('🔒 Security and Performance', () => {
    it('should handle concurrent operations safely', async () => {
      const promises = []

      // Simulate concurrent availability updates
      for (let i = 0; i < 5; i++) {
        promises.push(
          request(app)
            .post('/api/availability/slots')
            .set(riderAuthHeaders)
            .send({
              slots: [
                {
                  startTime: `2024-12-25T${10 + i}:00:00Z`,
                  endTime: `2024-12-25T${11 + i}:00:00Z`,
                  type: 'specific'
                }
              ]
            })
        )
      }

      const responses = await Promise.all(promises)

      // All should succeed or fail gracefully
      responses.forEach(response => {
        expect([200, 201, 400, 409]).toContain(response.status)
      })
    })

    it('should enforce rate limits', async () => {
      // This test would verify rate limiting in a real scenario
      const response = await request(app)
        .get('/api/fleet-management/overview')
        .set(merchantAuthHeaders)

      expect([200, 429]).toContain(response.status)
    })

    it('should validate input data properly', async () => {
      // Test with invalid data
      await request(app)
        .post('/api/availability/weekly')
        .set(riderAuthHeaders)
        .send({
          weeklyPattern: {
            invalidDay: { available: true, hours: [] }
          }
        })
        .expect(400)

      // Test with missing required fields
      await request(app)
        .post('/api/matching/find-matches')
        .set(merchantAuthHeaders)
        .send({
          // Missing required fields
          vehicleType: 'MOTO'
        })
        .expect(400)
    })
  })

  describe('📊 Performance Metrics', () => {
    it('should provide comprehensive analytics', async () => {
      // Test availability analytics
      const availabilityAnalytics = await request(app)
        .get('/api/availability/analytics')
        .set(riderAuthHeaders)
        .query({
          startDate: '2024-12-01',
          endDate: '2024-12-31'
        })
        .expect(200)

      expect(availabilityAnalytics.body.success).toBe(true)
      expect(availabilityAnalytics.body.analytics).toHaveProperty('summary')

      // Test matching insights
      const matchingInsights = await request(app)
        .get('/api/matching/insights/rider-performance')
        .set(merchantAuthHeaders)
        .query({ riderId: riderId })
        .expect(200)

      expect(matchingInsights.body.success).toBe(true)
      expect(matchingInsights.body.insights).toHaveProperty('overallScore')

      // Test fleet reports
      const fleetReport = await request(app)
        .post('/api/fleet-management/reports/generate')
        .set(merchantAuthHeaders)
        .send({
          reportType: 'performance',
          startDate: '2024-12-01',
          endDate: '2024-12-31',
          format: 'json'
        })
        .expect(200)

      expect(fleetReport.body.success).toBe(true)
      expect(fleetReport.body.report).toHaveProperty('data')
    })
  })

  describe('🔧 Error Handling', () => {
    it('should handle service failures gracefully', async () => {
      // Test with non-existent rider
      await request(app)
        .get('/api/availability/rider/non-existent-rider')
        .set(merchantAuthHeaders)
        .query({
          startDate: '2024-12-01',
          endDate: '2024-12-31'
        })
        .expect(500)

      // Test with invalid session ID
      await request(app)
        .get('/api/rider-onboarding/status/invalid-session')
        .set(riderAuthHeaders)
        .expect(404)
    })

    it('should provide meaningful error messages', async () => {
      const response = await request(app)
        .post('/api/matching/find-matches')
        .set(merchantAuthHeaders)
        .send({
          // Missing required fields
        })

      expect(response.body.success).toBe(false)
      expect(response.body.message).toBeDefined()
      expect(typeof response.body.message).toBe('string')
    })
  })

  describe('🎯 Business Logic Validation', () => {
    it('should enforce BeMyRider business rules', async () => {
      // Test hourly rate limits (max €12.50)
      await request(app)
        .post('/api/rider-onboarding/vehicle-info')
        .set(riderAuthHeaders)
        .send({
          sessionId: 'test-session',
          vehicleType: 'MOTO',
          make: 'Honda',
          model: 'SH 150',
          year: '2020',
          licensePlate: 'AB123CD',
          hourlyRate: 15.00 // Above maximum
        })
        .expect(400)

      // Test shift duration limits (1-2 hours)
      await request(app)
        .post('/api/availability/slots')
        .set(riderAuthHeaders)
        .send({
          slots: [
            {
              startTime: '2024-12-25T10:00:00Z',
              endTime: '2024-12-25T13:00:00Z', // 3 hours - too long
              type: 'specific'
            }
          ]
        })
        .expect(400)
    })

    it('should calculate pricing correctly', async () => {
      // This would test the pricing calculation logic
      // For now, just verify the structure is correct
      const response = await request(app)
        .post('/api/matching/calculate-score')
        .set(merchantAuthHeaders)
        .send({
          riderId: riderId,
          bookingRequest: {
            startTime: '2024-12-25T10:00:00Z',
            endTime: '2024-12-25T11:00:00Z',
            location: { lat: 45.4642, lng: 9.1900 },
            budget: 12.00
          }
        })
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.scoring).toHaveProperty('totalScore')
      expect(response.body.scoring).toHaveProperty('breakdown')
    })
  })
})
