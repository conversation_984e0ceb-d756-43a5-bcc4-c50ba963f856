/**
 * 404 Not Found Page
 * Error page for non-existent routes
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { Link } from 'react-router-dom'
import { motion } from 'framer-motion'
import { HomeIcon, ArrowLeftIcon } from '@heroicons/react/24/outline'

import But<PERSON> from '@components/ui/Button'

const NotFoundPage = () => {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center max-w-md mx-auto"
      >
        <div className="mb-8">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: "spring", bounce: 0.5 }}
            className="text-9xl font-bold text-bemyrider-600 mb-4"
          >
            404
          </motion.div>
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Pagina non trovata
          </h1>
          <p className="text-gray-600 mb-8">
            La pagina che stai cercando non esiste o è stata spostata.
          </p>
        </div>

        <div className="space-y-4">
          <Link to="/">
            <Button 
              variant="bemyrider" 
              size="lg" 
              fullWidth
              leftIcon={<HomeIcon className="w-5 h-5" />}
            >
              Torna alla Dashboard
            </Button>
          </Link>
          
          <Button 
            variant="outline" 
            size="lg" 
            fullWidth
            onClick={() => window.history.back()}
            leftIcon={<ArrowLeftIcon className="w-5 h-5" />}
          >
            Torna Indietro
          </Button>
        </div>

        <div className="mt-12">
          <div className="w-16 h-16 bg-bemyrider-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <span className="text-2xl font-bold text-bemyrider-600">BR</span>
          </div>
          <p className="text-sm text-gray-500">
            © 2024 BeMyRider. Tutti i diritti riservati.
          </p>
        </div>
      </motion.div>
    </div>
  )
}

export default NotFoundPage
