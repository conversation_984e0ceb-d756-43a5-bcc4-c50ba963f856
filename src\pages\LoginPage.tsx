import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { ArrowLeft } from 'lucide-react'
import { SignIn } from '@clerk/clerk-react'

const LoginPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 bg-white shadow-sm">
        <Link to="/" className="flex items-center text-gray-600">
          <ArrowLeft className="w-5 h-5 mr-2" />
          Torna alla home
        </Link>
        <h1 className="text-lg font-semibold bemyrider-logo">bemyrider</h1>
      </div>

      {/* Login Form */}
      <div className="flex-1 flex items-center justify-center p-6">
        <div className="w-full max-w-md">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Accedi al tuo account
            </h2>
            <p className="text-gray-600">
              Benvenuto su bemyrider
            </p>
          </div>

          <SignIn
            appearance={{
              elements: {
                formButtonPrimary: 'bg-purple-600 hover:bg-purple-700',
                card: 'shadow-lg',
              }
            }}
            redirectUrl="/app/dashboard"
            signUpUrl="/"
          />
        </div>
      </div>
    </div>
  )
}

export default LoginPage
