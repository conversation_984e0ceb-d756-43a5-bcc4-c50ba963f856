/**
 * Booking Flow Integration Tests
 * End-to-end booking process testing
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest'
import request from 'supertest'
import { createTestApp } from '../helpers/test-app.js'

describe('Booking Flow Integration', () => {
  let app
  let clientAuthHeaders
  let riderAuthHeaders
  let testBooking

  beforeAll(async () => {
    app = await createTestApp()
    
    // Setup auth headers for different user types
    clientAuthHeaders = global.createAuthHeaders('test_client_id')
    riderAuthHeaders = global.createAuthHeaders('test_rider_id')

    // Setup test users
    global.testDb.addUser({
      id: 'test_client_id',
      clerk_id: 'test_client_clerk',
      email: '<EMAIL>',
      type: 'ESERCENTE',
      first_name: 'Test',
      last_name: 'Client',
      profile_complete: true
    })

    global.testDb.addUser({
      id: 'test_rider_id',
      clerk_id: 'test_rider_clerk',
      email: '<EMAIL>',
      type: 'RIDER',
      first_name: 'Test',
      last_name: 'Rider',
      vehicle_type: 'MOTO',
      hourly_rate: 25.00,
      is_available: true,
      profile_complete: true
    })
  })

  afterAll(async () => {
    if (global.testDb) {
      global.testDb.clear()
    }
  })

  beforeEach(() => {
    // Clear bookings before each test
    if (global.testDb) {
      global.testDb.bookings.clear()
    }
  })

  describe('🎯 Complete Booking Flow', () => {
    it('should complete full booking lifecycle', async () => {
      // Step 1: Get pricing estimate
      const estimateData = {
        serviceType: 'STANDARD_DELIVERY',
        vehicleType: 'MOTO',
        duration: 120, // 2 hours
        pickupCoordinates: { latitude: 45.4642, longitude: 9.1900 },
        deliveryCoordinates: { latitude: 45.4654, longitude: 9.1859 },
        scheduledDate: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString() // 2 hours from now
      }

      const estimateResponse = await request(app)
        .post('/api/bookings/estimate')
        .send(estimateData)
        .expect(200)

      expect(estimateResponse.body.success).toBe(true)
      expect(estimateResponse.body.estimate).toHaveProperty('estimatedTotal')
      expect(estimateResponse.body.estimate.estimatedTotal).toBeGreaterThan(0)

      // Step 2: Create booking
      const bookingData = {
        ...estimateData,
        pickupAddress: 'Via Roma 1, Milano',
        deliveryAddress: 'Via Milano 10, Milano',
        specialInstructions: 'Test booking instructions'
      }

      const createResponse = await request(app)
        .post('/api/bookings')
        .set(clientAuthHeaders)
        .send(bookingData)
        .expect(201)

      expect(createResponse.body.success).toBe(true)
      expect(createResponse.body.booking).toHaveProperty('id')
      expect(createResponse.body.booking.status).toBe('PENDING')
      expect(createResponse.body.booking).toHaveProperty('payment_client_secret')

      testBooking = createResponse.body.booking

      // Step 3: Rider accepts booking
      const acceptResponse = await request(app)
        .post(`/api/bookings/${testBooking.id}/accept`)
        .set(riderAuthHeaders)
        .expect(200)

      expect(acceptResponse.body.success).toBe(true)
      expect(acceptResponse.body.booking.status).toBe('ACCEPTED')
      expect(acceptResponse.body.booking.rider_id).toBe('test_rider_id')

      // Step 4: Rider starts service
      const startData = {
        location: 'Via Roma 1, Milano',
        coordinates: { latitude: 45.4642, longitude: 9.1900 }
      }

      const startResponse = await request(app)
        .post(`/api/bookings/${testBooking.id}/start`)
        .set(riderAuthHeaders)
        .send(startData)
        .expect(200)

      expect(startResponse.body.success).toBe(true)
      expect(startResponse.body.booking.status).toBe('IN_PROGRESS')
      expect(startResponse.body.booking).toHaveProperty('actual_start')

      // Step 5: Rider completes service
      const completionData = {
        location: 'Via Milano 10, Milano',
        coordinates: { latitude: 45.4654, longitude: 9.1859 },
        notes: 'Service completed successfully'
      }

      const completeResponse = await request(app)
        .post(`/api/bookings/${testBooking.id}/complete`)
        .set(riderAuthHeaders)
        .send(completionData)
        .expect(200)

      expect(completeResponse.body.success).toBe(true)
      expect(completeResponse.body.booking.status).toBe('COMPLETED')
      expect(completeResponse.body.booking).toHaveProperty('actual_end')
      expect(completeResponse.body.booking.payment_status).toBe('COMPLETED')

      // Step 6: Verify booking details
      const getResponse = await request(app)
        .get(`/api/bookings/${testBooking.id}`)
        .set(clientAuthHeaders)
        .expect(200)

      expect(getResponse.body.success).toBe(true)
      expect(getResponse.body.booking.status).toBe('COMPLETED')
    })

    it('should handle booking cancellation by client', async () => {
      // Create a booking first
      const bookingData = {
        serviceType: 'STANDARD_DELIVERY',
        vehicleType: 'MOTO',
        duration: 60,
        pickupAddress: 'Via Roma 1, Milano',
        deliveryAddress: 'Via Milano 10, Milano',
        pickupCoordinates: { latitude: 45.4642, longitude: 9.1900 },
        deliveryCoordinates: { latitude: 45.4654, longitude: 9.1859 },
        scheduledDate: new Date(Date.now() + 3 * 60 * 60 * 1000).toISOString() // 3 hours from now
      }

      const createResponse = await request(app)
        .post('/api/bookings')
        .set(clientAuthHeaders)
        .send(bookingData)
        .expect(201)

      const bookingId = createResponse.body.booking.id

      // Cancel the booking
      const cancelResponse = await request(app)
        .post(`/api/bookings/${bookingId}/cancel`)
        .set(clientAuthHeaders)
        .send({ reason: 'Changed plans' })
        .expect(200)

      expect(cancelResponse.body.success).toBe(true)
      expect(cancelResponse.body.booking.status).toBe('CANCELLED')
      expect(cancelResponse.body.booking.cancellation_reason).toBe('Changed plans')
    })

    it('should handle booking cancellation by rider', async () => {
      // Create and accept a booking
      const bookingData = {
        serviceType: 'STANDARD_DELIVERY',
        vehicleType: 'MOTO',
        duration: 60,
        pickupAddress: 'Via Roma 1, Milano',
        deliveryAddress: 'Via Milano 10, Milano',
        pickupCoordinates: { latitude: 45.4642, longitude: 9.1900 },
        deliveryCoordinates: { latitude: 45.4654, longitude: 9.1859 },
        scheduledDate: new Date(Date.now() + 1 * 60 * 60 * 1000).toISOString() // 1 hour from now
      }

      const createResponse = await request(app)
        .post('/api/bookings')
        .set(clientAuthHeaders)
        .send(bookingData)
        .expect(201)

      const bookingId = createResponse.body.booking.id

      // Rider accepts
      await request(app)
        .post(`/api/bookings/${bookingId}/accept`)
        .set(riderAuthHeaders)
        .expect(200)

      // Rider cancels
      const cancelResponse = await request(app)
        .post(`/api/bookings/${bookingId}/cancel`)
        .set(riderAuthHeaders)
        .send({ reason: 'Emergency situation' })
        .expect(200)

      expect(cancelResponse.body.success).toBe(true)
      expect(cancelResponse.body.booking.status).toBe('CANCELLED')
      expect(cancelResponse.body.booking.cancelled_by).toBe('test_rider_id')
    })
  })

  describe('🔒 Authorization Tests', () => {
    it('should prevent unauthorized booking access', async () => {
      // Create booking as client
      const bookingData = {
        serviceType: 'STANDARD_DELIVERY',
        vehicleType: 'MOTO',
        duration: 60,
        pickupAddress: 'Via Roma 1, Milano',
        deliveryAddress: 'Via Milano 10, Milano',
        pickupCoordinates: { latitude: 45.4642, longitude: 9.1900 },
        deliveryCoordinates: { latitude: 45.4654, longitude: 9.1859 },
        scheduledDate: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString()
      }

      const createResponse = await request(app)
        .post('/api/bookings')
        .set(clientAuthHeaders)
        .send(bookingData)
        .expect(201)

      const bookingId = createResponse.body.booking.id

      // Try to access with different user
      const otherUserHeaders = global.createAuthHeaders('other_user_id')
      
      await request(app)
        .get(`/api/bookings/${bookingId}`)
        .set(otherUserHeaders)
        .expect(403)
    })

    it('should prevent non-riders from accepting bookings', async () => {
      // Create booking
      const bookingData = {
        serviceType: 'STANDARD_DELIVERY',
        vehicleType: 'MOTO',
        duration: 60,
        pickupAddress: 'Via Roma 1, Milano',
        deliveryAddress: 'Via Milano 10, Milano',
        pickupCoordinates: { latitude: 45.4642, longitude: 9.1900 },
        deliveryCoordinates: { latitude: 45.4654, longitude: 9.1859 },
        scheduledDate: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString()
      }

      const createResponse = await request(app)
        .post('/api/bookings')
        .set(clientAuthHeaders)
        .send(bookingData)
        .expect(201)

      const bookingId = createResponse.body.booking.id

      // Try to accept as client (not rider)
      await request(app)
        .post(`/api/bookings/${bookingId}/accept`)
        .set(clientAuthHeaders)
        .expect(400)
    })
  })

  describe('📊 Pricing Tests', () => {
    it('should calculate pricing correctly with dynamic factors', async () => {
      // Test weekend pricing
      const weekendDate = new Date()
      weekendDate.setDate(weekendDate.getDate() + (6 - weekendDate.getDay())) // Next Saturday
      weekendDate.setHours(14, 0, 0, 0) // 2 PM

      const weekendEstimate = await request(app)
        .post('/api/bookings/estimate')
        .send({
          serviceType: 'STANDARD_DELIVERY',
          vehicleType: 'MOTO',
          duration: 120,
          pickupCoordinates: { latitude: 45.4642, longitude: 9.1900 },
          deliveryCoordinates: { latitude: 45.4654, longitude: 9.1859 },
          scheduledDate: weekendDate.toISOString()
        })
        .expect(200)

      expect(weekendEstimate.body.estimate.dynamicPricing).toBe(true)

      // Test peak hours pricing
      const peakHourDate = new Date()
      peakHourDate.setDate(peakHourDate.getDate() + 1) // Tomorrow
      peakHourDate.setHours(19, 0, 0, 0) // 7 PM (peak hour)

      const peakEstimate = await request(app)
        .post('/api/bookings/estimate')
        .send({
          serviceType: 'STANDARD_DELIVERY',
          vehicleType: 'MOTO',
          duration: 120,
          pickupCoordinates: { latitude: 45.4642, longitude: 9.1900 },
          deliveryCoordinates: { latitude: 45.4654, longitude: 9.1859 },
          scheduledDate: peakHourDate.toISOString()
        })
        .expect(200)

      expect(peakEstimate.body.estimate.dynamicPricing).toBe(true)
    })

    it('should apply minimum booking amount', async () => {
      // Very short booking that should hit minimum
      const shortEstimate = await request(app)
        .post('/api/bookings/estimate')
        .send({
          serviceType: 'STANDARD_DELIVERY',
          vehicleType: 'BICI',
          duration: 30, // 30 minutes
          pickupCoordinates: { latitude: 45.4642, longitude: 9.1900 },
          deliveryCoordinates: { latitude: 45.4642, longitude: 9.1900 }, // Same location
          scheduledDate: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString()
        })
        .expect(200)

      expect(shortEstimate.body.estimate.estimatedTotal).toBeGreaterThanOrEqual(10.00) // Minimum €10
    })
  })

  describe('⏰ Timing Validation', () => {
    it('should reject bookings scheduled too soon', async () => {
      const tooSoonDate = new Date(Date.now() + 15 * 60 * 1000) // 15 minutes from now

      await request(app)
        .post('/api/bookings')
        .set(clientAuthHeaders)
        .send({
          serviceType: 'STANDARD_DELIVERY',
          vehicleType: 'MOTO',
          duration: 60,
          pickupAddress: 'Via Roma 1, Milano',
          deliveryAddress: 'Via Milano 10, Milano',
          pickupCoordinates: { latitude: 45.4642, longitude: 9.1900 },
          deliveryCoordinates: { latitude: 45.4654, longitude: 9.1859 },
          scheduledDate: tooSoonDate.toISOString()
        })
        .expect(400)
    })

    it('should reject bookings with invalid duration', async () => {
      await request(app)
        .post('/api/bookings')
        .set(clientAuthHeaders)
        .send({
          serviceType: 'STANDARD_DELIVERY',
          vehicleType: 'MOTO',
          duration: 15, // Too short (minimum 30 minutes)
          pickupAddress: 'Via Roma 1, Milano',
          deliveryAddress: 'Via Milano 10, Milano',
          pickupCoordinates: { latitude: 45.4642, longitude: 9.1900 },
          deliveryCoordinates: { latitude: 45.4654, longitude: 9.1859 },
          scheduledDate: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString()
        })
        .expect(400)
    })
  })
})
