import React from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { Search, Heart, Star, MessageCircle, Menu } from 'lucide-react'

const BottomNavigation: React.FC = () => {
  const navigate = useNavigate()
  const location = useLocation()

  const navItems = [
    { icon: Search, label: 'Cerca', path: '/app/riders' },
    { icon: Heart, label: 'Preferiti', path: '/app/favorites' },
    { icon: Star, label: 'Recensioni', path: '/app/reviews' },
    { icon: MessageCircle, label: 'Chat', path: '/app/messages' },
    { icon: Menu, label: 'Menu', path: '/app/profile' },
  ]

  return (
    <nav className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2">
      <div className="flex justify-around">
        {navItems.map((item) => {
          const Icon = item.icon
          const isActive = location.pathname === item.path
          
          return (
            <button
              key={item.path}
              onClick={() => navigate(item.path)}
              className={`flex flex-col items-center py-2 px-3 rounded-lg transition-colors ${
                isActive 
                  ? 'text-purple-600 bg-purple-50' 
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <Icon size={20} />
              <span className="text-xs mt-1">{item.label}</span>
            </button>
          )
        })}
      </div>
    </nav>
  )
}

export default BottomNavigation
