{"numTotalTestSuites": 13, "numPassedTestSuites": 0, "numFailedTestSuites": 13, "numPendingTestSuites": 0, "numTotalTests": 35, "numPassedTests": 2, "numFailedTests": 33, "numPendingTests": 0, "numTodoTests": 0, "snapshot": {"added": 0, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0, "didUpdate": false}, "startTime": 1750155143835, "success": false, "testResults": [{"assertionResults": [{"ancestorTitles": ["Booking Flow Integration", "🎯 Complete Booking Flow"], "fullName": "Booking Flow Integration 🎯 Complete Booking Flow should complete full booking lifecycle", "status": "failed", "title": "should complete full booking lifecycle", "duration": 63.81230000000005, "failureMessages": ["Error: expected 200 \"OK\", got 404 \"Not Found\"\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\tests\\integration\\booking-flow.test.js:78:10\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1894:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1860:10)\n    at runTest (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1571:12)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n----\n    at Test._assertStatus (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:267:14)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:323:13\n    at Test._assertFunction (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:300:13)\n    at Test.assert (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:179:23)\n    at Server.localAssert (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:135:14)\n    at Object.onceWrapper (node:events:632:28)\n    at Server.emit (node:events:518:28)\n    at emitCloseNT (node:net:2418:8)\n    at processTicksAndRejections (node:internal/process/task_queues:89:21)"], "location": {"line": 64, "column": 7}, "meta": {}}, {"ancestorTitles": ["Booking Flow Integration", "🎯 Complete Booking Flow"], "fullName": "Booking Flow Integration 🎯 Complete Booking Flow should handle booking cancellation by client", "status": "failed", "title": "should handle booking cancellation by client", "duration": 8.580199999999877, "failureMessages": ["Error: expected 201 \"Created\", got 404 \"Not Found\"\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\tests\\integration\\booking-flow.test.js:176:10\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1894:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1860:10)\n    at runTest (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1571:12)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n----\n    at Test._assertStatus (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:267:14)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:323:13\n    at Test._assertFunction (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:300:13)\n    at Test.assert (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:179:23)\n    at Server.localAssert (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:135:14)\n    at Object.onceWrapper (node:events:632:28)\n    at Server.emit (node:events:518:28)\n    at emitCloseNT (node:net:2418:8)\n    at processTicksAndRejections (node:internal/process/task_queues:89:21)"], "location": {"line": 159, "column": 7}, "meta": {}}, {"ancestorTitles": ["Booking Flow Integration", "🎯 Complete Booking Flow"], "fullName": "Booking Flow Integration 🎯 Complete Booking Flow should handle booking cancellation by rider", "status": "failed", "title": "should handle booking cancellation by rider", "duration": 6.790399999999863, "failureMessages": ["Error: expected 201 \"Created\", got 404 \"Not Found\"\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\tests\\integration\\booking-flow.test.js:209:10\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1894:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1860:10)\n    at runTest (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1571:12)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n----\n    at Test._assertStatus (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:267:14)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:323:13\n    at Test._assertFunction (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:300:13)\n    at Test.assert (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:179:23)\n    at Server.localAssert (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:135:14)\n    at Object.onceWrapper (node:events:632:28)\n    at Server.emit (node:events:518:28)\n    at emitCloseNT (node:net:2418:8)\n    at processTicksAndRejections (node:internal/process/task_queues:89:21)"], "location": {"line": 192, "column": 7}, "meta": {}}, {"ancestorTitles": ["Booking Flow Integration", "🔒 Authorization Tests"], "fullName": "Booking Flow Integration 🔒 Authorization Tests should prevent unauthorized booking access", "status": "failed", "title": "should prevent unauthorized booking access", "duration": 15.58900000000017, "failureMessages": ["Error: expected 201 \"Created\", got 404 \"Not Found\"\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\tests\\integration\\booking-flow.test.js:250:10\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1894:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1860:10)\n    at runTest (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1571:12)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n----\n    at Test._assertStatus (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:267:14)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:323:13\n    at Test._assertFunction (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:300:13)\n    at Test.assert (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:179:23)\n    at Server.localAssert (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:135:14)\n    at Object.onceWrapper (node:events:632:28)\n    at Server.emit (node:events:518:28)\n    at emitCloseNT (node:net:2418:8)\n    at processTicksAndRejections (node:internal/process/task_queues:89:21)"], "location": {"line": 233, "column": 7}, "meta": {}}, {"ancestorTitles": ["Booking Flow Integration", "🔒 Authorization Tests"], "fullName": "Booking Flow Integration 🔒 Authorization Tests should prevent non-riders from accepting bookings", "status": "failed", "title": "should prevent non-riders from accepting bookings", "duration": 7.552999999999884, "failureMessages": ["Error: expected 201 \"Created\", got 404 \"Not Found\"\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\tests\\integration\\booking-flow.test.js:280:10\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1894:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1860:10)\n    at runTest (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1571:12)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n----\n    at Test._assertStatus (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:267:14)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:323:13\n    at Test._assertFunction (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:300:13)\n    at Test.assert (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:179:23)\n    at Server.localAssert (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:135:14)\n    at Object.onceWrapper (node:events:632:28)\n    at Server.emit (node:events:518:28)\n    at emitCloseNT (node:net:2418:8)\n    at processTicksAndRejections (node:internal/process/task_queues:89:21)"], "location": {"line": 263, "column": 7}, "meta": {}}, {"ancestorTitles": ["Booking Flow Integration", "📊 Pricing Tests"], "fullName": "Booking Flow Integration 📊 Pricing Tests should calculate pricing correctly with dynamic factors", "status": "failed", "title": "should calculate pricing correctly with dynamic factors", "duration": 9.934300000000121, "failureMessages": ["Error: expected 200 \"OK\", got 404 \"Not Found\"\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\tests\\integration\\booking-flow.test.js:309:10\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1894:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1860:10)\n    at runTest (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1571:12)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n----\n    at Test._assertStatus (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:267:14)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:323:13\n    at Test._assertFunction (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:300:13)\n    at Test.assert (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:179:23)\n    at Server.localAssert (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:135:14)\n    at Object.onceWrapper (node:events:632:28)\n    at Server.emit (node:events:518:28)\n    at emitCloseNT (node:net:2418:8)\n    at processTicksAndRejections (node:internal/process/task_queues:89:21)"], "location": {"line": 293, "column": 7}, "meta": {}}, {"ancestorTitles": ["Booking Flow Integration", "📊 Pricing Tests"], "fullName": "Booking Flow Integration 📊 Pricing Tests should apply minimum booking amount", "status": "failed", "title": "should apply minimum booking amount", "duration": 7.830600000000004, "failureMessages": ["Error: expected 200 \"OK\", got 404 \"Not Found\"\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\tests\\integration\\booking-flow.test.js:345:10\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1894:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1860:10)\n    at runTest (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1571:12)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n----\n    at Test._assertStatus (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:267:14)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:323:13\n    at Test._assertFunction (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:300:13)\n    at Test.assert (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:179:23)\n    at Server.localAssert (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:135:14)\n    at Object.onceWrapper (node:events:632:28)\n    at Server.emit (node:events:518:28)\n    at emitCloseNT (node:net:2418:8)\n    at processTicksAndRejections (node:internal/process/task_queues:89:21)"], "location": {"line": 333, "column": 7}, "meta": {}}, {"ancestorTitles": ["Booking Flow Integration", "⏰ Timing Validation"], "fullName": "Booking Flow Integration ⏰ Timing Validation should reject bookings scheduled too soon", "status": "failed", "title": "should reject bookings scheduled too soon", "duration": 6.505399999999781, "failureMessages": ["Error: expected 400 \"Bad Request\", got 404 \"Not Found\"\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\tests\\integration\\booking-flow.test.js:368:10\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1894:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1860:10)\n    at runTest (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1571:12)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n----\n    at Test._assertStatus (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:267:14)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:323:13\n    at Test._assertFunction (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:300:13)\n    at Test.assert (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:179:23)\n    at Server.localAssert (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:135:14)\n    at Object.onceWrapper (node:events:632:28)\n    at Server.emit (node:events:518:28)\n    at emitCloseNT (node:net:2418:8)\n    at processTicksAndRejections (node:internal/process/task_queues:89:21)"], "location": {"line": 352, "column": 7}, "meta": {}}, {"ancestorTitles": ["Booking Flow Integration", "⏰ Timing Validation"], "fullName": "Booking Flow Integration ⏰ Timing Validation should reject bookings with invalid duration", "status": "failed", "title": "should reject bookings with invalid duration", "duration": 17.624000000000024, "failureMessages": ["Error: expected 400 \"Bad Request\", got 404 \"Not Found\"\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\tests\\integration\\booking-flow.test.js:385:10\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1894:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1860:10)\n    at runTest (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1571:12)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n----\n    at Test._assertStatus (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:267:14)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:323:13\n    at Test._assertFunction (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:300:13)\n    at Test.assert (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:179:23)\n    at Server.localAssert (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:135:14)\n    at Object.onceWrapper (node:events:632:28)\n    at Server.emit (node:events:518:28)\n    at emitCloseNT (node:net:2418:8)\n    at processTicksAndRejections (node:internal/process/task_queues:89:21)"], "location": {"line": 371, "column": 7}, "meta": {}}], "startTime": 1750155146817, "endTime": 1750155146962.624, "status": "failed", "message": "", "name": "C:/Users/<USER>/Documents/augment-projects/bemyrider/tests/integration/booking-flow.test.js"}, {"assertionResults": [{"ancestorTitles": ["Users API Integration Tests", "GET /api/users/profile/:clerkId"], "fullName": "Users API Integration Tests GET /api/users/profile/:clerkId should get user profile successfully with valid auth", "status": "failed", "title": "should get user profile successfully with valid auth", "duration": 38.076699999999846, "failureMessages": ["Error: expected 200 \"OK\", got 403 \"Forbidden\"\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\tests\\integration\\api\\users.test.js:45:10\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1894:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1860:10)\n    at runTest (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1571:12)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n----\n    at Test._assertStatus (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:267:14)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:323:13\n    at Test._assertFunction (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:300:13)\n    at Test.assert (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:179:23)\n    at Server.localAssert (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:135:14)\n    at Object.onceWrapper (node:events:632:28)\n    at Server.emit (node:events:518:28)\n    at emitCloseNT (node:net:2418:8)\n    at processTicksAndRejections (node:internal/process/task_queues:89:21)"], "location": {"line": 41, "column": 7}, "meta": {}}, {"ancestorTitles": ["Users API Integration Tests", "GET /api/users/profile/:clerkId"], "fullName": "Users API Integration Tests GET /api/users/profile/:clerkId should return 401 without authentication", "status": "passed", "title": "should return 401 without authentication", "duration": 7.111400000000003, "failureMessages": [], "location": {"line": 62, "column": 7}, "meta": {}}, {"ancestorTitles": ["Users API Integration Tests", "GET /api/users/profile/:clerkId"], "fullName": "Users API Integration Tests GET /api/users/profile/:clerkId should return 403 when accessing other user profile", "status": "passed", "title": "should return 403 when accessing other user profile", "duration": 5.176199999999881, "failureMessages": [], "location": {"line": 73, "column": 7}, "meta": {}}, {"ancestorTitles": ["Users API Integration Tests", "GET /api/users/profile/:clerkId"], "fullName": "Users API Integration Tests GET /api/users/profile/:clerkId should return 404 for non-existent user", "status": "failed", "title": "should return 404 for non-existent user", "duration": 11.408200000000079, "failureMessages": ["Error: expected 404 \"Not Found\", got 403 \"Forbidden\"\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\tests\\integration\\api\\users.test.js:92:10\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1894:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1860:10)\n    at runTest (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1571:12)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n----\n    at Test._assertStatus (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:267:14)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:323:13\n    at Test._assertFunction (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:300:13)\n    at Test.assert (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:179:23)\n    at Server.localAssert (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:135:14)\n    at Object.onceWrapper (node:events:632:28)\n    at Server.emit (node:events:518:28)\n    at emitCloseNT (node:net:2418:8)\n    at processTicksAndRejections (node:internal/process/task_queues:89:21)"], "location": {"line": 88, "column": 7}, "meta": {}}, {"ancestorTitles": ["Users API Integration Tests", "GET /api/users/profile/:clerkId"], "fullName": "Users API Integration Tests GET /api/users/profile/:clerkId should handle database errors gracefully", "status": "failed", "title": "should handle database errors gracefully", "duration": 5.852300000000014, "failureMessages": ["Error: expected 500 \"Internal Server Error\", got 403 \"Forbidden\"\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\tests\\integration\\api\\users.test.js:110:10\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1894:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1860:10)\n    at runTest (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1571:12)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n----\n    at Test._assertStatus (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:267:14)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:323:13\n    at Test._assertFunction (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:300:13)\n    at Test.assert (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:179:23)\n    at Server.localAssert (C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\node_modules\\supertest\\lib\\test.js:135:14)\n    at Object.onceWrapper (node:events:632:28)\n    at Server.emit (node:events:518:28)\n    at emitCloseNT (node:net:2418:8)\n    at processTicksAndRejections (node:internal/process/task_queues:89:21)"], "location": {"line": 100, "column": 7}, "meta": {}}, {"ancestorTitles": ["Users API Integration Tests", "POST /api/users/sync"], "fullName": "Users API Integration Tests POST /api/users/sync should sync new user successfully", "status": "failed", "title": "should sync new user successfully", "duration": 1.0243000000000393, "failureMessages": ["TypeError: global.testDb.clear is not a function\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\tests\\integration\\api\\users.test.js:35:21\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1894:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1860:10)\n    at runHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1433:51)\n    at callSuiteHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1439:25)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at callSuiteHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1426:21)\n    at runTest (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1563:26)"], "location": {"line": 133, "column": 7}, "meta": {}}, {"ancestorTitles": ["Users API Integration Tests", "POST /api/users/sync"], "fullName": "Users API Integration Tests POST /api/users/sync should update existing user on sync", "status": "failed", "title": "should update existing user on sync", "duration": 0.4472000000000662, "failureMessages": ["TypeError: global.testDb.clear is not a function\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\tests\\integration\\api\\users.test.js:35:21\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1894:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1860:10)\n    at runHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1433:51)\n    at callSuiteHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1439:25)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at callSuiteHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1426:21)\n    at runTest (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1563:26)"], "location": {"line": 151, "column": 7}, "meta": {}}, {"ancestorTitles": ["Users API Integration Tests", "POST /api/users/sync"], "fullName": "Users API Integration Tests POST /api/users/sync should validate required fields", "status": "failed", "title": "should validate required fields", "duration": 0.3387000000000171, "failureMessages": ["TypeError: global.testDb.clear is not a function\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\tests\\integration\\api\\users.test.js:35:21\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1894:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1860:10)\n    at runHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1433:51)\n    at callSuiteHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1439:25)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at callSuiteHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1426:21)\n    at runTest (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1563:26)"], "location": {"line": 176, "column": 7}, "meta": {}}, {"ancestorTitles": ["Users API Integration Tests", "POST /api/users/sync"], "fullName": "Users API Integration Tests POST /api/users/sync should validate email format", "status": "failed", "title": "should validate email format", "duration": 0.40629999999987376, "failureMessages": ["TypeError: global.testDb.clear is not a function\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\tests\\integration\\api\\users.test.js:35:21\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1894:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1860:10)\n    at runHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1433:51)\n    at callSuiteHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1439:25)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at callSuiteHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1426:21)\n    at runTest (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1563:26)"], "location": {"line": 198, "column": 7}, "meta": {}}, {"ancestorTitles": ["Users API Integration Tests", "POST /api/users/sync"], "fullName": "Users API Integration Tests POST /api/users/sync should prevent syncing other user data", "status": "failed", "title": "should prevent syncing other user data", "duration": 0.2602999999999156, "failureMessages": ["TypeError: global.testDb.clear is not a function\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\tests\\integration\\api\\users.test.js:35:21\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1894:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1860:10)\n    at runHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1433:51)\n    at callSuiteHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1439:25)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at callSuiteHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1426:21)\n    at runTest (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1563:26)"], "location": {"line": 215, "column": 7}, "meta": {}}, {"ancestorTitles": ["Users API Integration Tests", "POST /api/users/sync"], "fullName": "Users API Integration Tests POST /api/users/sync should handle rate limiting", "status": "failed", "title": "should handle rate limiting", "duration": 0.3022999999998319, "failureMessages": ["TypeError: global.testDb.clear is not a function\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\tests\\integration\\api\\users.test.js:35:21\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1894:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1860:10)\n    at runHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1433:51)\n    at callSuiteHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1439:25)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at callSuiteHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1426:21)\n    at runTest (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1563:26)"], "location": {"line": 233, "column": 7}, "meta": {}}, {"ancestorTitles": ["Users API Integration Tests", "PUT /api/users/profile/:clerkId"], "fullName": "Users API Integration Tests PUT /api/users/profile/:clerkId should update user profile successfully", "status": "failed", "title": "should update user profile successfully", "duration": 0.2118000000000393, "failureMessages": ["TypeError: global.testDb.clear is not a function\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\tests\\integration\\api\\users.test.js:35:21\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1894:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1860:10)\n    at runHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1433:51)\n    at callSuiteHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1439:25)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at callSuiteHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1426:21)\n    at runTest (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1563:26)"], "location": {"line": 258, "column": 7}, "meta": {}}, {"ancestorTitles": ["Users API Integration Tests", "PUT /api/users/profile/:clerkId"], "fullName": "Users API Integration Tests PUT /api/users/profile/:clerkId should validate phone number format", "status": "failed", "title": "should validate phone number format", "duration": 0.26149999999984175, "failureMessages": ["TypeError: global.testDb.clear is not a function\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\tests\\integration\\api\\users.test.js:35:21\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1894:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1860:10)\n    at runHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1433:51)\n    at callSuiteHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1439:25)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at callSuiteHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1426:21)\n    at runTest (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1563:26)"], "location": {"line": 276, "column": 7}, "meta": {}}, {"ancestorTitles": ["Users API Integration Tests", "PUT /api/users/profile/:clerkId"], "fullName": "Users API Integration Tests PUT /api/users/profile/:clerkId should validate hourly rate for riders", "status": "failed", "title": "should validate hourly rate for riders", "duration": 0.2074999999999818, "failureMessages": ["TypeError: global.testDb.clear is not a function\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\tests\\integration\\api\\users.test.js:35:21\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1894:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1860:10)\n    at runHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1433:51)\n    at callSuiteHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1439:25)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at callSuiteHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1426:21)\n    at runTest (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1563:26)"], "location": {"line": 293, "column": 7}, "meta": {}}, {"ancestorTitles": ["Users API Integration Tests", "PUT /api/users/profile/:clerkId"], "fullName": "Users API Integration Tests PUT /api/users/profile/:clerkId should prevent updating other user profiles", "status": "failed", "title": "should prevent updating other user profiles", "duration": 0.23690000000010514, "failureMessages": ["TypeError: global.testDb.clear is not a function\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\tests\\integration\\api\\users.test.js:35:21\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1894:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1860:10)\n    at runHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1433:51)\n    at callSuiteHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1439:25)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at callSuiteHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1426:21)\n    at runTest (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1563:26)"], "location": {"line": 313, "column": 7}, "meta": {}}, {"ancestorTitles": ["Users API Integration Tests", "PUT /api/users/profile/:clerkId"], "fullName": "Users API Integration Tests PUT /api/users/profile/:clerkId should sanitize input data", "status": "failed", "title": "should sanitize input data", "duration": 0.19039999999995416, "failureMessages": ["TypeError: global.testDb.clear is not a function\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\tests\\integration\\api\\users.test.js:35:21\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1894:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1860:10)\n    at runHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1433:51)\n    at callSuiteHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1439:25)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at callSuiteHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1426:21)\n    at runTest (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1563:26)"], "location": {"line": 329, "column": 7}, "meta": {}}, {"ancestorTitles": ["Users API Integration Tests", "GET /api/users/riders"], "fullName": "Users API Integration Tests GET /api/users/riders should get all available riders", "status": "failed", "title": "should get all available riders", "duration": 0.18790000000012697, "failureMessages": ["TypeError: global.testDb.clear is not a function\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\tests\\integration\\api\\users.test.js:35:21\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1894:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1860:10)\n    at runHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1433:51)\n    at callSuiteHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1439:25)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at callSuiteHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1426:21)\n    at runTest (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1563:26)"], "location": {"line": 381, "column": 7}, "meta": {}}, {"ancestorTitles": ["Users API Integration Tests", "GET /api/users/riders"], "fullName": "Users API Integration Tests GET /api/users/riders should filter riders by city", "status": "failed", "title": "should filter riders by city", "duration": 0.18329999999991742, "failureMessages": ["TypeError: global.testDb.clear is not a function\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\tests\\integration\\api\\users.test.js:35:21\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1894:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1860:10)\n    at runHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1433:51)\n    at callSuiteHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1439:25)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at callSuiteHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1426:21)\n    at runTest (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1563:26)"], "location": {"line": 401, "column": 7}, "meta": {}}, {"ancestorTitles": ["Users API Integration Tests", "GET /api/users/riders"], "fullName": "Users API Integration Tests GET /api/users/riders should filter riders by vehicle type", "status": "failed", "title": "should filter riders by vehicle type", "duration": 0.2317000000000462, "failureMessages": ["TypeError: global.testDb.clear is not a function\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\tests\\integration\\api\\users.test.js:35:21\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1894:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1860:10)\n    at runHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1433:51)\n    at callSuiteHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1439:25)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at callSuiteHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1426:21)\n    at runTest (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1563:26)"], "location": {"line": 412, "column": 7}, "meta": {}}, {"ancestorTitles": ["Users API Integration Tests", "GET /api/users/riders"], "fullName": "Users API Integration Tests GET /api/users/riders should filter riders by availability", "status": "failed", "title": "should filter riders by availability", "duration": 0.2398000000000593, "failureMessages": ["TypeError: global.testDb.clear is not a function\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\tests\\integration\\api\\users.test.js:35:21\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1894:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1860:10)\n    at runHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1433:51)\n    at callSuiteHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1439:25)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at callSuiteHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1426:21)\n    at runTest (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1563:26)"], "location": {"line": 421, "column": 7}, "meta": {}}, {"ancestorTitles": ["Users API Integration Tests", "GET /api/users/riders"], "fullName": "Users API Integration Tests GET /api/users/riders should filter riders by rate range", "status": "failed", "title": "should filter riders by rate range", "duration": 0.21829999999999927, "failureMessages": ["TypeError: global.testDb.clear is not a function\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\tests\\integration\\api\\users.test.js:35:21\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1894:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1860:10)\n    at runHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1433:51)\n    at callSuiteHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1439:25)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at callSuiteHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1426:21)\n    at runTest (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1563:26)"], "location": {"line": 431, "column": 7}, "meta": {}}, {"ancestorTitles": ["Users API Integration Tests", "GET /api/users/riders"], "fullName": "Users API Integration Tests GET /api/users/riders should support pagination", "status": "failed", "title": "should support pagination", "duration": 0.20479999999997744, "failureMessages": ["TypeError: global.testDb.clear is not a function\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\tests\\integration\\api\\users.test.js:35:21\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1894:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1860:10)\n    at runHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1433:51)\n    at callSuiteHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1439:25)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at callSuiteHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1426:21)\n    at runTest (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1563:26)"], "location": {"line": 442, "column": 7}, "meta": {}}, {"ancestorTitles": ["Users API Integration Tests", "GET /api/users/riders"], "fullName": "Users API Integration Tests GET /api/users/riders should include rider statistics", "status": "failed", "title": "should include rider statistics", "duration": 0.22430000000008476, "failureMessages": ["TypeError: global.testDb.clear is not a function\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\tests\\integration\\api\\users.test.js:35:21\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1894:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1860:10)\n    at runHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1433:51)\n    at callSuiteHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1439:25)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at callSuiteHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1426:21)\n    at runTest (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1563:26)"], "location": {"line": 461, "column": 7}, "meta": {}}, {"ancestorTitles": ["Users API Integration Tests", "Erro<PERSON>"], "fullName": "Users API Integration Tests Error Handling should handle malformed JSON", "status": "failed", "title": "should handle malformed JSON", "duration": 0.20859999999993306, "failureMessages": ["TypeError: global.testDb.clear is not a function\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\tests\\integration\\api\\users.test.js:35:21\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1894:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1860:10)\n    at runHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1433:51)\n    at callSuiteHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1439:25)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at callSuiteHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1426:21)\n    at runTest (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1563:26)"], "location": {"line": 477, "column": 7}, "meta": {}}, {"ancestorTitles": ["Users API Integration Tests", "Erro<PERSON>"], "fullName": "Users API Integration Tests Error Handling should handle missing Content-Type header", "status": "failed", "title": "should handle missing Content-Type header", "duration": 0.21499999999991815, "failureMessages": ["TypeError: global.testDb.clear is not a function\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\tests\\integration\\api\\users.test.js:35:21\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1894:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1860:10)\n    at runHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1433:51)\n    at callSuiteHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1439:25)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at callSuiteHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1426:21)\n    at runTest (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1563:26)"], "location": {"line": 491, "column": 7}, "meta": {}}, {"ancestorTitles": ["Users API Integration Tests", "Erro<PERSON>"], "fullName": "Users API Integration Tests Error Handling should handle request timeout", "status": "failed", "title": "should handle request timeout", "duration": 0.21920000000000073, "failureMessages": ["TypeError: global.testDb.clear is not a function\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\bemyrider\\tests\\integration\\api\\users.test.js:35:21\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1894:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1860:10)\n    at runHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1433:51)\n    at callSuiteHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1439:25)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at callSuiteHook (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1426:21)\n    at runTest (file:///C:/Users/<USER>/Documents/augment-projects/bemyrider/node_modules/@vitest/runner/dist/chunk-hooks.js:1563:26)"], "location": {"line": 504, "column": 7}, "meta": {}}], "startTime": 1750155146820, "endTime": 1750155146895.2192, "status": "failed", "message": "", "name": "C:/Users/<USER>/Documents/augment-projects/bemyrider/tests/integration/api/users.test.js"}]}