/**
 * Merchant Dashboard Page
 * Main dashboard for merchants with overview, fleet stats, and quick actions
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { motion } from 'framer-motion'
import {
  HomeIcon,
  UserGroupIcon,
  TruckIcon,
  ChartBarIcon,
  MapPinIcon,
  ClockIcon,
  CurrencyEuroIcon,
  StarIcon,
  PlusIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline'

import Card, { CardHeader, CardTitle, CardContent } from '@components/ui/Card'
import Button from '@components/ui/Button'
import Header from '@components/layout/Header'

const mockData = {
  merchant: {
    name: 'Pizzeria Da Mario',
    type: 'restaurant',
    fleetSize: 8,
    activeRiders: 6,
    rating: 4.6
  },
  todayStats: {
    activeShifts: 4,
    completedShifts: 12,
    totalHours: 18,
    totalCost: 216.00
  },
  weekStats: {
    totalShifts: 89,
    totalHours: 134,
    totalCost: 1608.00,
    averageUtilization: 78
  },
  fleetOverview: {
    totalRiders: 8,
    activeRiders: 6,
    availableRiders: 3,
    busyRiders: 3,
    offlineRiders: 2,
    averageRating: 4.7,
    utilizationRate: 75
  },
  activeShifts: [
    {
      id: 1,
      riderName: 'Mario Rossi',
      startTime: '2024-12-22T14:00:00Z',
      endTime: '2024-12-22T16:00:00Z',
      status: 'active',
      location: 'Zona Centro',
      vehicleType: 'MOTO'
    },
    {
      id: 2,
      riderName: 'Luigi Verdi',
      startTime: '2024-12-22T15:00:00Z',
      endTime: '2024-12-22T17:00:00Z',
      status: 'active',
      location: 'Porta Garibaldi',
      vehicleType: 'BICI'
    }
  ],
  alerts: [
    {
      id: 1,
      type: 'warning',
      message: 'Patente di Mario Rossi in scadenza tra 15 giorni',
      priority: 'medium'
    },
    {
      id: 2,
      type: 'info',
      message: '3 nuovi rider disponibili nella tua zona',
      priority: 'low'
    }
  ],
  recentActivity: [
    {
      id: 1,
      type: 'shift_completed',
      message: 'Turno completato da Luigi Verdi',
      time: '30 min fa',
      amount: 25.00
    },
    {
      id: 2,
      type: 'rider_joined',
      message: 'Nuovo rider aggiunto alla flotta',
      time: '2 ore fa'
    },
    {
      id: 3,
      type: 'shift_started',
      message: 'Turno iniziato da Mario Rossi',
      time: '3 ore fa'
    }
  ]
}

const MerchantDashboard = () => {
  const [currentTime, setCurrentTime] = useState(new Date())

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 60000)

    return () => clearInterval(timer)
  }, [])

  const formatTime = (date) => {
    return date.toLocaleTimeString('it-IT', { 
      hour: '2-digit', 
      minute: '2-digit' 
    })
  }

  const formatDateTime = (dateString) => {
    return new Date(dateString).toLocaleTimeString('it-IT', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getGreeting = () => {
    const hour = currentTime.getHours()
    if (hour < 12) return 'Buongiorno'
    if (hour < 18) return 'Buon pomeriggio'
    return 'Buonasera'
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header 
        title={`${getGreeting()}, ${mockData.merchant.name}!`}
        subtitle={`Dashboard Flotta - ${currentTime.toLocaleDateString('it-IT', { 
          weekday: 'long', 
          year: 'numeric', 
          month: 'long', 
          day: 'numeric' 
        })} - ${formatTime(currentTime)}`}
        actions={
          <div className="flex space-x-3">
            <Button variant="outline" size="sm" leftIcon={<MapPinIcon className="w-4 h-4" />}>
              Trova Rider
            </Button>
            <Button variant="merchant" size="sm" leftIcon={<PlusIcon className="w-4 h-4" />}>
              Nuovo Turno
            </Button>
          </div>
        }
      />

      <div className="p-6 space-y-6">
        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
          >
            <Card className="metric-card card-hover">
              <div className="flex items-center justify-between">
                <div>
                  <p className="metric-label">Rider Attivi</p>
                  <p className="metric-value">{mockData.fleetOverview.activeRiders}</p>
                  <p className="metric-change metric-change-positive">
                    {mockData.fleetOverview.availableRiders} disponibili
                  </p>
                </div>
                <div className="p-3 bg-blue-100 rounded-full">
                  <UserGroupIcon className="w-6 h-6 text-blue-600" />
                </div>
              </div>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Card className="metric-card card-hover">
              <div className="flex items-center justify-between">
                <div>
                  <p className="metric-label">Turni Oggi</p>
                  <p className="metric-value">{mockData.todayStats.completedShifts}</p>
                  <p className="metric-change metric-change-positive">
                    +{mockData.todayStats.activeShifts} in corso
                  </p>
                </div>
                <div className="p-3 bg-green-100 rounded-full">
                  <ClockIcon className="w-6 h-6 text-green-600" />
                </div>
              </div>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card className="metric-card card-hover">
              <div className="flex items-center justify-between">
                <div>
                  <p className="metric-label">Costo Oggi</p>
                  <p className="metric-value">€{mockData.todayStats.totalCost.toFixed(2)}</p>
                  <p className="metric-change metric-change-positive">
                    €{mockData.weekStats.totalCost.toFixed(2)} questa settimana
                  </p>
                </div>
                <div className="p-3 bg-yellow-100 rounded-full">
                  <CurrencyEuroIcon className="w-6 h-6 text-yellow-600" />
                </div>
              </div>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <Card className="metric-card card-hover">
              <div className="flex items-center justify-between">
                <div>
                  <p className="metric-label">Utilizzo Flotta</p>
                  <p className="metric-value">{mockData.fleetOverview.utilizationRate}%</p>
                  <p className="metric-change metric-change-positive">
                    Rating medio {mockData.fleetOverview.averageRating}
                  </p>
                </div>
                <div className="p-3 bg-purple-100 rounded-full">
                  <ChartBarIcon className="w-6 h-6 text-purple-600" />
                </div>
              </div>
            </Card>
          </motion.div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Active Shifts */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.5 }}
            className="lg:col-span-2"
          >
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center space-x-2">
                    <TruckIcon className="w-5 h-5 text-blue-600" />
                    <span>Turni Attivi</span>
                  </CardTitle>
                  <Link to="/merchant/fleet">
                    <Button variant="outline" size="sm">
                      Gestisci Flotta
                    </Button>
                  </Link>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockData.activeShifts.map((shift, index) => (
                    <motion.div
                      key={shift.id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.6 + index * 0.1 }}
                      className="flex items-center justify-between p-4 bg-green-50 border border-green-200 rounded-lg"
                    >
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <div className="flex-shrink-0">
                            <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                              <UserGroupIcon className="w-5 h-5 text-green-600" />
                            </div>
                          </div>
                          <div className="flex-1">
                            <h4 className="font-medium text-gray-900">{shift.riderName}</h4>
                            <div className="flex items-center space-x-4 text-sm text-gray-600">
                              <span className="flex items-center space-x-1">
                                <ClockIcon className="w-4 h-4" />
                                <span>{formatDateTime(shift.startTime)} - {formatDateTime(shift.endTime)}</span>
                              </span>
                              <span className="flex items-center space-x-1">
                                <MapPinIcon className="w-4 h-4" />
                                <span>{shift.location}</span>
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <span className="status-badge status-success">
                          {shift.vehicleType}
                        </span>
                        <Button variant="outline" size="sm">
                          Traccia
                        </Button>
                      </div>
                    </motion.div>
                  ))}
                </div>
                
                {mockData.activeShifts.length === 0 && (
                  <div className="text-center py-8">
                    <TruckIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Nessun turno attivo</h3>
                    <p className="text-gray-600 mb-4">Programma nuovi turni per la tua flotta</p>
                    <Link to="/merchant/find-riders">
                      <Button variant="merchant">
                        Trova Rider
                      </Button>
                    </Link>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>

          {/* Alerts & Activity */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.7 }}
            className="space-y-6"
          >
            {/* Alerts */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <ExclamationTriangleIcon className="w-5 h-5 text-orange-600" />
                  <span>Avvisi</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {mockData.alerts.map((alert, index) => (
                    <motion.div
                      key={alert.id}
                      initial={{ opacity: 0, x: 10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.8 + index * 0.1 }}
                      className={`p-3 rounded-lg border ${
                        alert.type === 'warning' ? 'bg-orange-50 border-orange-200' : 'bg-blue-50 border-blue-200'
                      }`}
                    >
                      <p className={`text-sm font-medium ${
                        alert.type === 'warning' ? 'text-orange-800' : 'text-blue-800'
                      }`}>
                        {alert.message}
                      </p>
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <CheckCircleIcon className="w-5 h-5 text-green-600" />
                  <span>Attività Recente</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockData.recentActivity.map((activity, index) => (
                    <motion.div
                      key={activity.id}
                      initial={{ opacity: 0, x: 10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.9 + index * 0.1 }}
                      className="flex items-start space-x-3"
                    >
                      <div className="flex-shrink-0">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                          activity.type === 'shift_completed' ? 'bg-green-100' :
                          activity.type === 'rider_joined' ? 'bg-blue-100' : 'bg-yellow-100'
                        }`}>
                          {activity.type === 'shift_completed' && <CheckCircleIcon className="w-4 h-4 text-green-600" />}
                          {activity.type === 'rider_joined' && <UserGroupIcon className="w-4 h-4 text-blue-600" />}
                          {activity.type === 'shift_started' && <ClockIcon className="w-4 h-4 text-yellow-600" />}
                        </div>
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">{activity.message}</p>
                        <div className="flex items-center justify-between">
                          <p className="text-xs text-gray-500">{activity.time}</p>
                          {activity.amount && (
                            <p className="text-sm font-medium text-green-600">
                              €{activity.amount.toFixed(2)}
                            </p>
                          )}
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.0 }}
        >
          <Card>
            <CardHeader>
              <CardTitle>Azioni Rapide</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Link to="/merchant/find-riders" className="block">
                  <div className="p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-all cursor-pointer">
                    <div className="flex items-center space-x-3">
                      <MapPinIcon className="w-8 h-8 text-blue-600" />
                      <div>
                        <h3 className="font-medium text-gray-900">Trova Rider</h3>
                        <p className="text-sm text-gray-600">Cerca rider disponibili</p>
                      </div>
                    </div>
                  </div>
                </Link>

                <Link to="/merchant/fleet" className="block">
                  <div className="p-4 border border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-all cursor-pointer">
                    <div className="flex items-center space-x-3">
                      <TruckIcon className="w-8 h-8 text-green-600" />
                      <div>
                        <h3 className="font-medium text-gray-900">Gestisci Flotta</h3>
                        <p className="text-sm text-gray-600">Amministra i tuoi rider</p>
                      </div>
                    </div>
                  </div>
                </Link>

                <Link to="/merchant/analytics" className="block">
                  <div className="p-4 border border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition-all cursor-pointer">
                    <div className="flex items-center space-x-3">
                      <ChartBarIcon className="w-8 h-8 text-purple-600" />
                      <div>
                        <h3 className="font-medium text-gray-900">Analytics</h3>
                        <p className="text-sm text-gray-600">Visualizza statistiche</p>
                      </div>
                    </div>
                  </div>
                </Link>

                <Link to="/merchant/bookings" className="block">
                  <div className="p-4 border border-gray-200 rounded-lg hover:border-orange-300 hover:bg-orange-50 transition-all cursor-pointer">
                    <div className="flex items-center space-x-3">
                      <ClockIcon className="w-8 h-8 text-orange-600" />
                      <div>
                        <h3 className="font-medium text-gray-900">Prenotazioni</h3>
                        <p className="text-sm text-gray-600">Gestisci i turni</p>
                      </div>
                    </div>
                  </div>
                </Link>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}

export default MerchantDashboard
