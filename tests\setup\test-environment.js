/**
 * Test Environment Setup
 * Configures the test environment and mocks
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import { vi } from 'vitest'

// Mock external modules
vi.mock('@clerk/clerk-sdk-node', () => ({
  createClerkClient: () => global.mockClerk || {
    verifyToken: vi.fn().mockRejectedValue(new Error('Mock not initialized')),
    users: {
      getUser: vi.fn().mockRejectedValue(new Error('Mock not initialized'))
    }
  }
}))

vi.mock('@supabase/supabase-js', () => ({
  createClient: () => global.mockSupabase || {
    from: vi.fn().mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({ data: null, error: null })
        })
      })
    })
  }
}))

vi.mock('stripe', () => ({
  default: class MockStripe {
    constructor() {
      return global.mockStripe || {
        paymentIntents: {
          create: vi.fn(),
          confirm: vi.fn()
        }
      }
    }
  }
}))

// Mock node-fetch for API testing
vi.mock('node-fetch', async () => {
  const actual = await vi.importActual('node-fetch')
  return {
    ...actual,
    default: vi.fn()
  }
})

// Mock file system operations
vi.mock('fs', async () => {
  const actual = await vi.importActual('fs')
  return {
    ...actual,
    readFileSync: vi.fn(),
    writeFileSync: vi.fn(),
    existsSync: vi.fn().mockReturnValue(true)
  }
})

// Mock environment variables
process.env = {
  ...process.env,
  NODE_ENV: 'test',
  VITE_SUPABASE_URL: 'https://test-project.supabase.co',
  VITE_SUPABASE_ANON_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test',
  CLERK_SECRET_KEY: 'sk_test_mock_key_for_testing',
  VITE_CLERK_PUBLISHABLE_KEY: 'pk_test_mock_key_for_testing',
  PORT: '5001',
  FRONTEND_URL: 'http://localhost:3000'
}

// Global test utilities
global.vi = vi

// Mock reset function
global.mockReset = () => {
  vi.clearAllMocks()
  vi.resetAllMocks()
}

// Cache clear function
global.clearCache = () => {
  // Clear any application caches
  if (global.appCache) {
    global.appCache.clear()
  }
}

// Console override for cleaner test output
const originalConsole = { ...console }

global.setupTestConsole = () => {
  console.log = vi.fn()
  console.info = vi.fn()
  console.warn = vi.fn()
  console.error = vi.fn()
}

global.restoreTestConsole = () => {
  console.log = originalConsole.log
  console.info = originalConsole.info
  console.warn = originalConsole.warn
  console.error = originalConsole.error
}

// Test database helpers
global.testDb = {
  // Mock database operations for testing
  users: new Map(),
  bookings: new Map(),
  reviews: new Map(),
  payments: new Map(),
  
  // Helper methods
  clear() {
    this.users.clear()
    this.bookings.clear()
    this.reviews.clear()
    this.payments.clear()
  },
  
  addUser(user) {
    this.users.set(user.id, user)
    return user
  },
  
  getUser(id) {
    return this.users.get(id)
  },
  
  addBooking(booking) {
    this.bookings.set(booking.id, booking)
    return booking
  },
  
  getBooking(id) {
    return this.bookings.get(id)
  }
}

// HTTP client for testing
global.testClient = {
  baseURL: 'http://localhost:5001/api',
  
  async request(method, path, options = {}) {
    const url = `${this.baseURL}${path}`
    const config = {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    }
    
    if (config.body && typeof config.body === 'object') {
      config.body = JSON.stringify(config.body)
    }
    
    // Mock fetch for testing
    const mockFetch = await import('node-fetch')
    return mockFetch.default(url, config)
  },
  
  get(path, options) {
    return this.request('GET', path, options)
  },
  
  post(path, body, options) {
    return this.request('POST', path, { ...options, body })
  },
  
  put(path, body, options) {
    return this.request('PUT', path, { ...options, body })
  },
  
  delete(path, options) {
    return this.request('DELETE', path, options)
  }
}

// Test server helpers
global.testServer = {
  isRunning: false,
  instance: null,
  
  async start() {
    if (this.isRunning) return
    
    // Start test server
    const { startTestServer } = await import('../../start-test-server.js')
    this.instance = await startTestServer()
    this.isRunning = true
    
    // Wait for server to be ready
    await global.waitFor(1000)
  },
  
  async stop() {
    if (!this.isRunning) return
    
    if (this.instance && this.instance.close) {
      this.instance.close()
    }
    this.isRunning = false
  }
}

// Performance testing helpers
global.performance = {
  measureTime: async (fn) => {
    const start = Date.now()
    await fn()
    return Date.now() - start
  },
  
  measureMemory: () => {
    if (process.memoryUsage) {
      return process.memoryUsage()
    }
    return null
  }
}

// Error testing helpers
global.expectError = async (fn, expectedError) => {
  try {
    await fn()
    throw new Error('Expected function to throw an error')
  } catch (error) {
    if (expectedError) {
      expect(error.message).toContain(expectedError)
    }
    return error
  }
}

// Async testing helpers
global.expectAsync = {
  toResolve: async (promise) => {
    try {
      const result = await promise
      return result
    } catch (error) {
      throw new Error(`Expected promise to resolve, but it rejected with: ${error.message}`)
    }
  },
  
  toReject: async (promise) => {
    try {
      await promise
      throw new Error('Expected promise to reject, but it resolved')
    } catch (error) {
      return error
    }
  }
}

// Data validation helpers
global.validateSchema = (data, schema) => {
  const errors = []
  
  for (const [key, rules] of Object.entries(schema)) {
    const value = data[key]
    
    if (rules.required && (value === undefined || value === null)) {
      errors.push(`${key} is required`)
    }
    
    if (value !== undefined && rules.type) {
      const actualType = typeof value
      if (actualType !== rules.type) {
        errors.push(`${key} should be ${rules.type}, got ${actualType}`)
      }
    }
    
    if (value !== undefined && rules.pattern && !rules.pattern.test(value)) {
      errors.push(`${key} does not match required pattern`)
    }
  }
  
  return errors
}

// Test data generators
global.generators = {
  user: (type = 'RIDER') => ({
    id: global.generateTestData.uuid(),
    clerk_id: global.generateTestData.uuid(),
    email: global.generateTestData.email(),
    type,
    first_name: 'Test',
    last_name: 'User',
    display_name: 'Test User',
    phone: global.generateTestData.phone(),
    profile_complete: true,
    email_verified: true,
    account_status: 'ACTIVE',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }),
  
  booking: () => ({
    id: global.generateTestData.uuid(),
    booking_number: global.generateTestData.bookingNumber(),
    client_id: global.generateTestData.uuid(),
    rider_id: global.generateTestData.uuid(),
    scheduled_date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
    duration: 120,
    pickup_address: 'Test Pickup Address',
    delivery_address: 'Test Delivery Address',
    base_rate: 25.00,
    subtotal: 50.00,
    platform_fee: 7.50,
    total_amount: 57.50,
    status: 'PENDING',
    payment_status: 'PENDING',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  })
}
