# bemyrider - Marketplace per Rider

bemyrider è una piattaforma marketplace che connette esercenti locali con rider professionali per servizi di consegna a tariffa oraria.

## 🚀 Funzionalità Principali

### Per gli Esercenti
- **Ricerca Rider**: Trova rider disponibili per tipo di veicolo, tariffa e zona
- **Prenotazione Oraria**: Prenota rider per 1-2 ore quando necessario
- **Pagamenti Sicuri**: Pagamenti automatici tramite Stripe Connect
- **Gestione Prenotazioni**: Monitora lo stato delle prenotazioni in tempo reale
- **Sistema di Recensioni**: Valuta i rider dopo ogni servizio

### Per i Rider
- **Gestione Disponibilità**: Imposta giorni e orari di lavoro
- **Tariffa Personalizzata**: Definisci la tua tariffa oraria
- **Pagamenti Automatici**: Ricevi pagamenti direttamente sul tuo conto Stripe
- **Profilo Professionale**: Crea un profilo dettagliato con veicolo e descrizione
- **Dashboard Guadagni**: Monitora i tuoi guadagni e statistiche

### Sistema di Pagamento
- **Stripe Connect**: Integrazione con Destination Charges
- **Commissione 15%**: Commissione automatica della piattaforma
- **Pagamenti Tracciabili**: Tutti i pagamenti sono tracciati e documentati
- **Ricevute Automatiche**: Generazione automatica di ricevute PDF

## 🛠️ Stack Tecnologico

### Frontend
- **React 18** con TypeScript
- **Vite** per il build tool
- **Tailwind CSS** per lo styling
- **React Router** per la navigazione
- **React Hook Form** per la gestione dei form
- **Axios** per le chiamate API
- **Date-fns** per la gestione delle date

### Backend
- **Node.js** con Express
- **PostgreSQL** tramite Supabase
- **Clerk** per l'autenticazione
- **Stripe Connect** per i pagamenti
- **Helmet** per la sicurezza HTTP
- **Rate Limiting** e validazione input

## 📦 Installazione

### Prerequisiti
- Node.js 18+
- Account Supabase (database)
- Account Clerk (autenticazione)
- Account Stripe (per i pagamenti - opzionale per MVP)

### 1. Clona il repository
```bash
git clone <repository-url>
cd bemyrider
```

### 2. Installa le dipendenze
```bash
npm install
```

### 3. Configura le variabili d'ambiente
Copia il file `.env.example` e rinominalo in `.env`:

```bash
cp .env.example .env
```

**IMPORTANTE**: Compila TUTTE le variabili richieste nel file `.env`. Il server non si avvierà se mancano configurazioni critiche.

Variabili **OBBLIGATORIE**:
```env
# Supabase (Database)
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Clerk (Autenticazione)
CLERK_SECRET_KEY=sk_test_your_clerk_secret_key
VITE_CLERK_PUBLISHABLE_KEY=pk_test_your_clerk_publishable_key
```

Variabili **OPZIONALI**:
```env
# Server
PORT=5000
NODE_ENV=development
FRONTEND_URL=http://localhost:3000

# Stripe (Pagamenti - opzionale per MVP)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
```

### 4. Configura il database
Il database viene configurato automaticamente tramite Supabase. Esegui gli script SQL forniti nella dashboard di Supabase:

1. Accedi alla dashboard di Supabase
2. Vai su SQL Editor
3. Esegui il contenuto di `database_setup.sql`

```bash
# Verifica la connessione al database
npm run server
```

### 5. Avvia l'applicazione

#### Sviluppo
```bash
# Frontend (porta 3000)
npm run dev

# Backend (porta 5000) - in un altro terminale
npm run server
```

#### Produzione
```bash
# Build del frontend
npm run build

# Avvia il server
npm start
```

## 🔧 Configurazione Stripe

### 1. Crea un account Stripe
- Vai su [stripe.com](https://stripe.com) e crea un account
- Attiva Stripe Connect nel dashboard

### 2. Configura i webhook
Nel dashboard Stripe, configura un webhook endpoint:
- URL: `https://your-domain.com/api/payments/webhook`
- Eventi da ascoltare:
  - `payment_intent.succeeded`
  - `payment_intent.payment_failed`
  - `account.updated`

### 3. Ottieni le chiavi API
- Chiave pubblica: `pk_test_...` (per il frontend)
- Chiave segreta: `sk_test_...` (per il backend)
- Webhook secret: `whsec_...` (per verificare i webhook)

## 📱 Utilizzo

### Registrazione
1. Vai su `http://localhost:3000`
2. Clicca su "Registrati"
3. Scegli il tipo di account (Esercente o Rider)
4. Compila i dati richiesti

### Per i Rider
1. Completa il profilo con tariffa oraria e tipo di veicolo
2. Configura l'account Stripe Connect per ricevere pagamenti
3. Imposta la tua disponibilità (giorni e orari)
4. Attendi le prenotazioni dagli esercenti

### Per gli Esercenti
1. Cerca rider disponibili per tipo di veicolo
2. Visualizza profili, tariffe e recensioni
3. Prenota un rider per 1-2 ore
4. Paga tramite carta di credito
5. Monitora lo stato della prenotazione

## 🗂️ Struttura del Progetto

```
bemyrider/
├── src/                    # Frontend React
│   ├── components/         # Componenti riutilizzabili
│   ├── contexts/          # Context providers (Auth, Stripe)
│   ├── pages/             # Pagine dell'applicazione
│   └── utils/             # Utility functions
├── server/                # Backend Express
│   ├── routes/            # Route API
│   ├── middleware/        # Middleware personalizzati
│   └── utils/             # Utility backend
├── prisma/                # Schema database e migrazioni
└── public/                # File statici
```

## 🔐 Sicurezza

- **Autenticazione Clerk**: Token JWT sicuri per l'accesso alle API
- **Validazione Environment**: Validazione rigorosa delle variabili d'ambiente
- **Validazione Input**: Validazione lato client e server con middleware dedicati
- **CORS Configurato**: Configurazione CORS restrittiva per origini specifiche
- **Helmet Security**: Headers di sicurezza HTTP automatici
- **Rate Limiting**: Protezione contro attacchi DDoS e spam
- **Nessuna Credenziale Hardcoded**: Tutte le credenziali tramite variabili d'ambiente

## 📊 Database Schema

Il database include le seguenti entità principali:
- **Users**: Utenti (esercenti e rider)
- **Bookings**: Prenotazioni
- **Payments**: Pagamenti
- **Reviews**: Recensioni
- **Availability**: Disponibilità dei rider

## 🚀 Deploy

### Frontend (Vercel)
```bash
npm run build
# Deploy su Vercel o altro provider
```

### Backend (Heroku/Railway)
```bash
# Configura le variabili d'ambiente
# Deploy su Heroku, Railway o altro provider
```

### Database (PostgreSQL)
- Utilizza un servizio gestito come Supabase, Railway o Heroku Postgres

## 🤝 Contribuire

1. Fork del repository
2. Crea un branch per la feature (`git checkout -b feature/AmazingFeature`)
3. Commit delle modifiche (`git commit -m 'Add some AmazingFeature'`)
4. Push del branch (`git push origin feature/AmazingFeature`)
5. Apri una Pull Request

## 📄 Licenza

Questo progetto è sotto licenza MIT. Vedi il file `LICENSE` per i dettagli.

## 📞 Supporto

Per supporto o domande:
- Email: <EMAIL>
- GitHub Issues: [Apri un issue](https://github.com/your-repo/bemyrider/issues)

## 🎯 Roadmap

- [ ] App mobile (React Native)
- [ ] Sistema di chat in tempo reale
- [ ] Tracking GPS delle consegne
- [ ] Sistema di fatturazione automatica
- [ ] Dashboard analytics avanzata
- [ ] Sistema di notifiche push
- [ ] Integrazione con servizi di mappe
