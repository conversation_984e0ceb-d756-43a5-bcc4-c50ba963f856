/**
 * Security Middleware
 * Enterprise-grade security implementation for BeMyRider
 * Author: Senior Software Engineer
 * Version: 1.0.0
 */

import helmet from 'helmet'
import rateLimit from 'express-rate-limit'
import slowDown from 'express-slow-down'
import { body, validationResult, param, query } from 'express-validator'
import DOMPurify from 'isomorphic-dompurify'
import crypto from 'crypto'
import { getEnv } from '../utils/env-validator.js'

// ============================================================================
// SECURITY HEADERS
// ============================================================================

/**
 * Configure security headers with Helmet
 */
export const securityHeaders = helmet({
  // Content Security Policy
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:", "blob:"],
      scriptSrc: ["'self'", "'unsafe-inline'", "https://js.stripe.com"],
      connectSrc: ["'self'", "https://api.stripe.com", "https://*.supabase.co"],
      frameSrc: ["'self'", "https://js.stripe.com"],
      objectSrc: ["'none'"],
      upgradeInsecureRequests: [],
    },
  },
  
  // Cross-Origin Embedder Policy
  crossOriginEmbedderPolicy: false,
  
  // HTTP Strict Transport Security
  hsts: {
    maxAge: 31536000, // 1 year
    includeSubDomains: true,
    preload: true
  },
  
  // X-Frame-Options
  frameguard: {
    action: 'deny'
  },
  
  // X-Content-Type-Options
  noSniff: true,
  
  // Referrer Policy
  referrerPolicy: {
    policy: 'strict-origin-when-cross-origin'
  },
  
  // Permissions Policy
  permissionsPolicy: {
    features: {
      geolocation: ['self'],
      camera: ['none'],
      microphone: ['none'],
      payment: ['self', 'https://js.stripe.com']
    }
  }
})

// ============================================================================
// RATE LIMITING
// ============================================================================

/**
 * General API rate limiting
 */
export const generalRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // limit each IP to 1000 requests per windowMs
  message: {
    success: false,
    message: 'Troppi tentativi. Riprova tra 15 minuti.',
    retryAfter: 15 * 60
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    res.status(429).json({
      success: false,
      message: 'Rate limit exceeded',
      retryAfter: Math.round(req.rateLimit.resetTime / 1000)
    })
  }
})

/**
 * Authentication rate limiting
 */
export const authRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // limit each IP to 5 auth requests per windowMs
  message: {
    success: false,
    message: 'Troppi tentativi di autenticazione. Riprova tra 15 minuti.',
    retryAfter: 15 * 60
  },
  skipSuccessfulRequests: true
})

/**
 * Password reset rate limiting
 */
export const passwordResetRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // limit each IP to 3 password reset requests per hour
  message: {
    success: false,
    message: 'Troppi tentativi di reset password. Riprova tra 1 ora.',
    retryAfter: 60 * 60
  }
})

/**
 * API creation rate limiting
 */
export const createRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 10, // limit each IP to 10 creation requests per minute
  message: {
    success: false,
    message: 'Troppi tentativi di creazione. Riprova tra 1 minuto.',
    retryAfter: 60
  }
})

/**
 * Slow down middleware for suspicious activity
 */
export const speedLimiter = slowDown({
  windowMs: 15 * 60 * 1000, // 15 minutes
  delayAfter: 100, // allow 100 requests per 15 minutes at full speed
  delayMs: 500, // slow down subsequent requests by 500ms per request
  maxDelayMs: 20000, // maximum delay of 20 seconds
})

// ============================================================================
// INPUT VALIDATION & SANITIZATION
// ============================================================================

/**
 * Sanitize HTML input to prevent XSS
 */
export const sanitizeInput = (req, res, next) => {
  const sanitizeValue = (value) => {
    if (typeof value === 'string') {
      return DOMPurify.sanitize(value, { 
        ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'p', 'br'],
        ALLOWED_ATTR: []
      })
    }
    if (typeof value === 'object' && value !== null) {
      const sanitized = {}
      for (const [key, val] of Object.entries(value)) {
        sanitized[key] = sanitizeValue(val)
      }
      return sanitized
    }
    return value
  }

  if (req.body) {
    req.body = sanitizeValue(req.body)
  }
  if (req.query) {
    req.query = sanitizeValue(req.query)
  }
  if (req.params) {
    req.params = sanitizeValue(req.params)
  }

  next()
}

/**
 * Validation error handler
 */
export const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req)
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Errori di validazione',
      errors: errors.array().map(error => ({
        field: error.path,
        message: error.msg,
        value: error.value
      }))
    })
  }
  next()
}

/**
 * Common validation rules
 */
export const validationRules = {
  // User validation
  email: body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Email non valida'),
    
  password: body('password')
    .isLength({ min: 8, max: 128 })
    .withMessage('Password deve essere tra 8 e 128 caratteri')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password deve contenere almeno: 1 minuscola, 1 maiuscola, 1 numero, 1 carattere speciale'),
    
  phone: body('phone')
    .isMobilePhone('it-IT')
    .withMessage('Numero di telefono non valido'),
    
  name: body(['firstName', 'lastName', 'displayName'])
    .isLength({ min: 1, max: 50 })
    .withMessage('Nome deve essere tra 1 e 50 caratteri')
    .matches(/^[a-zA-ZÀ-ÿ\s'-]+$/)
    .withMessage('Nome contiene caratteri non validi'),
    
  // Booking validation
  bookingDate: body('scheduledDate')
    .isISO8601()
    .withMessage('Data non valida')
    .custom((value) => {
      const date = new Date(value)
      const now = new Date()
      if (date <= now) {
        throw new Error('La data deve essere futura')
      }
      return true
    }),
    
  duration: body('duration')
    .isInt({ min: 1, max: 2 })
    .withMessage('Durata deve essere tra 1 e 2 ore'),
    
  amount: body(['amount', 'totalAmount'])
    .isFloat({ min: 0.01, max: 10000 })
    .withMessage('Importo deve essere tra 0.01 e 10000'),

  hourlyRate: body('hourlyRate')
    .isFloat({ min: 5.00, max: 12.50 })
    .withMessage('Tariffa oraria deve essere tra 5.00 e 12.50 euro'),
    
  // ID validation
  uuid: param('id')
    .isUUID()
    .withMessage('ID non valido'),
    
  clerkId: param('clerkId')
    .isLength({ min: 1, max: 100 })
    .withMessage('Clerk ID non valido'),
    
  // Query validation
  pagination: [
    query('page')
      .optional()
      .isInt({ min: 1, max: 1000 })
      .withMessage('Pagina deve essere tra 1 e 1000'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limite deve essere tra 1 e 100')
  ],
  
  // Location validation
  coordinates: [
    body('latitude')
      .isFloat({ min: -90, max: 90 })
      .withMessage('Latitudine non valida'),
    body('longitude')
      .isFloat({ min: -180, max: 180 })
      .withMessage('Longitudine non valida')
  ]
}

// ============================================================================
// CSRF PROTECTION
// ============================================================================

/**
 * Generate CSRF token
 */
export const generateCSRFToken = () => {
  return crypto.randomBytes(32).toString('hex')
}

/**
 * CSRF protection middleware
 */
export const csrfProtection = (req, res, next) => {
  // Skip CSRF for GET requests and API endpoints with proper auth
  if (req.method === 'GET' || req.path.startsWith('/api/')) {
    return next()
  }

  const token = req.headers['x-csrf-token'] || req.body._csrf
  const sessionToken = req.session?.csrfToken

  if (!token || !sessionToken || token !== sessionToken) {
    return res.status(403).json({
      success: false,
      message: 'Token CSRF non valido'
    })
  }

  next()
}

// ============================================================================
// REQUEST SIZE LIMITS
// ============================================================================

/**
 * Request size validation
 */
export const requestSizeLimit = (req, res, next) => {
  const contentLength = parseInt(req.headers['content-length'] || '0')
  const maxSize = 10 * 1024 * 1024 // 10MB

  if (contentLength > maxSize) {
    return res.status(413).json({
      success: false,
      message: 'Request troppo grande. Massimo 10MB.'
    })
  }

  next()
}

// ============================================================================
// SECURITY LOGGING
// ============================================================================

/**
 * Security event logger
 */
export const logSecurityEvent = (eventType, details, req) => {
  const securityEvent = {
    timestamp: new Date().toISOString(),
    type: eventType,
    ip: req.ip || req.connection.remoteAddress,
    userAgent: req.headers['user-agent'],
    url: req.originalUrl,
    method: req.method,
    userId: req.auth?.userId || null,
    sessionId: req.auth?.sessionId || null,
    details: details
  }

  // Log to security log file
  console.log('SECURITY_EVENT:', JSON.stringify(securityEvent))
  
  // In production, send to security monitoring system
  if (process.env.NODE_ENV === 'production') {
    // Send to SIEM, Splunk, or other security monitoring
  }
}

/**
 * Security monitoring middleware
 */
export const securityMonitoring = (req, res, next) => {
  // Monitor for suspicious patterns
  const suspiciousPatterns = [
    /(\<script\>|\<\/script\>)/i,
    /(union|select|insert|delete|drop|create|alter)/i,
    /(\.\.|\/etc\/passwd|\/bin\/sh)/i,
    /(eval\(|javascript:|vbscript:)/i
  ]

  const requestData = JSON.stringify({
    body: req.body,
    query: req.query,
    params: req.params
  })

  for (const pattern of suspiciousPatterns) {
    if (pattern.test(requestData)) {
      logSecurityEvent('SUSPICIOUS_REQUEST', {
        pattern: pattern.toString(),
        data: requestData
      }, req)
      
      return res.status(400).json({
        success: false,
        message: 'Request contiene contenuto sospetto'
      })
    }
  }

  next()
}

// ============================================================================
// EXPORTS
// ============================================================================

export default {
  securityHeaders,
  generalRateLimit,
  authRateLimit,
  passwordResetRateLimit,
  createRateLimit,
  speedLimiter,
  sanitizeInput,
  handleValidationErrors,
  validationRules,
  csrfProtection,
  requestSizeLimit,
  logSecurityEvent,
  securityMonitoring
}
